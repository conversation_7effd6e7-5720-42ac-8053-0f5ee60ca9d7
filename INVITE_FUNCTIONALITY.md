# User Invite Functionality

## Overview

The user invite functionality allows administrators to send invitations to new users via email. When a user receives an invitation, they can click the link to set up their account with the specified role.

## Implementation Details

### Frontend Components

#### 1. Manage Users Page (`src/app/(dashboard)/admin-settings/manage-users/page.tsx`)

**Key Features:**
- Modal form for inviting users
- Email validation
- Role selection with proper mapping
- Loading states and confirmation dialogs
- Toast notifications for success/error feedback
- Form field validation and disabled states during submission

**Role Mapping:**
```typescript
const roleMapping = {
  'Stores Manager': 'c_stores_manager',
  'Accountant': 'c_accountant',
  'Procurement Officer': 'c_procurement_officer',
  'Approving Manager': 'c_approving_manager',
  'Requesting User': 'c_requesting_user'
};
```

**Role-Based Access Control:**
- **Only `c_admin` users** can access the invite functionality
- **Non-admin users** see a disabled invite button with permission message
- **Access denied modal** shown if non-admin tries to access invite form

**Available Roles for c_admin to Invite:**
- `c_stores_manager` - Stores Manager
- `c_accountant` - Accountant
- `c_procurement_officer` - Procurement Officer
- `c_approving_manager` - Approving Manager
- `c_requesting_user` - Requesting User

**Security Restrictions:**
- `c_admin` cannot invite other `c_admin` users (prevents privilege escalation)
- `s_admin` and supplier roles are handled through separate workflows
- Role validation enforced on both frontend and backend

#### 2. API Proxy Routes

**Auth Invite Route** (`src/app/api/proxy/auth/invite/route.ts`)
- **Purpose:** Proxies company user invitations
- **Endpoint:** `POST /api/proxy/auth/invite`
- **Backend:** `{{BASE_URL}}/auth/invite`

**Supplier Invite Route** (`src/app/api/proxy/supplier/invite/route.ts`)
- **Purpose:** Proxies supplier user invitations
- **Endpoint:** `POST /api/proxy/supplier/invite`
- **Backend:** `{{BASE_URL}}/supplier/invite`

**Common Features:**
- Handles authentication by forwarding Bearer tokens
- Provides detailed error handling and logging
- Avoids CORS issues in development

### Backend Integration

#### Company User Invitations
**API Endpoint:** `{{BASE_URL}}/auth/invite`

**Request Format:**
```json
{
  "email": "<EMAIL>",
  "role_name": "c_stores_manager"
}
```

#### Supplier User Invitations
**API Endpoint:** `{{BASE_URL}}/supplier/invite`

**Request Format:**
```json
{
  "email": "<EMAIL>",
  "role_name": "s_user"
}
```

**Available Roles:**
- `c_admin` - System Administrator
- `c_stores_manager` - Stores Manager
- `c_accountant` - Accountant
- `c_procurement_officer` - Procurement Officer
- `c_approving_manager` - Approving Manager
- `c_requesting_user` - Requesting User
- `s_user` - Supplier User (sent when s_admin is selected)

**Headers Required:**
```
Authorization: Bearer {jwt_token}
Content-Type: application/json
Accept: application/json
```

## Usage Instructions

### For Administrators

1. **Navigate to Manage Users:**
   - Go to Admin Settings → Manage Users
   - Click the "Invite user" button

2. **Fill Out the Invitation Form:**
   - Enter the user's email address
   - Select their business role from the dropdown
   - Optionally fill in other fields (first name, last name, etc.)

3. **Send the Invitation:**
   - Click "Send Invite"
   - Confirm the invitation in the dialog
   - Wait for the success/error notification

**Note:** When selecting "Supplier Admin", the system automatically:
- Routes to the supplier invitation endpoint
- Sends the invitation with `s_user` role
- Uses supplier-specific email templates

4. **Monitor Results:**
   - Success: User receives an email with setup instructions
   - Error: Check the error message and retry if needed

### For Invited Users

1. **Check Email:**
   - Look for an invitation email from the system
   - Click the invitation link

2. **Set Up Account:**
   - Follow the instructions to create a password
   - Complete any required profile information
   - Confirm account setup

3. **Access System:**
   - Log in with the email and password
   - Access features based on assigned role

## Error Handling

### Common Error Scenarios

| Status Code | Error Message | Solution |
|-------------|---------------|----------|
| 401 | Authentication failed | User needs to log in again |
| 403 | No permission to send invitations | Check user role permissions |
| 409 | User already exists or has pending invitation | Use different email or check existing users |
| 422 | Invalid email address or role | Verify email format and role selection |
| 500 | Internal server error | Contact system administrator |

### Client-Side Validation

- **Email Format:** Validates proper email format using regex
- **Required Fields:** Ensures email and role are provided
- **Confirmation:** Shows confirmation dialog before sending
- **Network Errors:** Handles connection issues gracefully

## Testing

### Test Page

A test page is available at `/test-invite` for debugging and testing the invite functionality:

**Features:**
- Direct API testing interface
- Response inspection
- Different role testing
- Error scenario testing

### Manual Testing Steps

1. **Valid Invitation:**
   ```
   Email: <EMAIL>
   Role: c_requesting_user
   Expected: Success message and email sent
   ```

2. **Invalid Email:**
   ```
   Email: invalid-email
   Role: c_requesting_user
   Expected: Email validation error
   ```

3. **Missing Authentication:**
   ```
   Clear localStorage token
   Expected: Authentication error
   ```

4. **Duplicate Invitation:**
   ```
   Send same email twice
   Expected: Conflict error (409)
   ```

## Security Considerations

### Authentication
- All requests require valid JWT token
- Token is automatically included from localStorage
- Expired tokens are handled gracefully

### Authorization
- Only users with appropriate permissions can send invites
- Role assignments are validated on the backend
- Email addresses are validated before processing

### Data Protection
- Email addresses are validated and sanitized
- No sensitive data is logged in client-side console
- API responses don't expose internal system details

## Troubleshooting

### Common Issues

1. **"No authentication token found"**
   - Solution: User needs to log in again
   - Check: localStorage contains 'access_token'

2. **"Network error"**
   - Solution: Check internet connection
   - Verify: API endpoint is accessible

3. **"You do not have permission"**
   - Solution: Contact administrator for role update
   - Check: User has invite permissions

4. **"User already exists"**
   - Solution: Check existing users list
   - Alternative: Use different email address

### Debug Information

Enable browser console to see detailed logs:
- Request/response data
- Authentication status
- Error details
- API endpoint information

## Future Enhancements

### Potential Improvements

1. **Bulk Invitations:** Support multiple email addresses
2. **Custom Messages:** Allow personalized invitation messages
3. **Expiration Settings:** Configurable invitation expiry
4. **Resend Functionality:** Ability to resend expired invitations
5. **Invitation Tracking:** Dashboard to monitor invitation status
6. **Role Templates:** Predefined role configurations
7. **Email Templates:** Customizable invitation email designs

### Integration Opportunities

1. **User Management:** Integration with user listing and editing
2. **Audit Logging:** Track all invitation activities
3. **Notification System:** Real-time updates on invitation status
4. **Analytics:** Invitation success rates and metrics
