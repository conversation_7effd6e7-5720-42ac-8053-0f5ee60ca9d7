// Test script for direct API testing in browser console
async function testInvitationApi() {
  console.log('====== TESTING INVITATION API ======');
  
  // Get token from localStorage
  const token = localStorage.getItem('access_token');
  if (!token) {
    console.error('No authentication token found. Please log in first.');
    return;
  }
  
  console.log('Token available: ' + token.substring(0, 10) + '...');
  
  // Test payload matching the example format
  const payload = {
    "emails": [
      "<EMAIL>"
    ],
    "role": "supplier",
    "message": "This is a test invitation",
    "organization_id": 0,
    "expires_in_days": 7
  };
  
  console.log('Sending payload:', payload);
  
  try {
    // 1. Try with JSON format
    console.log('\n==== ATTEMPT 1: JSON FORMAT ====');
    
    const jsonResponse = await fetch('https://dev.ascensionservices.net/api/v1/invitations/bulk', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(payload)
    });
    
    console.log('JSON format status:', jsonResponse.status);
    try {
      const jsonData = await jsonResponse.json();
      console.log('JSON response data:', jsonData);
    } catch (e) {
      const textResponse = await jsonResponse.text();
      console.log('JSON format text response:', textResponse);
    }
    
    // 2. Try with form-urlencoded format
    console.log('\n==== ATTEMPT 2: FORM-URLENCODED FORMAT ====');
    
    // Convert payload to URLSearchParams format
    const formData = new URLSearchParams();
    formData.append('emails', payload.emails.join(','));
    formData.append('role', payload.role);
    formData.append('message', payload.message);
    formData.append('organization_id', payload.organization_id.toString());
    formData.append('expires_in_days', payload.expires_in_days.toString());
    
    console.log('Form data:', formData.toString());
    
    const formResponse = await fetch('https://dev.ascensionservices.net/api/v1/invitations/bulk', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: formData.toString()
    });
    
    console.log('Form-urlencoded format status:', formResponse.status);
    try {
      const formData = await formResponse.json();
      console.log('Form-urlencoded response data:', formData);
    } catch (e) {
      const textResponse = await formResponse.text();
      console.log('Form-urlencoded text response:', textResponse);
    }
    
    // 3. Try with array notation for emails
    console.log('\n==== ATTEMPT 3: FORM-URLENCODED WITH ARRAY NOTATION ====');
    
    // Convert payload to URLSearchParams format with array notation for emails
    const formData2 = new URLSearchParams();
    // Use array notation for emails
    formData2.append('emails[]', payload.emails[0]);
    formData2.append('role', payload.role);
    formData2.append('message', payload.message);
    formData2.append('organization_id', payload.organization_id.toString());
    formData2.append('expires_in_days', payload.expires_in_days.toString());
    
    console.log('Form data with array notation:', formData2.toString());
    
    const formArrayResponse = await fetch('https://dev.ascensionservices.net/api/v1/invitations/bulk', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: formData2.toString()
    });
    
    console.log('Form-urlencoded with array format status:', formArrayResponse.status);
    try {
      const formArrayData = await formArrayResponse.json();
      console.log('Form-urlencoded with array response data:', formArrayData);
    } catch (e) {
      const textResponse = await formArrayResponse.text();
      console.log('Form-urlencoded with array text response:', textResponse);
    }
    
  } catch (error) {
    console.error('Error testing invitation API:', error);
  }
}

// Instructions for using this script:
console.log('To run the invitation API test:');
console.log('1. Make sure you are logged in');
console.log('2. Run the test by typing: testInvitationApi()');
