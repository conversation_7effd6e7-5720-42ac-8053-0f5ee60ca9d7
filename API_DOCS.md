# Comprehensive API Documentation

## Table of Contents
- [Authentication](#authentication)
- [Users](#users)
- [Organizations](#organizations)
- [Invitations](#invitations)
- [Procurements](#procurements)
- [Email Templates](#email-templates)
- [Common Responses](#common-responses)
- [Rate Limiting](#rate-limiting)

## Base URL
```
https://dev.ascensionservices.net/api/v1
```

## Authentication
All endpoints (except `/auth/*`) require authentication using a JWT token.

### Headers
```
Authorization: Bearer {token}
Content-Type: application/json
```

## Authentication Endpoints

### Signup Organisation
```http
POST /auth/signup
```

**Request Body**
- `organization_name` (string, required)
- `email` (string, required)
- `password` (string, required)
- `contact_name` (string, required)
- `phone_number` (string, optional)

### Login
```http
POST /auth/login
```

**Request Body**
- `email` (string, required)
- `password` (string, required)

### Invite User
```http
POST /auth/invite
```

**Request Body**
- `email` (string, required)
- `role` (string, required)
- `organization_id` (string, required)
- `message` (string, optional)

### Complete Setup
```http
POST /auth/complete-setup
```

**Request Body**
- `token` (string, required)
- `password` (string, required)
- `profile` (object, optional)

---

## Users

### Get Current User
```http
GET /users/me
```

**Response**
```json
{
  "id": "user_123",
  "email": "<EMAIL>",
  "full_name": "John Doe",
  "role": "admin",
  "organization_id": "org_123"
}
```

### Update User
```http
PATCH /users/me
```

**Request Body**
- `full_name` (string, optional)
- `avatar_url` (string, optional)
- `phone_number` (string, optional)
- `timezone` (string, optional)

---

## Organizations

### List Organizations
```http
GET /organizations
```

**Query Parameters**
- `page` (number, optional, default: 1)
- `limit` (number, optional, default: 10)
- `type` (string, optional)

### Create Organization
```http
POST /organizations
```

**Request Body**
- `name` (string, required)
- `type` (string, required)
- `address` (string, optional)
- `contact_email` (string, optional)
- `phone_number` (string, optional)
- `tax_id` (string, optional)
- `registration_number` (string, optional)

---

## Invitations

### Send Invitation
```http
POST /invitations
```

**Request Body**
- `email` (string, required)
- `role` (string, required)
- `organization_id` (string, required)
- `message` (string, optional)

### List Invitations
```http
GET /invitations
```

**Query Parameters**
- `status` (string, optional)
- `organization_id` (string, optional)
- `page` (number, optional, default: 1)
- `limit` (number, optional, default: 10)

### Accept Invitation
```http
POST /invitations/{invitation_id}/accept
```

**Path Parameters**
- `invitation_id` (string, required)

---

## Procurements

### Create RFQ (Request for Quotation)
```http
POST /procurements/rfqs
```

**Request Body**
- `title` (string, required)
- `description` (string, optional)
- `items` (array, required)
  - `name` (string, required)
  - `quantity` (number, required)
  - `unit` (string, required)
  - `specifications` (string, optional)
- `deadline` (string, required, ISO 8601)
- `attachments` (array, optional)

### List RFQs
```http
GET /procurements/rfqs
```

**Query Parameters**
- `status` (string, optional)
- `organization_id` (string, optional)
- `page` (number, optional, default: 1)
- `limit` (number, optional, default: 10)

### Submit Quotation
```http
POST /procurements/quotations
```

**Request Body**
- `rfq_id` (string, required)
- `items` (array, required)
  - `item_id` (string, required)
  - `unit_price` (number, required)
  - `delivery_time` (string, required)
  - `notes` (string, optional)
- `valid_until` (string, required, ISO 8601)

---

## Email Templates

### List All Email Templates
```http
GET /email/templates/
```

**Query Parameters**
- `page` (number, optional, default: 1)
- `limit` (number, optional, default: 10)
- `search` (string, optional)

### Create Email Template
```http
POST /email/templates/
```

**Request Body**
- `name` (string, required)
- `slug` (string, required)
- `subject` (string, required)
- `body` (string, required)
- `variables` (array, optional)
- `is_active` (boolean, optional, default: true)

### Send Email Using Template
```http
POST /email/send_email
```

**Request Body**
- `template_slug` (string, required)
- `to` (array, required)
- `cc` (array, optional)
- `bcc` (array, optional)
- `context` (object, required)
- `attachments` (array, optional)

---

## Common Responses

### Success Response
```json
{
  "status": "success",
  "data": {},
  "message": "Operation completed successfully"
}
```

### Error Response
```json
{
  "status": "error",
  "error": {
    "code": "ERROR_CODE",
    "message": "Error description",
    "details": {}
  }
}
```

### Paginated Response
```json
{
  "status": "success",
  "data": {
    "items": [],
    "total": 0,
    "page": 1,
    "limit": 10,
    "total_pages": 1
  }
}
```

## Rate Limiting
- 100 requests per minute per IP address
- Headers included in response:
  - `X-RateLimit-Limit`: Request limit per time window
  - `X-RateLimit-Remaining`: Remaining requests in current window
  - `X-RateLimit-Reset`: Time when the rate limit resets (UTC epoch seconds)

## Support
For any questions or issues, <NAME_EMAIL>
