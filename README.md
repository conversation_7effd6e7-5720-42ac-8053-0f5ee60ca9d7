# Ascension Frontend

## Overview
Welcome to the Ascension Frontend project! This is a modern web application built with React and TypeScript, designed to provide an intuitive and responsive user interface.

## 🚀 Getting Started

### Prerequisites
- Node.js (v16 or later)
- npm (v8 or later) or Yarn

### Installation
1. Clone the repository:
   ```bash
   git clone https://github.com/ascensionorg/frontend-v1.git
   cd frontend-v1
   ```

2. Install dependencies:
   ```bash
   npm install
   # or
   yarn install
   ```

### Running the Application

```bash
# Start the development server
npm run dev
# or
yarn dev

# Build for production
npm run build
# or
yarn build
```

## 🛠️ Tech Stack

- **Framework**: React
- **Language**: TypeScript
- **Styling**: CSS Modules / Styled Components
- **State Management**: Redux / Context API
- **Routing**: React Router
- **Build Tool**: Vite / Webpack

## 📁 Project Structure

```
src/
├── components/     # Reusable UI components
├── layouts/        # Layout components
├── pages/          # Page components
├── assets/         # Static assets
├── styles/         # Global styles
├── hooks/          # Custom React hooks
├── utils/          # Utility functions
└── types/          # TypeScript type definitions
```

c_stores_manager, c_accountant, c_procurement_officer, c_approving_manager, and c_requesting_user, supplier, s_admin, c_admin, super_admin
