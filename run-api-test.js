// Node.js version of the test script
const { exec } = require('child_process');
const fs = require('fs');
const path = require('path');

// Try to find the token from localStorage equivalent
let token = '';

// Check if there's a token stored in a local file for testing
try {
  if (fs.existsSync('./token.txt')) {
    token = fs.readFileSync('./token.txt', 'utf8').trim();
    console.log('Found token in token.txt');
  }
} catch (err) {
  console.error('Error reading token file:', err);
}

// Prompt user if no token is found
if (!token) {
  console.log('\x1b[33m%s\x1b[0m', 'No token found. Please provide your access token:');
  console.log('\x1b[33m%s\x1b[0m', '1. Open browser console while logged in to the application');
  console.log('\x1b[33m%s\x1b[0m', '2. Run: localStorage.getItem("access_token")');
  console.log('\x1b[33m%s\x1b[0m', '3. Copy the token value (without quotes)');
  console.log('\x1b[33m%s\x1b[0m', '4. Create a file named token.txt in this directory and paste the token');
  console.log('\x1b[33m%s\x1b[0m', '5. Run this script again');
  process.exit(1);
}

// Create curl command
const curlCommand = `curl -X POST https://dev.ascensionservices.net/api/v1/invitations/bulk \\
  -H 'Content-Type: application/json' \\
  -H 'Accept: application/json' \\
  -H 'Authorization: Bearer ${token}' \\
  -d '{"emails": ["<EMAIL>"], "role": "supplier", "message": "Test invitation", "expires_in_days": 7}' \\
  -v`;

console.log('\x1b[36m%s\x1b[0m', 'Running test API call with curl...');
console.log('\x1b[90m%s\x1b[0m', curlCommand);
console.log('\x1b[36m%s\x1b[0m', 'API Response:');

// Execute curl command
exec(curlCommand, (error, stdout, stderr) => {
  if (error) {
    console.error('\x1b[31m%s\x1b[0m', 'Error executing curl command:', error);
    return;
  }
  
  // Output curl response
  if (stderr) {
    console.log('\x1b[90m%s\x1b[0m', stderr); // stderr from curl contains request details with -v flag
  }
  
  if (stdout) {
    try {
      // Try to parse as JSON for pretty printing
      const jsonResponse = JSON.parse(stdout);
      console.log('\x1b[32m%s\x1b[0m', 'API Response (formatted):');
      console.log(JSON.stringify(jsonResponse, null, 2));
    } catch (e) {
      // If not valid JSON, print raw response
      console.log('\x1b[33m%s\x1b[0m', 'API Response (raw):');
      console.log(stdout);
    }
  }
  
  console.log('\x1b[36m%s\x1b[0m', 'Test completed');
});
