#!/bin/bash

# <PERSON><PERSON>t to update authentication files with new versions
echo "Starting authentication files update..."

# Function to safely replace a file
replace_file() {
  local original="$1"
  local new="$2"
  
  if [ -f "$new" ]; then
    echo "Replacing $original with $new"
    mv "$new" "$original"
    echo "✅ Successfully updated $original"
  else
    echo "❌ Error: $new does not exist, skipping replacement of $original"
  fi
}

# Replace auth service
replace_file "src/services/auth.service.ts" "src/services/auth.service.ts.new"

# Replace password service
replace_file "src/services/password.service.ts" "src/services/password.service.ts.new"

# Replace auth context
replace_file "src/context/AuthContext.tsx" "src/context/AuthContext.tsx.new"

# Replace login page
replace_file "src/app/(auth)/login/page.tsx" "src/app/(auth)/login/page.tsx.new"

# Replace forgot password page
replace_file "src/app/(auth)/forgot-password/page.tsx" "src/app/(auth)/forgot-password/page.tsx.new"

# Replace reset password page
replace_file "src/app/(auth)/reset-password/page.tsx" "src/app/(auth)/reset-password/page.tsx.new"

# Replace API proxy routes
replace_file "src/app/api/proxy/auth/login/route.ts" "src/app/api/proxy/auth/login/route.ts.new"
replace_file "src/app/api/proxy/auth/signup/route.ts" "src/app/api/proxy/auth/signup/route.ts.new"

echo "Authentication files update completed!"
