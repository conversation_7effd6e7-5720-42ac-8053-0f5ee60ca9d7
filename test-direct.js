// Direct test script for browser console
// Copy and paste this entire script into your browser console
// while logged in to your application

// Function to test the invitation API
async function testInvitationApi() {
  console.clear(); // Clear the console
  console.log('🔍 TESTING SUPPLIER INVITATION API');
  console.log('----------------------------------');

  // Get token from localStorage
  const token = localStorage.getItem('access_token');
  if (!token) {
    console.error('❌ No authentication token found in localStorage');
    return;
  }
  console.log('✓ Found authentication token in localStorage');
  
  // Test URL and create headers
  const apiUrl = 'https://dev.ascensionservices.net/api/v1/invitations/bulk';
  const headers = {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': `Bearer ${token}`
  };
  
  // Let's try multiple payload variations to see what works
  const testCases = [
    {
      name: 'Standard payload without organization_id',
      payload: {
        emails: ['<EMAIL>'],
        role: 'supplier',
        message: 'Test invitation',
        expires_in_days: 7
      }
    },
    {
      name: 'With organization_id null',
      payload: {
        emails: ['<EMAIL>'],
        role: 'supplier',
        message: 'Test invitation',
        organization_id: null,
        expires_in_days: 7
      }
    },
    {
      name: 'With organization_id as string',
      payload: {
        emails: ['<EMAIL>'],
        role: 'supplier',
        message: 'Test invitation',
        organization_id: "0",
        expires_in_days: 7
      }
    },
    {
      name: 'Minimal payload',
      payload: {
        emails: ['<EMAIL>'],
        role: 'supplier'
      }
    }
  ];

  // Test each variation
  for (const testCase of testCases) {
    console.log(`\n🧪 TESTING: ${testCase.name}`);
    console.log('Payload:', JSON.stringify(testCase.payload));
    
    try {
      const response = await fetch(apiUrl, {
        method: 'POST',
        headers: headers,
        body: JSON.stringify(testCase.payload)
      });
      
      console.log(`Status: ${response.status} ${response.statusText}`);
      
      let responseData;
      try {
        const text = await response.text();
        try {
          responseData = JSON.parse(text);
          console.log('Response data:', responseData);
        } catch (e) {
          console.log('Raw response (not JSON):', text);
        }
      } catch (e) {
        console.log('Error reading response:', e);
      }
      
      console.log(response.ok ? '✅ SUCCESS' : '❌ FAILED');
    } catch (error) {
      console.error('Fetch error:', error);
    }
  }
}

// Run the test
testInvitationApi().then(() => {
  console.log('\n🏁 Testing completed');
});
