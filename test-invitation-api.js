// <PERSON>ript to extract authentication token and test the invitation API directly
// Run this in your browser console

// Get the authentication token from localStorage
const token = localStorage.getItem('access_token');
console.log('Auth token retrieved:', token ? '✅ Token found' : '❌ No token found');
console.log('Token length:', token?.length);

// Create a curl command for testing (copy-paste this to your terminal)
const curlCommand = `curl -X POST https://dev.ascensionservices.net/api/v1/invitations/bulk \\
  -H 'Content-Type: application/json' \\
  -H 'Accept: application/json' \\
  -H 'Authorization: Bearer ${token}' \\
  -d '{"emails": ["<EMAIL>"], "role": "supplier", "message": "Test invitation", "expires_in_days": 7}' \\
  -v`;

console.log('\nCurl command for testing (copy and run in terminal):\n\n', curlCommand);

// You can also test directly using fetch API
console.log('\nTesting API directly with fetch...');

fetch('https://dev.ascensionservices.net/api/v1/invitations/bulk', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
    'Authorization': `Bearer ${token}`
  },
  body: JSON.stringify({
    emails: ['<EMAIL>'],
    role: 'supplier',
    message: 'Test invitation',
    expires_in_days: 7
  })
})
.then(response => {
  console.log('Response status:', response.status);
  return response.text();
})
.then(data => {
  try {
    const jsonData = JSON.parse(data);
    console.log('API response (JSON):', jsonData);
  } catch (e) {
    console.log('API response (raw):', data);
  }
})
.catch(error => {
  console.error('Fetch error:', error);
});
