import { NextResponse } from 'next/server';
import type { NextRequest } from 'next/server';

// Role to dashboard URL mapping - same as in roleRedirect.ts but for middleware
const ROLE_DASHBOARD_MAPPING: { [key: string]: string } = {
  // Admin roles
  'super_admin': '/super-admin-dashboard',
  'admin': '/super-admin-dashboard',
  's_admin': '/supplier-dashboard',
  'c_admin': '/company-dashboard',

  // Company user roles (invitable by c_admin)
  'c_stores_manager': '/stores-manager-dashboard',
  'c_accountant': '/accountant-dashboard',
  'c_procurement_officer': '/procurement-officer-dashboard',
  'c_approving_manager': '/approving-manager-dashboard',
  'c_requesting_user': '/requesting-user-dashboard',

  // Supplier user roles
  'supplier_user': '/supplier-user-dashboard',
  's_user': '/supplier-user-dashboard',
  'supplier': '/supplier-user-dashboard',
};

// Protected routes that require authentication
const PROTECTED_ROUTES = [
  '/dashboard',
  '/company-dashboard',
  '/supplier-dashboard',
  '/super-admin-dashboard',
  '/stores-manager-dashboard',
  '/accountant-dashboard',
  '/procurement-officer-dashboard',
  '/approving-manager-dashboard',
  '/requesting-user-dashboard',
  '/supplier-user-dashboard',
];

// Routes that require specific roles
const ROLE_PROTECTED_ROUTES: { [key: string]: string[] } = {
  '/company-dashboard': ['c_admin'],
  '/supplier-dashboard': ['s_admin'],
  '/super-admin-dashboard': ['super_admin', 'admin'],
  '/stores-manager-dashboard': ['c_stores_manager'],
  '/accountant-dashboard': ['c_accountant'],
  '/procurement-officer-dashboard': ['c_procurement_officer'],
  '/approving-manager-dashboard': ['c_approving_manager'],
  '/requesting-user-dashboard': ['c_requesting_user'],
  '/supplier-user-dashboard': ['supplier_user', 's_user', 'supplier'],
};

function getUserFromRequest(request: NextRequest): { role?: string; isAuthenticated: boolean } {
  try {
    // Check for access token first to determine authentication status
    const accessToken = request.cookies.get('access_token')?.value;

    if (!accessToken) {
      return { isAuthenticated: false };
    }

    // Try to get user data from cookies
    const userCookie = request.cookies.get('user')?.value;
    let userData = null;

    if (userCookie) {
      try {
        userData = JSON.parse(decodeURIComponent(userCookie));
      } catch (parseError) {
        console.error('Error parsing user cookie in middleware:', parseError);
        // If we can't parse user data but have access token, still consider authenticated
        return { isAuthenticated: true };
      }
    }

    return {
      role: userData?.role,
      isAuthenticated: true
    };
  } catch (error) {
    console.error('Error in getUserFromRequest:', error);
    return { isAuthenticated: false };
  }
}

export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // Temporarily disable middleware to debug login issues
  // TODO: Re-enable after fixing login redirection
  console.log(`Middleware: ${pathname} - DISABLED FOR DEBUGGING`);
  return NextResponse.next();

  /* DISABLED CODE - WILL RE-ENABLE AFTER FIXING LOGIN
  // Skip middleware for static files, API routes, and auth pages
  if (
    pathname.startsWith('/_next') ||
    pathname.startsWith('/api') ||
    pathname.startsWith('/login') ||
    pathname.startsWith('/signup') ||
    pathname.startsWith('/forgot-password') ||
    pathname.includes('.') // Static files
  ) {
    return NextResponse.next();
  }

  const { role, isAuthenticated } = getUserFromRequest(request);

  // Handle protected routes
  if (PROTECTED_ROUTES.some(route => pathname.startsWith(route))) {
    // Redirect to login if not authenticated
    if (!isAuthenticated) {
      const loginUrl = new URL('/login', request.url);
      loginUrl.searchParams.set('callbackUrl', pathname);
      return NextResponse.redirect(loginUrl);
    }

    // Handle role-based access control
    const requiredRoles = ROLE_PROTECTED_ROUTES[pathname];
    if (requiredRoles && role && !requiredRoles.includes(role)) {
      // User doesn't have the required role, redirect to their appropriate dashboard
      const userDashboard = ROLE_DASHBOARD_MAPPING[role];
      if (userDashboard && userDashboard !== pathname) {
        console.log(`Middleware: Redirecting ${role} from ${pathname} to ${userDashboard}`);
        return NextResponse.redirect(new URL(userDashboard, request.url));
      }
    }

    // Handle generic dashboard route - redirect to role-specific dashboard
    if (pathname === '/dashboard' && role) {
      const userDashboard = ROLE_DASHBOARD_MAPPING[role];
      if (userDashboard) {
        console.log(`Middleware: Fast redirect ${role} from /dashboard to ${userDashboard}`);
        return NextResponse.redirect(new URL(userDashboard, request.url));
      }
    }
  }

  // Handle root path redirection for authenticated users
  if (pathname === '/' && isAuthenticated && role) {
    const userDashboard = ROLE_DASHBOARD_MAPPING[role];
    if (userDashboard) {
      console.log(`Middleware: Root redirect ${role} to ${userDashboard}`);
      return NextResponse.redirect(new URL(userDashboard, request.url));
    }
  }

  return NextResponse.next();
  */
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - api (API routes)
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     */
    '/((?!api|_next/static|_next/image|favicon.ico).*)',
  ],
};
