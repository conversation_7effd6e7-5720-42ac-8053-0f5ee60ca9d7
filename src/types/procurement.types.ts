export interface BudgetLimit {
  id: number;
  organisation_id: number;
  currency: string;
  amount_threshold: number;
  approver_level_required: number;
  min_quotations_required: number;
  override_case_reference: string | null;
  is_general_rule: boolean;
  created_at: string;
  updated_at: string | null;
}

export interface BudgetLimitCreate {
  currency: string;
  amount_threshold: number;
  approver_level_required: number;
  min_quotations_required: number;
  override_case_reference?: string | null;
  is_general_rule: boolean;
}

export interface BudgetLimitUpdate {
  currency?: string;
  amount_threshold?: number;
  approver_level_required?: number;
  min_quotations_required?: number;
  override_case_reference?: string | null;
  is_general_rule?: boolean;
}

export interface BudgetCode {
  id: number;
  organisation_id: number;
  code: string;
  description: string;
  department: string;
  budget_limit: number | null;
  currency: string;
  is_active: boolean;
  created_at: string;
  updated_at: string | null;
}

export interface BudgetCodeCreate {
  code: string;
  description: string;
  department: string;
  budget_limit?: number | null;
  currency: string;
  is_active: boolean;
}

export interface BudgetCodeUpdate {
  code?: string;
  description?: string;
  department?: string;
  budget_limit?: number | null;
  currency?: string;
  is_active?: boolean;
}

export interface ProcurementGroup {
  id: number;
  organisation_id: number;
  group_id_code: string;
  name: string;
  description: string | null;
  invoices_billed_to: string;
  status: 'active' | 'inactive';
  cases_count: number;
  invoices_count: number;
  created_at: string;
  updated_at: string | null;
}

export interface ProcurementGroupCreate {
  group_id_code: string;
  name: string;
  description?: string | null;
  invoices_billed_to: string;
  status: 'active' | 'inactive';
}

export interface ProcurementGroupUpdate {
  group_id_code?: string;
  name?: string;
  description?: string | null;
  invoices_billed_to?: string;
  status?: 'active' | 'inactive';
}

export interface ProcurementGroupStatusUpdate {
  status: 'active' | 'inactive';
}

// API Response types
export interface APIResponse<T> {
  data: T;
  message?: string;
  success: boolean;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// Basic data interfaces that match the API responses
export interface BudgetLimitFormData {
  currency: string;
  amount_threshold: number;
  approver_level_required: number;
  min_quotations_required: number;
  override_case_reference?: string;
  is_general_rule: boolean;
}

export interface BudgetCodeFormData {
  code: string;
  description: string;
  department?: string;
  budget_limit?: number | null;
  currency?: string;
  is_active: boolean;
}

export interface ProcurementGroupFormData {
  group_id_code: string;
  name: string;
  description?: string;
  invoices_billed_to?: string;
  status: 'active' | 'inactive';
}

export const SUPPORTED_CURRENCIES = ['UGX', 'USD'] as const;
export type Currency = typeof SUPPORTED_CURRENCIES[number];

export const APPROVAL_LEVELS = [1, 2, 3, 4, 5] as const;
export const MIN_QUOTATIONS_OPTIONS = [1, 2, 3, 4] as const;

export const isAdmin = (userRole?: string): boolean => {
  if (!userRole) return false;
  return ['super_admin', 'c_admin', 's_admin'].includes(userRole);
};

export const canManageProcurement = (userRole?: string): boolean => {
  if (!userRole) return false;
  return ['super_admin', 'c_admin'].includes(userRole);
};

export const canOverrideQuotations = (userRole?: string): boolean => {
  if (!userRole) return false;
  return ['super_admin', 'c_admin', 'c_procurement_officer', 'c_approving_manager'].includes(userRole);
}; 
