/**
 * Centralized role-based redirection utility
 * Ensures consistent dashboard routing across the application
 */

export interface RoleRedirectMapping {
  [key: string]: string;
}

/**
 * Role to dashboard URL mapping
 * This is the single source of truth for role-based redirections
 */
export const ROLE_DASHBOARD_MAPPING: RoleRedirectMapping = {
  // Admin roles
  'super_admin': '/super-admin-dashboard',
  'admin': '/super-admin-dashboard',
  's_admin': '/supplier-dashboard',
  'c_admin': '/company-dashboard',

  // Company user roles (invitable by c_admin)
  'c_stores_manager': '/stores-manager-dashboard',
  'c_accountant': '/accountant-dashboard',
  'c_procurement_officer': '/procurement-officer-dashboard',
  'c_approving_manager': '/approving-manager-dashboard',
  'c_requesting_user': '/requesting-user-dashboard',

  // Supplier user roles
  'supplier_user': '/supplier-user-dashboard',
  's_user': '/supplier-user-dashboard',
  'supplier': '/supplier-user-dashboard',
};

/**
 * Get the appropriate dashboard URL for a given user role
 * @param role - The user's role
 * @returns The dashboard URL for the role, or '/dashboard' as fallback
 */
export function getDashboardUrlForRole(role: string | undefined | null): string {
  if (!role) {
    console.warn('No role provided, using default dashboard');
    return '/dashboard';
  }

  const dashboardUrl = ROLE_DASHBOARD_MAPPING[role];
  
  if (!dashboardUrl) {
    console.warn(`Unknown role: ${role}, using default dashboard`);
    return '/dashboard';
  }

  return dashboardUrl;
}

/**
 * Get a human-readable description for a role
 * @param role - The user's role
 * @returns A human-readable role description
 */
export function getRoleDescription(role: string | undefined | null): string {
  const roleDescriptions: { [key: string]: string } = {
    'super_admin': 'Super Administrator',
    'admin': 'Administrator',
    's_admin': 'Supplier Administrator',
    'c_admin': 'Company Administrator',
    'c_stores_manager': 'Stores Manager',
    'c_accountant': 'Accountant',
    'c_procurement_officer': 'Procurement Officer',
    'c_approving_manager': 'Approving Manager',
    'c_requesting_user': 'Requesting User',
    'supplier_user': 'Supplier User',
    's_user': 'Supplier User',
    'supplier': 'Supplier User',
  };

  return roleDescriptions[role || ''] || role || 'Unknown Role';
}

/**
 * Check if a role has administrative privileges
 * @param role - The user's role
 * @returns True if the role has admin privileges
 */
export function isAdminRole(role: string | undefined | null): boolean {
  const adminRoles = ['super_admin', 'admin', 's_admin', 'c_admin'];
  return adminRoles.includes(role || '');
}

/**
 * Check if a role is a company role (c_*)
 * @param role - The user's role
 * @returns True if the role is a company role
 */
export function isCompanyRole(role: string | undefined | null): boolean {
  return (role || '').startsWith('c_');
}

/**
 * Check if a role is a supplier role (s_*)
 * @param role - The user's role
 * @returns True if the role is a supplier role
 */
export function isSupplierRole(role: string | undefined | null): boolean {
  return (role || '').startsWith('s_') || ['supplier_user', 'supplier'].includes(role || '');
}

/**
 * Get all available roles that can be invited by a specific role
 * @param inviterRole - The role of the user doing the inviting
 * @returns Array of roles that can be invited
 */
export function getInvitableRoles(inviterRole: string | undefined | null): string[] {
  switch (inviterRole) {
    case 'c_admin':
      return [
        'c_stores_manager',
        'c_accountant',
        'c_procurement_officer',
        'c_approving_manager',
        'c_requesting_user'
      ];
    case 'super_admin':
    case 'admin':
      // Super admins can invite all roles
      return Object.keys(ROLE_DASHBOARD_MAPPING);
    default:
      return [];
  }
}

/**
 * Redirect to the appropriate dashboard based on user role
 * This function can be used in Next.js router context
 * @param role - The user's role
 * @param router - Next.js router instance
 * @param logMessage - Optional custom log message
 */
export function redirectToDashboard(
  role: string | undefined | null,
  router: any,
  logMessage?: string
): void {
  const dashboardUrl = getDashboardUrlForRole(role);
  const roleDesc = getRoleDescription(role);

  const message = logMessage || `Redirecting ${roleDesc} to dashboard`;
  console.log(message, { role, dashboardUrl });

  router.push(dashboardUrl);
}

/**
 * Fast auth-based routing - immediately redirect to role-specific dashboard
 * This bypasses the generic dashboard page entirely for faster navigation
 * @param role - The user's role
 * @param router - Next.js router instance
 * @param logMessage - Optional custom log message
 */
export function fastAuthRedirect(
  role: string | undefined | null,
  router: any,
  logMessage?: string
): void {
  const dashboardUrl = getDashboardUrlForRole(role);
  const roleDesc = getRoleDescription(role);

  const message = logMessage || `Fast auth redirect: ${roleDesc} → ${dashboardUrl}`;
  console.log(message, { role, dashboardUrl });

  // Use replace instead of push to avoid adding to history stack
  // This makes navigation faster and prevents back button issues
  router.replace(dashboardUrl);
}

/**
 * Validate if a user has permission to access a specific dashboard
 * @param userRole - The user's role
 * @param requiredRole - The role required to access the dashboard
 * @returns True if the user has permission
 */
export function hasPermissionForDashboard(
  userRole: string | undefined | null,
  requiredRole: string
): boolean {
  return userRole === requiredRole;
}

/**
 * Get the expected dashboard URL for the current user
 * Useful for checking if user is on the correct dashboard
 * @param userRole - The user's role
 * @returns The expected dashboard URL
 */
export function getExpectedDashboardUrl(userRole: string | undefined | null): string {
  return getDashboardUrlForRole(userRole);
}
