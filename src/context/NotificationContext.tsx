'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { notificationService, Notification, CaseInvolvement } from '@/services/notification.service';

interface NotificationContextType {
  notifications: Notification[];
  unreadCount: number;
  loading: boolean;
  error: string | null;
  fetchNotifications: () => Promise<void>;
  fetchUnreadCount: () => Promise<void>;
  markAsViewed: (notificationId: string) => Promise<void>;
  markAllAsViewed: () => Promise<void>;
  createCaseInvolvement: (data: {
    procurementCaseId: string;
    userId: string;
    role: 'collaborator' | 'forwarded' | 'inquiry_recipient';
  }) => Promise<CaseInvolvement>;
  getCaseInvolvements: (procurementCaseId: string) => Promise<CaseInvolvement[]>;
  triggerStatusChange: (data: {
    procurementCaseId: string;
    status: string;
    message?: string;
  }) => Promise<void>;
  triggerNewQuote: (data: {
    procurementCaseId: string;
    quoteId: string;
    supplierId: string;
  }) => Promise<void>;
  triggerCaseAssignment: (data: {
    procurementCaseId: string;
    assignedToUserId: string;
    assignedByUserId: string;
  }) => Promise<void>;
}

const NotificationContext = createContext<NotificationContextType | undefined>(undefined);

export const useNotifications = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error('useNotifications must be used within a NotificationProvider');
  }
  return context;
};

interface NotificationProviderProps {
  children: React.ReactNode;
}

export const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch notifications
  const fetchNotifications = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await notificationService.getNotifications();
      setNotifications(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch notifications');
      console.error('Error fetching notifications:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Fetch unread count
  const fetchUnreadCount = useCallback(async () => {
    try {
      const count = await notificationService.getUnreadCount();
      setUnreadCount(count);
    } catch (err) {
      console.error('Error fetching unread count:', err);
    }
  }, []);

  // Mark notification as viewed
  const markAsViewed = useCallback(async (notificationId: string) => {
    try {
      await notificationService.markAsViewed(notificationId);
      // Update local state
      setNotifications(prev => 
        prev.map(notification => 
          notification.id === notificationId 
            ? { ...notification, isRead: true }
            : notification
        )
      );
      // Update unread count
      setUnreadCount(prev => Math.max(0, prev - 1));
    } catch (err) {
      console.error('Error marking notification as viewed:', err);
      throw err;
    }
  }, []);

  // Mark all notifications as viewed
  const markAllAsViewed = useCallback(async () => {
    try {
      await notificationService.markAllAsViewed();
      // Update local state
      setNotifications(prev => 
        prev.map(notification => ({ ...notification, isRead: true }))
      );
      setUnreadCount(0);
    } catch (err) {
      console.error('Error marking all notifications as viewed:', err);
      throw err;
    }
  }, []);

  // Create case involvement
  const createCaseInvolvement = useCallback(async (data: {
    procurementCaseId: string;
    userId: string;
    role: 'collaborator' | 'forwarded' | 'inquiry_recipient';
  }) => {
    try {
      return await notificationService.createCaseInvolvement(data);
    } catch (err) {
      console.error('Error creating case involvement:', err);
      throw err;
    }
  }, []);

  // Get case involvements
  const getCaseInvolvements = useCallback(async (procurementCaseId: string) => {
    try {
      return await notificationService.getCaseInvolvements(procurementCaseId);
    } catch (err) {
      console.error('Error fetching case involvements:', err);
      throw err;
    }
  }, []);

  // Trigger status change notification
  const triggerStatusChange = useCallback(async (data: {
    procurementCaseId: string;
    status: string;
    message?: string;
  }) => {
    try {
      await notificationService.triggerStatusChange(data);
    } catch (err) {
      console.error('Error triggering status change notification:', err);
      throw err;
    }
  }, []);

  // Trigger new quote notification
  const triggerNewQuote = useCallback(async (data: {
    procurementCaseId: string;
    quoteId: string;
    supplierId: string;
  }) => {
    try {
      await notificationService.triggerNewQuote(data);
    } catch (err) {
      console.error('Error triggering new quote notification:', err);
      throw err;
    }
  }, []);

  // Trigger case assignment notification
  const triggerCaseAssignment = useCallback(async (data: {
    procurementCaseId: string;
    assignedToUserId: string;
    assignedByUserId: string;
  }) => {
    try {
      await notificationService.triggerCaseAssignment(data);
    } catch (err) {
      console.error('Error triggering case assignment notification:', err);
      throw err;
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchNotifications();
    fetchUnreadCount();
  }, [fetchNotifications, fetchUnreadCount]);

  // Set up polling for real-time updates (every 30 seconds)
  useEffect(() => {
    const interval = setInterval(() => {
      fetchUnreadCount();
      // Only fetch full notifications if there are unread ones
      if (unreadCount > 0) {
        fetchNotifications();
      }
    }, 30000); // 30 seconds

    return () => clearInterval(interval);
  }, [fetchUnreadCount, fetchNotifications, unreadCount]);

  const value: NotificationContextType = {
    notifications,
    unreadCount,
    loading,
    error,
    fetchNotifications,
    fetchUnreadCount,
    markAsViewed,
    markAllAsViewed,
    createCaseInvolvement,
    getCaseInvolvements,
    triggerStatusChange,
    triggerNewQuote,
    triggerCaseAssignment,
  };

  return (
    <NotificationContext.Provider value={value}>
      {children}
    </NotificationContext.Provider>
  );
}; 
