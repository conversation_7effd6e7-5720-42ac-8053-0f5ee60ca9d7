'use client';

import React, { createContext, useContext, useState, useEffect, useCallback } from 'react';
import { messageService, Message, MessageDraft, FolderCounts, Recipient } from '@/services/message.service';

interface MessageContextType {
  // State
  messages: Message[];
  currentFolder: string;
  selectedMessages: string[];
  folderCounts: FolderCounts;
  loading: boolean;
  error: string | null;
  currentPage: number;
  totalPages: number;
  totalMessages: number;
  
  // Actions
  fetchMessages: (folder?: string, page?: number) => Promise<void>;
  fetchFolderCounts: () => Promise<void>;
  selectMessage: (messageId: string) => void;
  selectAllMessages: () => void;
  clearSelection: () => void;
  deleteMessage: (messageId: string) => Promise<void>;
  deleteSelectedMessages: () => Promise<void>;
  moveMessage: (messageId: string, folder: string) => Promise<void>;
  moveSelectedMessages: (folder: string) => Promise<void>;
  markAsRead: (messageId: string) => Promise<void>;
  starMessage: (messageId: string) => Promise<void>;
  createMessage: (messageData: MessageDraft) => Promise<Message>;
  sendMessage: (messageId: string) => Promise<void>;
  replyToMessage: (messageId: string, replyData: { body: string; attachments?: File[] }) => Promise<Message>;
  searchRecipients: (query: string) => Promise<Recipient[]>;
  uploadAttachment: (file: File) => Promise<any>;
}

const MessageContext = createContext<MessageContextType | undefined>(undefined);

export const useMessages = () => {
  const context = useContext(MessageContext);
  if (context === undefined) {
    throw new Error('useMessages must be used within a MessageProvider');
  }
  return context;
};

interface MessageProviderProps {
  children: React.ReactNode;
}

export const MessageProvider: React.FC<MessageProviderProps> = ({ children }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [currentFolder, setCurrentFolder] = useState('inbox');
  const [selectedMessages, setSelectedMessages] = useState<string[]>([]);
  const [folderCounts, setFolderCounts] = useState<FolderCounts>({
    inbox: 0,
    sent: 0,
    drafts: 0,
    trash: 0,
    unread: 0,
    starred: 0,
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [totalPages, setTotalPages] = useState(1);
  const [totalMessages, setTotalMessages] = useState(0);

  // Fetch messages by folder
  const fetchMessages = useCallback(async (folder: string = currentFolder, page: number = 1) => {
    try {
      setLoading(true);
      setError(null);
      const result = await messageService.getMessagesByFolder(folder, page, 20);
      setMessages(result.messages);
      setCurrentPage(result.page);
      setTotalPages(Math.ceil(result.total / result.limit));
      setTotalMessages(result.total);
      setCurrentFolder(folder);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch messages');
      console.error('Error fetching messages:', err);
    } finally {
      setLoading(false);
    }
  }, [currentFolder]);

  // Fetch folder counts
  const fetchFolderCounts = useCallback(async () => {
    try {
      const counts = await messageService.getFolderCounts();
      setFolderCounts(counts);
    } catch (err) {
      console.error('Error fetching folder counts:', err);
    }
  }, []);

  // Select message
  const selectMessage = useCallback((messageId: string) => {
    setSelectedMessages(prev => 
      prev.includes(messageId) 
        ? prev.filter(id => id !== messageId)
        : [...prev, messageId]
    );
  }, []);

  // Select all messages
  const selectAllMessages = useCallback(() => {
    setSelectedMessages(messages.map(msg => msg.id));
  }, [messages]);

  // Clear selection
  const clearSelection = useCallback(() => {
    setSelectedMessages([]);
  }, []);

  // Delete message
  const deleteMessage = useCallback(async (messageId: string) => {
    try {
      await messageService.deleteMessage(messageId);
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      setSelectedMessages(prev => prev.filter(id => id !== messageId));
      await fetchFolderCounts();
    } catch (err) {
      console.error('Error deleting message:', err);
      throw err;
    }
  }, [fetchFolderCounts]);

  // Delete selected messages
  const deleteSelectedMessages = useCallback(async () => {
    try {
      await messageService.bulkDeleteMessages(selectedMessages);
      setMessages(prev => prev.filter(msg => !selectedMessages.includes(msg.id)));
      setSelectedMessages([]);
      await fetchFolderCounts();
    } catch (err) {
      console.error('Error deleting selected messages:', err);
      throw err;
    }
  }, [selectedMessages, fetchFolderCounts]);

  // Move message
  const moveMessage = useCallback(async (messageId: string, folder: string) => {
    try {
      await messageService.moveMessage(messageId, folder);
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      setSelectedMessages(prev => prev.filter(id => id !== messageId));
      await fetchFolderCounts();
    } catch (err) {
      console.error('Error moving message:', err);
      throw err;
    }
  }, [fetchFolderCounts]);

  // Move selected messages
  const moveSelectedMessages = useCallback(async (folder: string) => {
    try {
      await messageService.bulkMoveMessages(selectedMessages, folder);
      setMessages(prev => prev.filter(msg => !selectedMessages.includes(msg.id)));
      setSelectedMessages([]);
      await fetchFolderCounts();
    } catch (err) {
      console.error('Error moving selected messages:', err);
      throw err;
    }
  }, [selectedMessages, fetchFolderCounts]);

  // Mark as read
  const markAsRead = useCallback(async (messageId: string) => {
    try {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === messageId ? { ...msg, isRead: true } : msg
        )
      );
      // Note: This would typically call an API to mark as read
      // await messageService.markAsRead(messageId);
    } catch (err) {
      console.error('Error marking message as read:', err);
      throw err;
    }
  }, []);

  // Star message
  const starMessage = useCallback(async (messageId: string) => {
    try {
      setMessages(prev => 
        prev.map(msg => 
          msg.id === messageId ? { ...msg, isStarred: !msg.isStarred } : msg
        )
      );
      // Note: This would typically call an API to star/unstar
      // await messageService.starMessage(messageId);
    } catch (err) {
      console.error('Error starring message:', err);
      throw err;
    }
  }, []);

  // Create message
  const createMessage = useCallback(async (messageData: MessageDraft) => {
    try {
      const newMessage = await messageService.createMessage(messageData);
      setMessages(prev => [newMessage, ...prev]);
      await fetchFolderCounts();
      return newMessage;
    } catch (err) {
      console.error('Error creating message:', err);
      throw err;
    }
  }, [fetchFolderCounts]);

  // Send message
  const sendMessage = useCallback(async (messageId: string) => {
    try {
      await messageService.sendMessage(messageId);
      setMessages(prev => prev.filter(msg => msg.id !== messageId));
      await fetchFolderCounts();
    } catch (err) {
      console.error('Error sending message:', err);
      throw err;
    }
  }, [fetchFolderCounts]);

  // Reply to message
  const replyToMessage = useCallback(async (messageId: string, replyData: { body: string; attachments?: File[] }) => {
    try {
      const reply = await messageService.replyToMessage(messageId, replyData);
      setMessages(prev => [reply, ...prev]);
      await fetchFolderCounts();
      return reply;
    } catch (err) {
      console.error('Error replying to message:', err);
      throw err;
    }
  }, [fetchFolderCounts]);

  // Search recipients
  const searchRecipients = useCallback(async (query: string) => {
    try {
      return await messageService.searchRecipients(query);
    } catch (err) {
      console.error('Error searching recipients:', err);
      throw err;
    }
  }, []);

  // Upload attachment
  const uploadAttachment = useCallback(async (file: File) => {
    try {
      return await messageService.uploadAttachment(file);
    } catch (err) {
      console.error('Error uploading attachment:', err);
      throw err;
    }
  }, []);

  // Initial data fetch
  useEffect(() => {
    fetchMessages();
    fetchFolderCounts();
  }, [fetchMessages, fetchFolderCounts]);

  const value: MessageContextType = {
    messages,
    currentFolder,
    selectedMessages,
    folderCounts,
    loading,
    error,
    currentPage,
    totalPages,
    totalMessages,
    fetchMessages,
    fetchFolderCounts,
    selectMessage,
    selectAllMessages,
    clearSelection,
    deleteMessage,
    deleteSelectedMessages,
    moveMessage,
    moveSelectedMessages,
    markAsRead,
    starMessage,
    createMessage,
    sendMessage,
    replyToMessage,
    searchRecipients,
    uploadAttachment,
  };

  return (
    <MessageContext.Provider value={value}>
      {children}
    </MessageContext.Provider>
  );
}; 
