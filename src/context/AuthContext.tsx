'use client';

import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { AuthService } from '@/services/auth.service';
import { toast } from 'sonner';

export interface User {
  id: string;
  email: string;
  full_name: string;
  first_name?: string;
  last_name?: string;
  role: string;
  organization_id?: string; // US spelling
  organisation_id?: string; // UK spelling - API returns this
}

interface SignupData {
  org_in: {
    name: string;
    type: 'company' | 'supplier';
    address: string;
    telephone: string;
    contact_person: string;
    status: string;
    country: string;
    incorporation_date: string;
  };
  admin_in: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    telephone_number: string;
    role_names: string[];
  };
}

interface CompleteSetupData {
  token: string;
  password: string;
  profile?: Record<string, any>;
}

interface AuthContextType {
  isAuthenticated: boolean | null; // null means loading/initializing
  login: (email: string, password: string) => Promise<void>;
  signup: (userData: SignupData) => Promise<void>;
  completeSetup: (data: CompleteSetupData) => Promise<void>;
  logout: () => void;
  user: User | null;
  mockSuperAdminLogin: () => void;
  mockUserLogin: (userType: string) => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const AuthProvider = ({ children }: { children: ReactNode }) => {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null); // null means loading/initializing
  const [user, setUser] = useState<User | null>(null);

  // Initialize auth state from localStorage
  useEffect(() => {
    const token = typeof window !== 'undefined' ? localStorage.getItem('access_token') : null;
    
    if (token) {
      const storedUser = localStorage.getItem('user');
      if (storedUser) {
        try {
          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);
        } catch (e) {
          console.error('Failed to parse user from localStorage', e);
          localStorage.removeItem('user'); // Clear corrupted user data
        }
      }
      setIsAuthenticated(true);
    } else {
      setIsAuthenticated(false);
      setUser(null);
    }
  }, []);

  const login = async (email: string, password: string) => {
    try {
      const response = await AuthService.login({ email, password });

      if (response.access_token) {
        // Get user data directly from API to ensure we have the latest data
        try {
          const userData = await AuthService.getCurrentUser();
          console.log('User data after login:', userData);
          console.log('User role:', userData.role);

          // Prevent login if user has no role
          if (!userData.role && (!userData.role_names || userData.role_names.length === 0)) {
            console.error('Login rejected: User has no assigned role');
            AuthService.logout(); // Clear tokens
            setIsAuthenticated(false);
            setUser(null);
            toast.error('Access denied: No role assigned to your account. Please contact your administrator.');
            throw new Error('Access denied: No role assigned to this account');
          }

          // Store user data and update state
          localStorage.setItem('user', JSON.stringify(userData));

          // Also store user data in a cookie for middleware access
          if (typeof window !== 'undefined') {
            document.cookie = `user=${encodeURIComponent(JSON.stringify(userData))}; path=/; max-age=86400; SameSite=Lax`;
          }

          setUser(userData);
          setIsAuthenticated(true);
        } catch (e) {
          console.error('Failed to verify user data after login', e);
          AuthService.logout(); // Clean up any tokens
          setIsAuthenticated(false);
          throw e;
        }
      } else {
        console.error('Login response missing access_token');
        setIsAuthenticated(false);
      }
    } catch (error) {
      console.error('Login failed:', error);
      setIsAuthenticated(false);
      setUser(null);
      throw error;
    }
  };

  const signup = async (userData: SignupData) => {
    try {
      const response = await AuthService.signup(userData);
      if (response.access_token) {
        // Try to get user data
        try {
          const userData = await AuthService.getCurrentUser();
          localStorage.setItem('user', JSON.stringify(userData));
          setUser(userData);
        } catch (e) {
          console.error('Failed to fetch user data after signup', e);
        }
        
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Signup failed:', error);
      setIsAuthenticated(false);
      setUser(null);
      throw error;
    }
  };

  const completeSetup = async (data: CompleteSetupData) => {
    try {
      const response = await AuthService.completeSetup(data);
      if (response.access_token) {
        // Try to get user data
        try {
          const userData = await AuthService.getCurrentUser();
          localStorage.setItem('user', JSON.stringify(userData));
          setUser(userData);
        } catch (e) {
          console.error('Failed to fetch user data after setup completion', e);
        }
        
        setIsAuthenticated(true);
      }
    } catch (error) {
      console.error('Setup completion failed:', error);
      setIsAuthenticated(false);
      setUser(null);
      throw error;
    }
  };

  const logout = () => {
    // Clear local state first for immediate UI update
    setIsAuthenticated(false);
    setUser(null);

    // Show toast without waiting
    toast.success('Logged out successfully!');

    // Clear auth data
    AuthService.logout();

    // Clear user cookie for middleware
    if (typeof window !== 'undefined') {
      document.cookie = 'user=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax';
    }

    // Redirect to login page
    if (typeof window !== 'undefined') {
      window.location.href = '/login';
    }
  };

  const mockSuperAdminLogin = () => {
    const mockSuperAdmin: User = {
      id: 'mock-super-admin-001',
      email: '<EMAIL>',
      full_name: 'Super Administrator',
      first_name: 'Super',
      last_name: 'Administrator',
      role: 'super_admin',
      organization_id: 'ascension-org-001'
    };

    // Store mock tokens
    localStorage.setItem('access_token', 'mock-super-admin-token');
    localStorage.setItem('refresh_token', 'mock-super-admin-refresh-token');
    localStorage.setItem('user', JSON.stringify(mockSuperAdmin));

    // Set user cookie for middleware
    if (typeof window !== 'undefined') {
      document.cookie = `user=${encodeURIComponent(JSON.stringify(mockSuperAdmin))}; path=/; max-age=86400; SameSite=Lax`;
      document.cookie = `access_token=mock-super-admin-token; path=/; max-age=86400; SameSite=Lax`;
    }

    // Update state
    setUser(mockSuperAdmin);
    setIsAuthenticated(true);

    toast.success('Logged in as Super Administrator!');
  };

  const mockUserLogin = (userType: string) => {
    const mockUsers: { [key: string]: User } = {
      'company_admin': {
        id: 'mock-company-admin-001',
        email: '<EMAIL>',
        full_name: 'Company Administrator',
        first_name: 'Company',
        last_name: 'Administrator',
        role: 'c_admin',
        organization_id: 'company-org-001'
      },
      'supplier_admin': {
        id: 'mock-supplier-admin-001',
        email: '<EMAIL>',
        full_name: 'Supplier Administrator',
        first_name: 'Supplier',
        last_name: 'Administrator',
        role: 's_admin',
        organization_id: 'supplier-org-001'
      },
      'procurement_officer': {
        id: 'mock-procurement-officer-001',
        email: '<EMAIL>',
        full_name: 'John Procurement',
        first_name: 'John',
        last_name: 'Procurement',
        role: 'c_procurement_officer',
        organization_id: 'company-org-001'
      },
      'stores_manager': {
        id: 'mock-stores-manager-001',
        email: '<EMAIL>',
        full_name: 'Sarah Stores',
        first_name: 'Sarah',
        last_name: 'Stores',
        role: 'c_stores_manager',
        organization_id: 'company-org-001'
      },
      'accountant': {
        id: 'mock-accountant-001',
        email: '<EMAIL>',
        full_name: 'Mike Accountant',
        first_name: 'Mike',
        last_name: 'Accountant',
        role: 'c_accountant',
        organization_id: 'company-org-001'
      },
      'approving_manager': {
        id: 'mock-approving-manager-001',
        email: '<EMAIL>',
        full_name: 'Lisa Manager',
        first_name: 'Lisa',
        last_name: 'Manager',
        role: 'c_approving_manager',
        organization_id: 'company-org-001'
      },
      'requesting_user': {
        id: 'mock-requesting-user-001',
        email: '<EMAIL>',
        full_name: 'Bob User',
        first_name: 'Bob',
        last_name: 'User',
        role: 'c_requesting_user',
        organization_id: 'company-org-001'
      },
      'supplier_user': {
        id: 'mock-supplier-user-001',
        email: '<EMAIL>',
        full_name: 'Alice Supplier',
        first_name: 'Alice',
        last_name: 'Supplier',
        role: 'supplier_user',
        organization_id: 'supplier-org-001'
      }
    };

    const selectedUser = mockUsers[userType];
    if (!selectedUser) {
      toast.error('Invalid user type selected');
      return;
    }

    // Store mock tokens
    localStorage.setItem('access_token', `mock-${userType}-token`);
    localStorage.setItem('refresh_token', `mock-${userType}-refresh-token`);
    localStorage.setItem('user', JSON.stringify(selectedUser));

    // Set user cookie for middleware
    if (typeof window !== 'undefined') {
      document.cookie = `user=${encodeURIComponent(JSON.stringify(selectedUser))}; path=/; max-age=86400; SameSite=Lax`;
      document.cookie = `access_token=mock-${userType}-token; path=/; max-age=86400; SameSite=Lax`;
    }

    // Update state
    setUser(selectedUser);
    setIsAuthenticated(true);

    toast.success(`Logged in as ${selectedUser.full_name}!`);
  };

  const value = {
    isAuthenticated,
    login,
    signup,
    completeSetup,
    logout,
    user,
    mockSuperAdminLogin,
    mockUserLogin
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
