'use client';

import { useState, useEffect, useCallback } from 'react';

interface PreloaderState {
  isLoading: boolean;
  text: string;
  variant: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient';
}

interface UseGlobalPreloaderReturn {
  isLoading: boolean;
  text: string;
  variant: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient';
  showPreloader: (text?: string, variant?: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient') => void;
  hidePreloader: () => void;
  updateText: (text: string) => void;
  updateVariant: (variant: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient') => void;
}

let globalPreloaderState: PreloaderState = {
  isLoading: false,
  text: 'Loading...',
  variant: 'modern'
};

let listeners: Set<() => void> = new Set();

const notifyListeners = () => {
  listeners.forEach(listener => listener());
};

export const globalPreloaderActions = {
  show: (text: string = 'Loading...', variant: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient' = 'modern') => {
    globalPreloaderState = {
      isLoading: true,
      text,
      variant
    };
    notifyListeners();
  },
  
  hide: () => {
    globalPreloaderState = {
      ...globalPreloaderState,
      isLoading: false
    };
    notifyListeners();
  },
  
  updateText: (text: string) => {
    globalPreloaderState = {
      ...globalPreloaderState,
      text
    };
    notifyListeners();
  },
  
  updateVariant: (variant: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient') => {
    globalPreloaderState = {
      ...globalPreloaderState,
      variant
    };
    notifyListeners();
  }
};

export function useGlobalPreloader(): UseGlobalPreloaderReturn {
  const [state, setState] = useState<PreloaderState>(globalPreloaderState);

  useEffect(() => {
    const listener = () => {
      setState({ ...globalPreloaderState });
    };
    
    listeners.add(listener);
    
    return () => {
      listeners.delete(listener);
    };
  }, []);

  const showPreloader = useCallback((
    text: string = 'Loading...', 
    variant: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient' = 'modern'
  ) => {
    globalPreloaderActions.show(text, variant);
  }, []);

  const hidePreloader = useCallback(() => {
    globalPreloaderActions.hide();
  }, []);

  const updateText = useCallback((text: string) => {
    globalPreloaderActions.updateText(text);
  }, []);

  const updateVariant = useCallback((variant: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient') => {
    globalPreloaderActions.updateVariant(variant);
  }, []);

  return {
    isLoading: state.isLoading,
    text: state.text,
    variant: state.variant,
    showPreloader,
    hidePreloader,
    updateText,
    updateVariant
  };
}

// Utility functions for common use cases
export const preloaderUtils = {
  // Show preloader for async operations
  withPreloader: async <T>(
    asyncFn: () => Promise<T>,
    text: string = 'Loading...',
    variant: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient' = 'modern'
  ): Promise<T> => {
    globalPreloaderActions.show(text, variant);
    try {
      const result = await asyncFn();
      return result;
    } finally {
      globalPreloaderActions.hide();
    }
  },

  // Show preloader for navigation
  showForNavigation: (destination: string = '') => {
    const text = destination ? `Navigating to ${destination}...` : 'Loading page...';
    globalPreloaderActions.show(text, 'minimal');
  },

  // Show preloader for authentication
  showForAuth: (action: 'login' | 'logout' | 'signup' = 'login') => {
    const texts = {
      login: 'Signing in...',
      logout: 'Signing out...',
      signup: 'Creating account...'
    };
    globalPreloaderActions.show(texts[action], 'elegant');
  },

  // Show preloader for data operations
  showForData: (operation: 'loading' | 'saving' | 'deleting' | 'updating' = 'loading') => {
    const texts = {
      loading: 'Loading data...',
      saving: 'Saving changes...',
      deleting: 'Deleting item...',
      updating: 'Updating information...'
    };
    globalPreloaderActions.show(texts[operation], 'default');
  },

  // Show preloader for file operations
  showForFile: (operation: 'uploading' | 'downloading' | 'processing' = 'uploading') => {
    const texts = {
      uploading: 'Uploading file...',
      downloading: 'Downloading file...',
      processing: 'Processing file...'
    };
    globalPreloaderActions.show(texts[operation], 'gradient');
  }
};

// React hook for automatic preloader management
export function useAutoPreloader(
  isLoading: boolean,
  text: string = 'Loading...',
  variant: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient' = 'modern'
) {
  useEffect(() => {
    if (isLoading) {
      globalPreloaderActions.show(text, variant);
    } else {
      globalPreloaderActions.hide();
    }
  }, [isLoading, text, variant]);
}

// React hook for preloader with timeout
export function usePreloaderWithTimeout(
  timeout: number = 5000,
  text: string = 'Loading...',
  variant: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient' = 'modern'
) {
  const [isLoading, setIsLoading] = useState(false);

  const startPreloader = useCallback(() => {
    setIsLoading(true);
    globalPreloaderActions.show(text, variant);
    
    const timer = setTimeout(() => {
      setIsLoading(false);
      globalPreloaderActions.hide();
    }, timeout);

    return () => clearTimeout(timer);
  }, [timeout, text, variant]);

  const stopPreloader = useCallback(() => {
    setIsLoading(false);
    globalPreloaderActions.hide();
  }, []);

  return {
    isLoading,
    startPreloader,
    stopPreloader
  };
}
