'use client';

import React, { useState, useCallback } from 'react';
import CustomModal, { ModalType } from '@/components/ui/CustomModal';

interface ModalConfig {
  title: string;
  message: string;
  type?: ModalType;
  confirmText?: string;
  cancelText?: string;
  showCancel?: boolean;
}

interface ModalState extends ModalConfig {
  isOpen: boolean;
  onConfirm?: () => void;
}

export const useModal = () => {
  const [modalState, setModalState] = useState<ModalState>({
    isOpen: false,
    title: '',
    message: '',
    type: 'info'
  });

  const closeModal = useCallback(() => {
    setModalState(prev => ({ ...prev, isOpen: false }));
  }, []);

  // Success modal
  const showSuccess = useCallback((title: string, message: string, confirmText = 'OK') => {
    setModalState({
      isOpen: true,
      title,
      message,
      type: 'success',
      confirmText,
      showCancel: false
    });
  }, []);

  // Error modal
  const showError = useCallback((title: string, message: string, confirmText = 'OK') => {
    setModalState({
      isOpen: true,
      title,
      message,
      type: 'error',
      confirmText,
      showCancel: false
    });
  }, []);

  // Warning modal
  const showWarning = useCallback((title: string, message: string, confirmText = 'OK') => {
    setModalState({
      isOpen: true,
      title,
      message,
      type: 'warning',
      confirmText,
      showCancel: false
    });
  }, []);

  // Info modal
  const showInfo = useCallback((title: string, message: string, confirmText = 'OK') => {
    setModalState({
      isOpen: true,
      title,
      message,
      type: 'info',
      confirmText,
      showCancel: false
    });
  }, []);

  // Confirm modal
  const showConfirm = useCallback((
    title: string, 
    message: string, 
    onConfirm: () => void,
    confirmText = 'Confirm',
    cancelText = 'Cancel'
  ) => {
    setModalState({
      isOpen: true,
      title,
      message,
      type: 'confirm',
      confirmText,
      cancelText,
      showCancel: true,
      onConfirm
    });
  }, []);

  // Generic modal
  const showModal = useCallback((config: ModalConfig & { onConfirm?: () => void }) => {
    setModalState({
      isOpen: true,
      ...config
    });
  }, []);

  // Modal component
  const ModalComponent = useCallback(() => (
    <CustomModal
      isOpen={modalState.isOpen}
      onClose={closeModal}
      onConfirm={modalState.onConfirm}
      title={modalState.title}
      message={modalState.message}
      type={modalState.type}
      confirmText={modalState.confirmText}
      cancelText={modalState.cancelText}
      showCancel={modalState.showCancel}
    />
  ), [modalState, closeModal]);

  return {
    showSuccess,
    showError,
    showWarning,
    showInfo,
    showConfirm,
    showModal,
    closeModal,
    ModalComponent
  };
};

// Alternative: Global modal context for app-wide usage
export const ModalContext = React.createContext<ReturnType<typeof useModal> | null>(null);

export const ModalProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const modal = useModal();

  return (
    <ModalContext.Provider value={modal}>
      {children}
      <modal.ModalComponent />
    </ModalContext.Provider>
  );
};

export const useGlobalModal = () => {
  const context = React.useContext(ModalContext);
  if (!context) {
    throw new Error('useGlobalModal must be used within a ModalProvider');
  }
  return context;
};
