'use client';

import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';
import { fastAuthRedirect, getDashboardUrlForRole } from '@/utils/roleRedirect';

/**
 * Enhanced auth hook that provides fast routing capabilities
 * This hook automatically handles role-based redirection and provides utilities
 * for fast navigation based on user roles
 */
export function useFastAuth() {
  const { user, isAuthenticated, login, logout } = useAuth();
  const router = useRouter();

  /**
   * Perform fast login with immediate role-based redirection
   * @param email - User email
   * @param password - User password
   * @returns Promise that resolves when login and redirection are complete
   */
  const fastLogin = async (email: string, password: string): Promise<void> => {
    try {
      // Perform login
      await login(email, password);
      
      // Get updated user data from localStorage (set by login function)
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      const role = currentUser?.role;
      
      if (role) {
        // Immediately redirect to role-specific dashboard
        fastAuthRedirect(role, router, `Fast login redirect for ${role}`);
      } else {
        // Fallback to generic dashboard if no role
        router.replace('/dashboard');
      }
    } catch (error) {
      // Re-throw error to be handled by the calling component
      throw error;
    }
  };

  /**
   * Check if current user has permission to access a specific route
   * @param requiredRole - The role required to access the route
   * @returns boolean indicating if user has permission
   */
  const hasRoutePermission = (requiredRole: string): boolean => {
    return user?.role === requiredRole;
  };

  /**
   * Get the expected dashboard URL for the current user
   * @returns The dashboard URL the user should be on
   */
  const getExpectedDashboard = (): string => {
    return getDashboardUrlForRole(user?.role);
  };

  /**
   * Check if user is on their correct dashboard
   * @param currentPath - The current route path
   * @returns boolean indicating if user is on correct dashboard
   */
  const isOnCorrectDashboard = (currentPath: string): boolean => {
    const expectedDashboard = getExpectedDashboard();
    return currentPath === expectedDashboard;
  };

  /**
   * Redirect user to their correct dashboard if they're not already there
   * @param currentPath - The current route path
   */
  const ensureCorrectDashboard = (currentPath: string): void => {
    if (user?.role && !isOnCorrectDashboard(currentPath)) {
      fastAuthRedirect(user.role, router, `Correcting dashboard for ${user.role}`);
    }
  };

  return {
    // Standard auth properties
    user,
    isAuthenticated,
    logout,
    
    // Fast auth methods
    fastLogin,
    hasRoutePermission,
    getExpectedDashboard,
    isOnCorrectDashboard,
    ensureCorrectDashboard,
    
    // Utility properties
    userRole: user?.role,
    isAdmin: ['super_admin', 'admin', 's_admin', 'c_admin'].includes(user?.role || ''),
    isCompanyUser: (user?.role || '').startsWith('c_'),
    isSupplierUser: (user?.role || '').startsWith('s_') || ['supplier_user', 'supplier'].includes(user?.role || ''),
  };
}

/**
 * Hook for components that require specific role access
 * Automatically redirects users who don't have the required role
 * @param requiredRole - The role required to access this component
 * @param redirectOnFail - Whether to redirect if user doesn't have required role (default: true)
 */
export function useRoleGuard(requiredRole: string, redirectOnFail: boolean = true) {
  const { user, isAuthenticated, userRole, getExpectedDashboard } = useFastAuth();
  const router = useRouter();

  useEffect(() => {
    if (isAuthenticated === false) {
      // Not authenticated, redirect to login
      router.push('/login');
      return;
    }

    if (isAuthenticated === true && user) {
      if (!userRole) {
        console.warn('User has no role assigned');
        return;
      }

      if (userRole !== requiredRole) {
        console.warn(`User role ${userRole} does not match required role ${requiredRole}`);
        
        if (redirectOnFail) {
          // Redirect to user's appropriate dashboard
          const expectedDashboard = getExpectedDashboard();
          console.log(`Redirecting ${userRole} to ${expectedDashboard}`);
          router.replace(expectedDashboard);
        }
      }
    }
  }, [isAuthenticated, user, userRole, requiredRole, redirectOnFail, router, getExpectedDashboard]);

  return {
    hasAccess: userRole === requiredRole,
    isLoading: isAuthenticated === null,
    user,
    userRole,
  };
}
