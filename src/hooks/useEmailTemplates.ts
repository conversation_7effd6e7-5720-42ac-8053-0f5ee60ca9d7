import { useState, useCallback } from 'react';
import { 
  ApiEmailTemplate, 
  CreateEmailTemplateDto,
  listEmailTemplates, 
  getEmailTemplate, 
  createEmailTemplate, 
  updateEmailTemplate, 
  deleteEmailTemplate,
  validateSlug,
  generateSlug
} from '@/services/emailTemplates';

interface UseEmailTemplatesReturn {
  templates: ApiEmailTemplate[];
  currentTemplate: ApiEmailTemplate | null;
  isLoading: boolean;
  error: Error | null;
  fetchTemplates: () => Promise<ApiEmailTemplate[]>;
  fetchTemplate: (slug: string) => Promise<ApiEmailTemplate>;
  createTemplate: (data: Omit<CreateEmailTemplateDto, 'slug'>, generateFromSubject?: boolean) => Promise<ApiEmailTemplate>;
  updateTemplate: (slug: string, data: Partial<CreateEmailTemplateDto>) => Promise<ApiEmailTemplate>;
  removeTemplate: (slug: string) => Promise<void>;
  validateSlug: (slug: string) => boolean;
  generateSlug: (subject: string) => string;
}

export const useEmailTemplates = (): UseEmailTemplatesReturn => {
  const [templates, setTemplates] = useState<ApiEmailTemplate[]>([]);
  const [currentTemplate, setCurrentTemplate] = useState<ApiEmailTemplate | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // Fetch all templates
  const fetchTemplates = useCallback(async () => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await listEmailTemplates();
      setTemplates(data.items);
      return data.items;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch templates');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Fetch a single template by slug
  const fetchTemplate = useCallback(async (slug: string) => {
    setIsLoading(true);
    setError(null);
    try {
      const data = await getEmailTemplate(slug);
      setCurrentTemplate(data);
      return data;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to fetch template');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Create a new template
  const createTemplate = useCallback(async (
    templateData: Omit<CreateEmailTemplateDto, 'slug'>,
    generateFromSubject = false
  ): Promise<ApiEmailTemplate> => {
    setIsLoading(true);
    setError(null);
    
    try {
      const slug = generateFromSubject 
        ? generateSlug(templateData.subject)
        : generateSlug(templateData.subject);
      
      if (!validateSlug(slug)) {
        throw new Error('Invalid slug format. Must be 3-100 characters long.');
      }
      
      if (!templateData.subject || templateData.subject.length < 1 || templateData.subject.length > 255) {
        throw new Error('Subject must be between 1 and 255 characters long.');
      }
      
      const newTemplate = await createEmailTemplate({
        ...templateData,
        slug,
      });
      
      setTemplates(prev => [...prev, newTemplate]);
      return newTemplate;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to create template');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  // Update an existing template
  const updateTemplate = useCallback(async (
    slug: string, 
    data: Partial<CreateEmailTemplateDto>
  ): Promise<ApiEmailTemplate> => {
    setIsLoading(true);
    setError(null);
    
    try {
      if (data.slug && !validateSlug(data.slug)) {
        throw new Error('Invalid slug format. Must be 3-100 characters long.');
      }
      
      if (data.subject && (data.subject.length < 1 || data.subject.length > 255)) {
        throw new Error('Subject must be between 1 and 255 characters long.');
      }
      
      const updatedTemplate = await updateEmailTemplate(slug, data);
      
      setTemplates(prev => 
        prev.map(t => t.slug === slug ? updatedTemplate : t)
      );
      
      if (currentTemplate?.slug === slug) {
        setCurrentTemplate(updatedTemplate);
      }
      
      return updatedTemplate;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to update template');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [currentTemplate]);

  // Delete a template
  const removeTemplate = useCallback(async (slug: string): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      await deleteEmailTemplate(slug);
      setTemplates(prev => prev.filter(t => t.slug !== slug));
      
      if (currentTemplate?.slug === slug) {
        setCurrentTemplate(null);
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to delete template');
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [currentTemplate]);

  return {
    templates,
    currentTemplate,
    isLoading,
    error,
    fetchTemplates,
    fetchTemplate,
    createTemplate,
    updateTemplate,
    removeTemplate,
    validateSlug,
    generateSlug,
  };
};

export default useEmailTemplates;
