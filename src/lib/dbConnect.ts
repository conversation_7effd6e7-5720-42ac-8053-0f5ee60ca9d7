import mongoose, { Mongoose } from 'mongoose';

const MONGODB_URI = process.env.MONGODB_URI;

// Don't throw error during build time - allow graceful degradation
if (!MONGODB_URI && process.env.NODE_ENV !== 'production') {
  console.warn(
    'Warning: MONGODB_URI environment variable not defined. MongoDB features will be disabled.'
  );
}

// Augment the NodeJS global type with our mongoose cache
declare global {
  var mongoose: {
    promise: Promise<Mongoose> | null;
    conn: Mongoose | null;
  };
}

let cached = global.mongoose;

if (!cached) {
  cached = global.mongoose = { conn: null, promise: null };
}

async function dbConnect(): Promise<Mongoose> {
  // Check if MongoDB URI is available
  if (!MONGODB_URI) {
    throw new Error(
      'MongoDB connection not available: MONGODB_URI environment variable not defined'
    );
  }

  if (cached.conn) {
    console.log('=> Using cached database connection');
    return cached.conn;
  }

  if (!cached.promise) {
    const opts = {
      bufferCommands: false,
    };
    console.log('=> Creating new database connection');
    cached.promise = mongoose.connect(MONGODB_URI, opts).then((mongoose) => {
      return mongoose;
    });
  }

  try {
    cached.conn = await cached.promise;
  } catch (e) {
    cached.promise = null;
    throw e;
  }

  return cached.conn;
}

export default dbConnect;
