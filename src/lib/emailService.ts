import nodemailer from 'nodemailer';

// Email service configuration
const emailConfig = {
  host: process.env.SMTP_HOST || 'smtp.gmail.com',
  port: parseInt(process.env.SMTP_PORT || '587'),
  secure: false, // true for 465, false for other ports like 587
  requireTLS: true, // Use STARTTLS for port 587
  auth: {
    user: process.env.SMTP_USER,
    pass: process.env.SMTP_PASS,
  },
  tls: {
    // Do not fail on invalid certs
    rejectUnauthorized: false
  }
};

// Create reusable transporter object using SMTP transport
let transporter: nodemailer.Transporter | null = null;

function getTransporter() {
  if (!transporter) {
    console.log('Creating SMTP transporter with config:', {
      host: emailConfig.host,
      port: emailConfig.port,
      secure: emailConfig.secure,
      user: emailConfig.auth.user ? `${emailConfig.auth.user.substring(0, 3)}***` : 'not set'
    });
    
    transporter = nodemailer.createTransport(emailConfig);
  }
  return transporter;
}

// Verify SMTP connection
export async function verifyEmailConnection(): Promise<boolean> {
  try {
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      console.error('SMTP credentials not configured');
      return false;
    }

    const transporter = getTransporter();
    await transporter.verify();
    console.log('✅ SMTP connection verified successfully');
    return true;
  } catch (error: any) {
    console.error('❌ SMTP connection failed:', error.message);
    return false;
  }
}

// Send invitation email
export async function sendInvitationEmail({
  to,
  invitedBy,
  invitationUrl,
  message,
  organizationName = 'Ascension',
  learnMoreUrl = 'https://ascensionservices.net'
}: {
  to: string;
  invitedBy: string;
  invitationUrl: string;
  message?: string;
  organizationName?: string;
  learnMoreUrl?: string;
}): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    console.log(`📧 Sending invitation email to ${to}...`);

    // Check if SMTP is configured
    if (!process.env.SMTP_USER || !process.env.SMTP_PASS) {
      throw new Error('SMTP credentials not configured');
    }

    const transporter = getTransporter();

    // Email content
    const subject = `Invitation to Join ${organizationName} Supplier Network`;
    
    const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>You've been invited to Ascension</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Helvetica, Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            margin: 0;
            padding: 0;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 600px;
            margin: 40px auto;
            background-color: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .header {
            padding: 40px 40px 20px 40px;
            text-align: left;
        }
        .logo {
            display: flex;
            align-items: center;
            margin-bottom: 30px;
        }
        .logo-icon {
            width: 24px;
            height: 24px;
            background-color: #18546c;
            border-radius: 4px;
            margin-right: 8px;
            position: relative;
        }
        .logo-icon::before {
            content: '';
            position: absolute;
            top: 6px;
            left: 6px;
            width: 0;
            height: 0;
            border-left: 6px solid white;
            border-right: 6px solid transparent;
            border-bottom: 6px solid transparent;
            border-top: 6px solid transparent;
        }
        .logo-text {
            font-size: 18px;
            font-weight: 600;
            color: #18546c;
            letter-spacing: 0.5px;
        }
        .title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin: 0 0 30px 0;
            text-align: center;
        }
        .content {
            padding: 0 40px 40px 40px;
            color: #666;
            line-height: 1.6;
        }
        .greeting {
            margin-bottom: 20px;
        }
        .invitation-text {
            margin-bottom: 20px;
        }
        .button-container {
            text-align: left;
            margin: 30px 0;
        }
        .button {
            display: inline-block;
            background-color: #18546c;
            color: white !important;
            padding: 12px 24px;
            text-decoration: none;
            border-radius: 6px;
            font-weight: 500;
            font-size: 14px;
        }
        .button:hover {
            background-color: #1a6985;
        }
        .description {
            margin: 30px 0;
            padding: 20px;
            background-color: #f8f9fa;
            border-radius: 6px;
            border-left: 4px solid #18546c;
        }
        .description-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        .footer-text {
            margin-top: 30px;
            font-style: italic;
            color: #888;
        }
        .footer {
            padding: 20px 40px;
            text-align: center;
            color: #888;
            font-size: 12px;
            background-color: #f8f9fa;
            border-top: 1px solid #eee;
        }
        .company-tagline {
            margin-top: 10px;
            font-weight: 500;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">
                <div class="logo-icon"></div>
                <div class="logo-text">ASCENSION</div>
            </div>
            <h1 class="title">You've been invited to Ascension</h1>
        </div>
        <div class="content">
            <div class="greeting">Hi,</div>

            <div class="invitation-text">
                You have been invited by <strong>${invitedBy}</strong> from <strong>${organizationName}</strong> to sign up in to Ascension.
            </div>

            <div class="button-container">
                <a href="${invitationUrl}" class="button">Sign Up</a>
            </div>

            <div class="description">
                <div class="description-title">What is Ascension?</div>
                <div>Imagine a platform where your business portfolio is visible to any type of client 24/7. Imagine that they can prompt you to quotations, send you purchase orders and receive your invoices for payment instantly. Now imagine that you can access this platform from any computer through the web. That's Ascension! <a href="${learnMoreUrl}" style="color: #18546c; text-decoration: none;">Learn more</a>.</div>
            </div>

            ${message ? `<div style="margin: 20px 0; padding: 15px; background-color: #e8f4f8; border-radius: 6px; border-left: 4px solid #18546c;"><em>"${message}"</em></div>` : ''}

            <div class="footer-text">
                Seamless procurement, better collaboration with your clients awaits!
            </div>
        </div>
        <div class="footer">
            <div>This message was sent to you by Ascension.</div>
            <div class="company-tagline">Ascension. Intuitive purchasing.</div>
        </div>
    </div>
</body>
</html>
    `;

    const textContent = `
ASCENSION

You've been invited to Ascension

Hi,

You have been invited by ${invitedBy} from ${organizationName} to sign up in to Ascension.

Sign Up: ${invitationUrl}

What is Ascension?
Imagine a platform where your business portfolio is visible to any type of client 24/7. Imagine that they can prompt you to quotations, send you purchase orders and receive your invoices for payment instantly. Now imagine that you can access this platform from any computer through the web. That's Ascension!

${message ? `"${message}"` : ''}

Seamless procurement, better collaboration with your clients awaits!

---
This message was sent to you by Ascension.
Ascension. Intuitive purchasing.
    `;

    // Send email
    const info = await transporter.sendMail({
      from: `"${process.env.SMTP_FROM_NAME || 'Ascension'}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
      to: to,
      subject: subject,
      text: textContent,
      html: htmlContent,
    });

    console.log('✅ Email sent successfully:', info.messageId);
    return { success: true, messageId: info.messageId };

  } catch (error: any) {
    console.error('❌ Failed to send email:', error.message);
    return { success: false, error: error.message };
  }
}

// Test email function
export async function sendTestEmail(to: string): Promise<{ success: boolean; messageId?: string; error?: string }> {
  try {
    console.log(`📧 Sending test email to ${to}...`);

    const transporter = getTransporter();

    const info = await transporter.sendMail({
      from: `"${process.env.SMTP_FROM_NAME || 'Ascension'}" <${process.env.SMTP_FROM || process.env.SMTP_USER}>`,
      to: to,
      subject: 'Test Email from Ascension System',
      text: 'This is a test email to verify SMTP configuration is working correctly.',
      html: `
        <h2>Test Email</h2>
        <p>This is a test email to verify SMTP configuration is working correctly.</p>
        <p>If you received this email, your SMTP settings are configured properly!</p>
        <p>Sent at: ${new Date().toLocaleString()}</p>
      `,
    });

    console.log('✅ Test email sent successfully:', info.messageId);
    return { success: true, messageId: info.messageId };

  } catch (error: any) {
    console.error('❌ Failed to send test email:', error.message);
    return { success: false, error: error.message };
  }
}
