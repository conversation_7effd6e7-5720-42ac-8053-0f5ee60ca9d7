import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}

/**
 * <PERSON><PERSON> generating a secure verification token.
 * In a real application, this would use a library like `crypto`.
 * @param email The user's email to associate with the token.
 * @returns A mock verification token.
 */
export function generateVerificationToken(email: string): string {
  // Create a simple base64 encoded token for mock purposes
  const tokenPayload = `${email}:${Date.now()}`;
  return Buffer.from(tokenPayload).toString('base64');
}

/**
 * <PERSON>cks sending a verification email.
 * In a real application, this would use an email service (e.g., SendGrid, AWS SES).
 * @param email The recipient's email address.
 * @param token The verification token to include in the email.
 * @returns A promise that resolves to true to simulate successful sending.
 */
export async function sendVerificationEmail(email: string, token: string): Promise<boolean> {
  console.log('--- <PERSON>OCK EMAIL ---');
  console.log(`Sending verification email to: ${email}`);
  console.log('Verification Link:', `http://localhost:3000/verify-email?token=${token}`);
  console.log('------------------');
  
  // Simulate network delay
  await new Promise(resolve => setTimeout(resolve, 500));
  
  // In a real app, you would handle potential errors from the email service.
  // For this mock, we'll always assume it's successful.
  return true;
}
