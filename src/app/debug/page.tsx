'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function DebugPage() {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [token, setToken] = useState<string | null>(null);
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  
  // Load token and user data when component mounts
  useEffect(() => {
    const storedToken = localStorage.getItem('access_token');
    if (storedToken) {
      setToken(storedToken.substring(0, 10) + '...');
    }
    
    try {
      const userDataString = localStorage.getItem('user');
      if (userDataString) {
        const userData = JSON.parse(userDataString);
        if (userData?.organization_id) {
          setOrganizationId(userData.organization_id);
          console.log('Using organization ID:', userData.organization_id);
        }
      }
    } catch (err) {
      console.error('Error loading user data:', err);
    }
  }, []);
  
  const runDebugTest = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const authToken = localStorage.getItem('access_token');
      if (!authToken) {
        throw new Error('Authentication token not found. Please log in first.');
      }
      
      // Create payload with organization ID if we have it
      const payload: any = {};
      if (organizationId) {
        payload.organization_id = organizationId;
      }
      
      // Call our debug endpoint
      const response = await fetch('/api/debug-invitation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${authToken}`
        },
        body: JSON.stringify(payload)
      });
      
      const data = await response.json();
      setResults(data);
      
      // Log to console for additional visibility
      console.log('Debug API results:', data);
    } catch (err: any) {
      setError(`Error: ${err.message}`);
      console.error('Debug test error:', err);
    } finally {
      setLoading(false);
    }
  };
  
  // Helper function to render API test result
  const renderResult = (title: string, result: any) => {
    if (!result) return null;
    
    return (
      <div className="mb-6 border rounded-lg p-4">
        <h3 className="font-semibold text-lg mb-2">{title}</h3>
        
        <div className={`text-sm ${result.success ? 'text-green-600' : 'text-red-600'}`}>
          Status: {result.status || 'N/A'}
          {result.error && <span> (Error: {result.error})</span>}
        </div>
        
        {result.text && (
          <div className="mt-2">
            <p className="text-xs font-semibold">Response:</p>
            <pre className="text-xs bg-gray-50 p-2 rounded mt-1 overflow-x-auto">
              {result.text}
            </pre>
          </div>
        )}
      </div>
    );
  };
  
  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">API Debug Tool</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <div className="mb-4">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Auth Token</label>
              <div className="text-sm p-2 bg-gray-50 rounded">
                {token || 'Not found'}
              </div>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Organization ID</label>
              <div className="text-sm p-2 bg-gray-50 rounded">
                {organizationId || 'Not found'}
              </div>
            </div>
          </div>
        </div>
        
        <Button 
          onClick={runDebugTest}
          disabled={loading}
          className="w-full"
        >
          {loading ? 'Testing All API Formats...' : 'Run API Format Tests'}
        </Button>
        
        <p className="mt-2 text-xs text-gray-500">
          This will test the invitation API with JSON, form-urlencoded, and multipart formats
        </p>
      </div>
      
      {error && (
        <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-6">
          <p className="text-red-600">{error}</p>
        </div>
      )}
      
      {results && (
        <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <h2 className="text-lg font-semibold mb-4">Test Results</h2>
          
          {renderResult('1. JSON Format Test', results.jsonFormat)}
          {renderResult('2. Form URL-Encoded Format Test', results.formUrlEncoded)}
          {renderResult('3. Multipart Form Data Test', results.multipart)}
        </div>
      )}
    </div>
  );
}
