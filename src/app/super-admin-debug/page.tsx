'use client';

import React from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';

export default function SuperAdminDebugPage() {
  const { user, isAuthenticated, mockSuperAdminLogin, mockUserLogin, logout } = useAuth();
  const router = useRouter();

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Super Admin Debug Page</h1>
      
      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-lg font-semibold mb-4">Authentication Status</h2>
        <div className="space-y-2">
          <p><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</p>
          {user && (
            <>
              <p><strong>User ID:</strong> {user.id}</p>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Name:</strong> {user.full_name}</p>
              <p><strong>Role:</strong> <span className={`px-2 py-1 rounded text-sm ${user.role === 'super_admin' ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'}`}>{user.role}</span></p>
              <p><strong>Organization ID:</strong> {user.organization_id || user.organisation_id || 'N/A'}</p>
            </>
          )}
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-lg font-semibold mb-4">Super Admin Access</h2>
        <div className="space-y-3">
          <Button
            onClick={() => {
              mockSuperAdminLogin();
              router.push('/super-admin-dashboard');
            }}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            🔧 Login as Super Admin & Go to Dashboard
          </Button>

          <Button
            onClick={() => mockSuperAdminLogin()}
            variant="outline"
            className="w-full"
          >
            🔧 Login as Super Admin (Stay Here)
          </Button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-lg font-semibold mb-4">Mock User Logins</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
          <Button
            onClick={() => {
              mockUserLogin('company_admin');
              router.push('/company-dashboard');
            }}
            variant="outline"
            className="w-full text-left justify-start"
          >
            🏢 Company Admin
          </Button>

          <Button
            onClick={() => {
              mockUserLogin('supplier_admin');
              router.push('/supplier-dashboard');
            }}
            variant="outline"
            className="w-full text-left justify-start"
          >
            🚚 Supplier Admin
          </Button>

          <Button
            onClick={() => {
              mockUserLogin('procurement_officer');
              router.push('/procurement-officer-dashboard');
            }}
            variant="outline"
            className="w-full text-left justify-start"
          >
            📋 Procurement Officer
          </Button>

          <Button
            onClick={() => {
              mockUserLogin('stores_manager');
              router.push('/stores-manager-dashboard');
            }}
            variant="outline"
            className="w-full text-left justify-start"
          >
            📦 Stores Manager
          </Button>

          <Button
            onClick={() => {
              mockUserLogin('accountant');
              router.push('/accountant-dashboard');
            }}
            variant="outline"
            className="w-full text-left justify-start"
          >
            💰 Accountant
          </Button>

          <Button
            onClick={() => {
              mockUserLogin('approving_manager');
              router.push('/approving-manager-dashboard');
            }}
            variant="outline"
            className="w-full text-left justify-start"
          >
            ✅ Approving Manager
          </Button>

          <Button
            onClick={() => {
              mockUserLogin('requesting_user');
              router.push('/requesting-user-dashboard');
            }}
            variant="outline"
            className="w-full text-left justify-start"
          >
            👤 Requesting User
          </Button>

          <Button
            onClick={() => {
              mockUserLogin('supplier_user');
              router.push('/supplier-user-dashboard');
            }}
            variant="outline"
            className="w-full text-left justify-start"
          >
            🏭 Supplier User
          </Button>
        </div>
      </div>

      <div className="bg-white dark:bg-gray-800 p-6 rounded-lg shadow-md mb-6">
        <h2 className="text-lg font-semibold mb-4">Navigation</h2>
        <div className="space-y-3">
          {isAuthenticated && (
            <Button
              onClick={logout}
              variant="destructive"
              className="w-full"
            >
              🚪 Logout
            </Button>
          )}

          <Button
            onClick={() => router.push('/super-admin-dashboard')}
            variant="outline"
            className="w-full"
          >
            📊 Go to Super Admin Dashboard
          </Button>

          <Button
            onClick={() => router.push('/login')}
            variant="outline"
            className="w-full"
          >
            🔐 Go to Login Page
          </Button>
        </div>
      </div>

      <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 p-4 rounded-lg">
        <p className="text-yellow-800 dark:text-yellow-200 text-sm">
          <strong>Note:</strong> This is a development debug page. The mock login creates a fake super admin user for testing purposes.
        </p>
      </div>

      {/* Access Instructions */}
      <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 p-4 rounded-lg mt-6">
        <h3 className="font-semibold text-blue-800 dark:text-blue-200 mb-2">How to Access Super Admin Dashboard:</h3>
        <ol className="text-blue-700 dark:text-blue-300 text-sm space-y-1 list-decimal list-inside">
          <li>Click "Login as Super Admin & Go to Dashboard" above, OR</li>
          <li>Go to the login page and click "🔧 Mock Super Admin Login (Dev)", OR</li>
          <li>Visit <code className="bg-blue-100 dark:bg-blue-800 px-1 rounded">/super-admin-debug</code> (this page) and use the buttons</li>
        </ol>
      </div>
    </div>
  );
}
