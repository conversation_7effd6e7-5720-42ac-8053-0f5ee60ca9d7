'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useGlobalModal } from '@/hooks/useModal';

export default function TestModalsPage() {
  const { showSuccess, showError, showWarning, showInfo, showConfirm } = useGlobalModal();

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>🎨 Custom Modal Design Test</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          
          <div>
            <h3 className="text-lg font-semibold mb-4">Test Different Modal Types</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              
              {/* Success Modal */}
              <Button
                onClick={() => showSuccess(
                  'Success!', 
                  'Your operation completed successfully. All data has been saved and processed.'
                )}
                className="bg-green-600 hover:bg-green-700"
              >
                ✅ Success Modal
              </Button>

              {/* Error Modal */}
              <Button
                onClick={() => showError(
                  'Error Occurred', 
                  'Something went wrong while processing your request. Please try again or contact support if the problem persists.'
                )}
                className="bg-red-600 hover:bg-red-700"
              >
                ❌ Error Modal
              </Button>

              {/* Warning Modal */}
              <Button
                onClick={() => showWarning(
                  'Warning', 
                  'This action may have unintended consequences. Please review your settings before proceeding.'
                )}
                className="bg-yellow-600 hover:bg-yellow-700"
              >
                ⚠️ Warning Modal
              </Button>

              {/* Info Modal */}
              <Button
                onClick={() => showInfo(
                  'Information', 
                  'Here is some important information you should know about this feature and how it works.'
                )}
                className="bg-blue-600 hover:bg-blue-700"
              >
                ℹ️ Info Modal
              </Button>

              {/* Confirm Modal */}
              <Button
                onClick={() => showConfirm(
                  'Confirm Action', 
                  'Are you sure you want to delete this item? This action cannot be undone.',
                  () => {
                    showSuccess('Deleted!', 'The item has been successfully deleted.');
                  },
                  'Delete',
                  'Cancel'
                )}
                className="bg-orange-600 hover:bg-orange-700"
              >
                🗑️ Confirm Modal
              </Button>

              {/* Long Content Modal */}
              <Button
                onClick={() => showInfo(
                  'Long Content Test', 
                  `This is a test of how the modal handles longer content.\n\nLorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua.\n\nUt enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.\n\nDuis aute irure dolor in reprehenderit in voluptate velit esse cillum dolore eu fugiat nulla pariatur.`
                )}
                className="bg-purple-600 hover:bg-purple-700"
              >
                📄 Long Content
              </Button>
            </div>
          </div>

          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold mb-4">Modal Features</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">✨ Design Features:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• High z-index (9999) - Always on top</li>
                  <li>• Backdrop blur effect</li>
                  <li>• Smooth animations (fade + zoom)</li>
                  <li>• Color-coded by type</li>
                  <li>• Professional shadows</li>
                  <li>• Responsive design</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">🎯 Functionality:</h4>
                <ul className="text-sm text-gray-600 space-y-1">
                  <li>• Escape key to close</li>
                  <li>• Click backdrop to close</li>
                  <li>• Prevents body scroll</li>
                  <li>• Customizable buttons</li>
                  <li>• Multi-line text support</li>
                  <li>• Global provider pattern</li>
                </ul>
              </div>
            </div>
          </div>

          <div className="border-t pt-6">
            <h3 className="text-lg font-semibold mb-4">Usage Examples</h3>
            <div className="bg-gray-100 p-4 rounded-lg">
              <pre className="text-sm overflow-x-auto">
{`// Import the hook
import { useGlobalModal } from '@/hooks/useModal';

// Use in component
const { showSuccess, showError, showConfirm } = useGlobalModal();

// Show success
showSuccess('Title', 'Message');

// Show confirmation
showConfirm('Title', 'Message', () => {
  // Confirm action
}, 'Confirm', 'Cancel');`}
              </pre>
            </div>
          </div>

        </CardContent>
      </Card>
    </div>
  );
}
