'use client';

import React, { useState, useEffect, useCallback } from 'react';
import { useParams, useRouter } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function AcceptInvitationPage() {
  const params = useParams();
  const router = useRouter();
  const invitationId = params.invitationId as string;
  
  const [invitation, setInvitation] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [accepting, setAccepting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  // Form data for accepting invitation
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    password: '',
    confirmPassword: '',
    companyName: '',
    phoneNumber: ''
  });

  const fetchInvitation = useCallback(async () => {
    try {
      // For now, we'll simulate fetching the invitation
      // In a real implementation, you'd call an API to get invitation details
      const mockInvitation = {
        id: invitationId,
        email: '<EMAIL>', // This would come from the API
        role: 'supplier',
        message: 'You have been invited to join our supplier network.',
        invited_by_name: 'Paul kale',
        organization_name: 'Ascension',
        status: 'pending',
        expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString()
      };
      
      setInvitation(mockInvitation);
      setLoading(false);
    } catch (err: any) {
      setError('Failed to load invitation details');
      setLoading(false);
    }
  }, [invitationId]);

  useEffect(() => {
    if (invitationId) {
      fetchInvitation();
    }
  }, [invitationId, fetchInvitation]);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleAcceptInvitation = async () => {
    setAccepting(true);
    setError(null);

    try {
      // Validate form
      if (!formData.firstName || !formData.lastName || !formData.password) {
        throw new Error('Please fill in all required fields');
      }

      if (formData.password !== formData.confirmPassword) {
        throw new Error('Passwords do not match');
      }

      if (formData.password.length < 6) {
        throw new Error('Password must be at least 6 characters long');
      }

      // In a real implementation, you would:
      // 1. Call the backend API to accept the invitation
      // 2. Create the user account
      // 3. Set up the user in the organization
      
      console.log('Accepting invitation with data:', {
        invitationId,
        ...formData,
        email: invitation.email
      });

      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Show success and redirect
      alert('Invitation accepted successfully! You can now log in with your credentials.');
      router.push('/login');

    } catch (err: any) {
      setError(err.message);
    } finally {
      setAccepting(false);
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#18546c] mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading invitation...</p>
        </div>
      </div>
    );
  }

  if (error && !invitation) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="text-center">
              <div className="text-red-500 text-4xl mb-4">⚠️</div>
              <h2 className="text-xl font-semibold text-gray-900 mb-2">Invalid Invitation</h2>
              <p className="text-gray-600 mb-4">{error}</p>
              <Button onClick={() => router.push('/login')}>
                Go to Login
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle className="text-center">
            <div className="text-[#18546c] text-3xl mb-2">📧</div>
            Accept Invitation
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          
          {/* Invitation Details */}
          <div className="bg-blue-50 border border-blue-200 rounded-md p-4">
            <h3 className="font-medium text-blue-800 mb-2">You&apos;re Invited!</h3>
            <div className="text-sm text-blue-700 space-y-1">
              <p><strong>Email:</strong> {invitation.email}</p>
              <p><strong>Role:</strong> {invitation.role}</p>
              <p><strong>Invited by:</strong> {invitation.invited_by_name}</p>
              <p><strong>Organization:</strong> {invitation.organization_name}</p>
            </div>
            {invitation.message && (
              <div className="mt-3 p-2 bg-white border border-blue-200 rounded text-sm">
                {invitation.message}
              </div>
            )}
          </div>

          {/* Accept Form */}
          <div className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  First Name *
                </label>
                <Input
                  type="text"
                  value={formData.firstName}
                  onChange={(e) => handleInputChange('firstName', e.target.value)}
                  placeholder="John"
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Last Name *
                </label>
                <Input
                  type="text"
                  value={formData.lastName}
                  onChange={(e) => handleInputChange('lastName', e.target.value)}
                  placeholder="Doe"
                />
              </div>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Company Name
              </label>
              <Input
                type="text"
                value={formData.companyName}
                onChange={(e) => handleInputChange('companyName', e.target.value)}
                placeholder="Your Company"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Phone Number
              </label>
              <Input
                type="tel"
                value={formData.phoneNumber}
                onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
                placeholder="+****************"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Password *
              </label>
              <Input
                type="password"
                value={formData.password}
                onChange={(e) => handleInputChange('password', e.target.value)}
                placeholder="Choose a secure password"
              />
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Confirm Password *
              </label>
              <Input
                type="password"
                value={formData.confirmPassword}
                onChange={(e) => handleInputChange('confirmPassword', e.target.value)}
                placeholder="Confirm your password"
              />
            </div>
          </div>

          {error && (
            <div className="p-3 bg-red-50 border border-red-200 rounded-md">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}

          <Button
            onClick={handleAcceptInvitation}
            disabled={accepting}
            className="w-full bg-[#18546c] hover:bg-[#1a6985]"
          >
            {accepting ? 'Creating Account...' : 'Accept Invitation & Create Account'}
          </Button>

          <div className="text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{' '}
              <button
                onClick={() => router.push('/login')}
                className="text-[#18546c] hover:underline"
              >
                Sign in here
              </button>
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
