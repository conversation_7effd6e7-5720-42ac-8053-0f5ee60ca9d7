'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { toast } from 'sonner';

interface InvitationFormData {
  email: string;
  role: string;
  message: string;
}

export default function CustomInvitationsPage() {
  const [invitations, setInvitations] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [formData, setFormData] = useState<InvitationFormData>({
    email: '',
    role: 'supplier',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);

  const fetchInvitations = async () => {
    setLoading(true);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No access token found');
      }

      const response = await fetch('/api/custom-invitations', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        }
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch invitations: ${response.status}`);
      }

      const data = await response.json();
      setInvitations(data.invitations || []);
      console.log('Custom invitations:', data);
    } catch (error: any) {
      console.error('Error fetching invitations:', error);
      alert(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchInvitations();
  }, []);

  const copyInvitationUrl = (url: string) => {
    navigator.clipboard.writeText(url);
    alert('Invitation URL copied to clipboard!');
  };

  const sendEmail = async (invitation: any) => {
    try {
      const response = await fetch('/api/send-invitation-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: invitation.email,
          invitationUrl: invitation.invitation_url,
          invitedBy: invitation.invited_by_name,
          message: invitation.message
        })
      });

      if (response.ok) {
        const data = await response.json();
        // Open mailto link
        window.location.href = data.mailtoLink;
      } else {
        alert('Failed to generate email content');
      }
    } catch (error) {
      console.error('Email generation error:', error);
      alert('Failed to generate email content');
    }
  };

  const copyEmailContent = async (invitation: any) => {
    try {
      const response = await fetch('/api/send-invitation-email', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          email: invitation.email,
          invitationUrl: invitation.invitation_url,
          invitedBy: invitation.invited_by_name,
          message: invitation.message
        })
      });

      if (response.ok) {
        const data = await response.json();
        const emailContent = `To: ${data.email}\nSubject: ${data.subject}\n\n${data.body}`;
        navigator.clipboard.writeText(emailContent);
        alert('Email content copied to clipboard! You can paste it into your email client.');
      } else {
        alert('Failed to generate email content');
      }
    } catch (error) {
      console.error('Email generation error:', error);
      alert('Failed to generate email content');
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (value: string) => {
    setFormData(prev => ({ ...prev, role: value }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!formData.email) {
      toast.error('Email is required');
      return;
    }

    setIsSubmitting(true);
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No access token found');
      }

      const response = await fetch('/api/custom-invitations', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (!response.ok) {
        throw new Error(`Failed to create invitation: ${response.status}`);
      }

      const data = await response.json();
      toast.success('Invitation created successfully');
      
      // Clear form
      setFormData({
        email: '',
        role: 'supplier',
        message: ''
      });
      
      // Refresh invitations list
      fetchInvitations();
      
      // Open mailto link to send the email immediately
      if (data.mailtoLink) {
        window.location.href = data.mailtoLink;
      }
    } catch (error: any) {
      console.error('Error creating invitation:', error);
      toast.error(`Error: ${error.message}`);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-6xl">
      <Card className="mb-6">
        <CardHeader>
          <CardTitle>Create New Invitation</CardTitle>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <Label htmlFor="email">Email Address <span className="text-red-500">*</span></Label>
                <Input 
                  id="email" 
                  name="email" 
                  type="email" 
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleInputChange}
                  required
                />
              </div>
              
              <div>
                <Label htmlFor="role">Role</Label>
                <Select 
                  value={formData.role} 
                  onValueChange={handleSelectChange}
                >
                  <SelectTrigger id="role">
                    <SelectValue placeholder="Select a role" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="supplier">Supplier</SelectItem>
                    <SelectItem value="customer">Customer</SelectItem>
                    <SelectItem value="partner">Partner</SelectItem>
                    <SelectItem value="admin">Admin</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
            
            <div>
              <Label htmlFor="message">Custom Message (Optional)</Label>
              <Textarea
                id="message"
                name="message"
                placeholder="Add a personalized message to the invitation email..."
                value={formData.message}
                onChange={handleInputChange}
                rows={3}
              />
            </div>
            
            <div className="flex justify-end">
              <Button 
                type="submit" 
                disabled={isSubmitting}
                className="bg-[#18546c] hover:bg-[#12414f]"
              >
                {isSubmitting ? 'Creating...' : 'Create & Send Invitation'}
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
      
      <Card>
        <CardHeader>
          <CardTitle className="flex justify-between items-center">
            Existing Invitations
            <Button onClick={fetchInvitations} disabled={loading}>
              {loading ? 'Loading...' : 'Refresh'}
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          {invitations.length === 0 ? (
            <div className="text-center py-8 text-gray-500">
              <p>No custom invitations found.</p>
              <p className="text-sm mt-2">
                Custom invitations are created when the standard API doesn&apos;t allow sending invitations.
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {invitations.map((invitation) => (
                <div key={invitation.id} className="border rounded-lg p-4 bg-gray-50">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h3 className="font-medium text-lg">{invitation.email}</h3>
                      <div className="text-sm text-gray-600 space-y-1 mt-2">
                        <p><strong>Role:</strong> {invitation.role}</p>
                        <p><strong>Status:</strong> 
                          <span className={`ml-1 px-2 py-1 rounded text-xs ${
                            invitation.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            invitation.status === 'accepted' ? 'bg-green-100 text-green-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {invitation.status}
                          </span>
                        </p>
                        <p><strong>Invited by:</strong> {invitation.invited_by_name} ({invitation.invited_by})</p>
                        <p><strong>Created:</strong> {new Date(invitation.created_at).toLocaleString()}</p>
                        <p><strong>Expires:</strong> {new Date(invitation.expires_at).toLocaleString()}</p>
                      </div>
                    </div>
                    
                    <div>
                      <div className="space-y-2">
                        <div>
                          <label className="text-sm font-medium text-gray-700">Invitation URL:</label>
                          <div className="flex gap-2 mt-1">
                            <input
                              type="text"
                              value={invitation.invitation_url}
                              readOnly
                              className="flex-1 text-xs p-2 border rounded bg-white"
                            />
                            <Button
                              size="sm"
                              onClick={() => copyInvitationUrl(invitation.invitation_url)}
                            >
                              Copy URL
                            </Button>
                          </div>
                        </div>

                        <div>
                          <label className="text-sm font-medium text-gray-700">Send Email:</label>
                          <div className="flex gap-2 mt-1">
                            <Button
                              size="sm"
                              onClick={() => sendEmail(invitation)}
                              className="bg-blue-600 hover:bg-blue-700"
                            >
                              📧 Open Email Client
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => copyEmailContent(invitation)}
                            >
                              📋 Copy Email Content
                            </Button>
                          </div>
                        </div>
                        
                        {invitation.message && (
                          <div>
                            <label className="text-sm font-medium text-gray-700">Message:</label>
                            <p className="text-sm text-gray-600 mt-1 p-2 bg-white border rounded">
                              {invitation.message}
                            </p>
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
          
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-md">
            <h4 className="font-medium text-blue-800">How Custom Invitations Work:</h4>
            <ul className="text-sm text-blue-700 mt-2 space-y-1 list-disc list-inside">
              <li>When standard API invitations fail due to permissions, custom invitations are created</li>
              <li>Each invitation gets a unique URL that recipients can use to join</li>
              <li>Invitation URLs can be shared manually via email, messaging, etc.</li>
              <li>Recipients click the URL to accept the invitation and create their account</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
