
'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import Image from 'next/image';
import { Logo } from '@/components/logo';
import { useAuth } from '@/context/AuthContext';
import { generateVerificationToken, sendVerificationEmail } from '@/lib/utils';

import { useRouter } from 'next/navigation';
import { getCountries, getCountryCallingCode } from 'libphonenumber-js';

// Define SignupData interface locally since it's not exported from AuthContext
interface SignupData {
  organization_name: string;
  email: string;
  password: string;
  contact_name: string;
  phone_number?: string;
}

// Define internal signup data structure used in the form
interface InternalSignupData {
  org_in: {
    name: string;
    type: 'company' | 'supplier';
    address: string;
    telephone: string;
    contact_person: string;
    status: string;
    country: string;
    incorporation_date: string;
  };
  admin_in: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    telephone_number: string;
    role_names: string[];
  };
}

// Type definitions for form fields
interface BaseFieldConfig {
  id: string;
  label: string;
  required: boolean;
  colSpan: 1 | 2;
}

interface TextFieldConfig extends BaseFieldConfig {
  type: 'text';
  autoComplete?: string;
  placeholder: string;
}

interface EmailFieldConfig extends BaseFieldConfig {
  type: 'email';
  autoComplete?: string;
  placeholder: string;
  value?: string; // For controlled components like confirmEmail
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

interface PasswordFieldConfig extends BaseFieldConfig {
  type: 'password';
  autoComplete?: string;
  placeholder: string;
  minLength: number;
  value?: string; // For controlled components like confirmPassword
  onChange?: (e: React.ChangeEvent<HTMLInputElement>) => void;
}

interface TelFieldConfig extends BaseFieldConfig {
  type: 'tel';
  autoComplete?: string;
  placeholder: string;
}

interface SelectFieldConfig extends BaseFieldConfig {
  type: 'select';
  options: Array<{ value: string; label: string }>;
}

interface CustomSelectFieldConfig extends BaseFieldConfig {
  type: 'customSelect';
}

type FormFieldConfig = 
  | TextFieldConfig 
  | EmailFieldConfig 
  | PasswordFieldConfig 
  | TelFieldConfig
  | SelectFieldConfig
  | CustomSelectFieldConfig;
import { toast } from 'sonner';


interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  password: string;
  companyName: string;
  countryCode: string;
  phone: string;
  streetAddress: string;
  city: string;
  country: string;
  orgType: 'company' | 'supplier';
  agreeToTerms: boolean;
  agreeToMarketing: boolean;
}

// Type for country data
interface CountryData {
  code: string;
  name: string;
  dialCode: string;
}

// Type for REST Countries API response
interface CountryApiData {
  name: {
    common: string;
    official: string;
  };
  cca2: string;
  flags: {
    png: string;
    svg: string;
    alt?: string;
  };
  idd: {
    root?: string;
    suffixes?: string[];
  };
}

export default function SignupPage() {
  const { signup } = useAuth();
  const router = useRouter();
  
  // State for password visibility
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  
  // Toggle password visibility
  const togglePasswordVisibility = () => setShowPassword(!showPassword);
  const toggleConfirmPasswordVisibility = () => setShowConfirmPassword(!showConfirmPassword);
  
  // State for country codes and full country data
  const [countries, setCountries] = useState<CountryData[]>([]);
  const [countryList, setCountryList] = useState<CountryApiData[]>([]);
  const [isLoadingCountries, setIsLoadingCountries] = useState(true);

  // Fetch countries from REST Countries API
  useEffect(() => {
    const fetchCountries = async () => {
      setIsLoadingCountries(true);
      try {
        // Fetch all countries from REST Countries API
        const response = await fetch('https://restcountries.com/v3.1/all?fields=name,cca2,flags,idd');
        if (!response.ok) {
          throw new Error('Failed to fetch countries');
        }
        
        const data: CountryApiData[] = await response.json();
        
        // Sort countries alphabetically by name
        data.sort((a, b) => a.name.common.localeCompare(b.name.common));
        
        // Save full country data for the dropdown
        setCountryList(data);
        
        // Extract country codes for phone number dropdown
        const phoneCountryData: CountryData[] = data
          .filter(country => country.idd && country.idd.root) // Only include countries with phone codes
          .map(country => {
            // Get the country calling code (with +)
            const root = country.idd.root || '';
            const suffix = country.idd.suffixes && country.idd.suffixes.length > 0 ? country.idd.suffixes[0] : '';
            const dialCode = `${root}${suffix}`;
            
            return {
              code: country.cca2,
              name: country.name.common,
              dialCode: dialCode
            };
          });
        
        // Sort phone country data alphabetically
        phoneCountryData.sort((a, b) => a.name.localeCompare(b.name));
        
        setCountries(phoneCountryData);
      } catch (error) {
        console.error('Error fetching countries:', error);
        // Fallback to some common country codes if the API fails
        setCountries([
          { code: 'UG', name: 'Uganda', dialCode: '+256' },
          { code: 'KE', name: 'Kenya', dialCode: '+254' },
          { code: 'US', name: 'United States', dialCode: '+1' },
          { code: 'GB', name: 'United Kingdom', dialCode: '+44' },
        ]);
      } finally {
        setIsLoadingCountries(false);
      }
    };
    
    fetchCountries();
  }, []);
  // Helper functions for password strength
  const getPasswordStrength = (password: string): number => {
    if (!password) return 0;
    
    let score = 0;
    
    // Length check
    if (password.length >= 8) score += 20;
    if (password.length >= 12) score += 10;
    
    // Complexity checks
    if (/[a-z]/.test(password)) score += 10;
    if (/[A-Z]/.test(password)) score += 20;
    if (/[0-9]/.test(password)) score += 20;
    if (/[^A-Za-z0-9]/.test(password)) score += 20;
    
    return Math.min(100, score);
  };
  
  const getPasswordStrengthClass = (password: string): string => {
    const strength = getPasswordStrength(password);
    if (strength < 40) return 'bg-red-500';
    if (strength < 70) return 'bg-yellow-500';
    return 'bg-green-500';
  };

  // State for form steps
  const [currentStep, setCurrentStep] = useState(1); // 1: User & Account, 2: Company Info
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    email: '',
    password: '',
    companyName: '',
    countryCode: '+256', // Default country code for Uganda
    phone: '',
    streetAddress: '',
    city: '',
    country: 'Uganda', // Default country set to Uganda
    orgType: 'company', // Default value
    agreeToTerms: false,
    agreeToMarketing: false
  });
  
  // Set Uganda as default country when countries are loaded
  useEffect(() => {
    if (countryList.length > 0) {
      const uganda = countryList.find(country => country.cca2 === 'UG');
      if (uganda) {
        setFormData(prev => ({
          ...prev,
          country: uganda.name.common
        }));
      }
    }
  }, [countryList]);
  const [confirmEmail, setConfirmEmail] = useState('');
  const [confirmPassword, setConfirmPassword] = useState('');
  const [error, setError] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [showConfirmationModal, setShowConfirmationModal] = useState(false);

  // State for real-time validation
  const [validationStatus, setValidationStatus] = useState({
    email: 'idle', // idle, checking, valid, invalid
    phone: 'idle',
    orgName: 'idle',
  });
  const [validationErrors, setValidationErrors] = useState({
    email: '',
    phone: '',
    orgName: '',
  });

  // Debounced validation for email
  useEffect(() => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!formData.email || !emailRegex.test(formData.email)) {
      setValidationStatus(prev => ({ ...prev, email: 'idle' }));
      setValidationErrors(prev => ({ ...prev, email: '' }));
      return;
    }

    // Simple client-side validation - email format is already checked above
    setValidationStatus(prev => ({ ...prev, email: 'valid' }));
    setValidationErrors(prev => ({ ...prev, email: '' }));
  }, [formData.email]);

  // Debounced validation for organization name
  useEffect(() => {
    if (!formData.companyName || formData.companyName.length < 2) {
      setValidationStatus(prev => ({ ...prev, orgName: 'idle' }));
      setValidationErrors(prev => ({ ...prev, orgName: '' }));
      return;
    }

    // Simple client-side validation - length is already checked above
    setValidationStatus(prev => ({ ...prev, orgName: 'valid' }));
    setValidationErrors(prev => ({ ...prev, orgName: '' }));
  }, [formData.companyName]);

  // Debounced validation for phone number
  useEffect(() => {
    const phoneRegex = /^[0-9]{9,10}$/;
    if (!formData.phone || !phoneRegex.test(formData.phone)) {
      setValidationStatus(prev => ({ ...prev, phone: 'idle' }));
      setValidationErrors(prev => ({ ...prev, phone: '' }));
      return;
    }

    // Simple client-side validation - phone format is already checked above
    setValidationStatus(prev => ({ ...prev, phone: 'valid' }));
    setValidationErrors(prev => ({ ...prev, phone: '' }));
  }, [formData.phone, formData.countryCode]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    
    if (name === 'confirmEmail') {
      setConfirmEmail(value);
    } else if (name === 'confirmPassword') {
      setConfirmPassword(value);
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: value
      }));
    }
    
    // Clear any previous error when user starts typing
    if (error) setError('');
  };

  // Validate step 1 fields and proceed to step 2 if valid
  const validateAndProceed = () => {
    // Validate required personal info fields
    if (!formData.firstName.trim()) {
      setError('First name is required');
      return;
    }
    
    if (!formData.lastName.trim()) {
      setError('Last name is required');
      return;
    }
    
    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setError('Please enter a valid email address');
      return;
    }

    const freeEmailProviders = [
      'gmail.com', 'yahoo.com', 'hotmail.com', 'outlook.com', 'aol.com',
      'icloud.com', 'mail.com', 'zoho.com', 'protonmail.com', 'yandex.com',
      'live.com', 'msn.com', 'me.com', 'gmx.com', 'inbox.com'
    ];
    
    const emailDomain = formData.email.split('@')[1].toLowerCase();
    if (freeEmailProviders.includes(emailDomain)) {
      setError('Please use a business email address');
      return;
    }

    // Validate password strength
    const passwordStrengthRegex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/;
    if (!passwordStrengthRegex.test(formData.password)) {
      setError('Password must be at least 8 characters long and include uppercase, lowercase, number, and special character');
      return;
    }

    // Validate password confirmation
    if (formData.password !== confirmPassword) {
      setError('Passwords do not match');
      return;
    }

    // Validate email confirmation
    if (formData.email !== confirmEmail) {
      setError('Emails do not match');
      return;
    }

    // If we're going to step 2, validate phone number
    if (currentStep === 2) {
      // Validate phone number format
      const phoneRegex = /^[0-9]{9,10}$/;
      if (formData.phone && !phoneRegex.test(formData.phone)) {
        setError('Phone number should be 9-10 digits without spaces or special characters');
        return;
      }
    }
    
    // If all validations pass, proceed to next step
    setCurrentStep(prev => prev + 1);
    setError('');
  };
  
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    // Show confirmation modal instead of submitting directly
    setShowConfirmationModal(true);
  };
  
  const handleConfirmSignup = async () => {
    // Final validation before submitting
    if (validationStatus.email !== 'valid' || validationStatus.orgName !== 'valid' || validationStatus.phone !== 'valid') {
      let errorMsg = 'Please fix the errors before submitting. ';
      if (validationErrors.email) errorMsg += validationErrors.email;
      if (validationErrors.orgName) errorMsg += validationErrors.orgName;
      if (validationErrors.phone) errorMsg += validationErrors.phone;
      setError(errorMsg);
      setShowConfirmationModal(false);
      return;
    }
    // These validations are now handled in validateAndProceed function
    // but we'll keep them here as a safety measure
    if (formData.email !== confirmEmail) {
      setError('Emails do not match');
      setShowConfirmationModal(false);
      return;
    }
    
    if (formData.password !== confirmPassword) {
      setError('Passwords do not match');
      setShowConfirmationModal(false);
      return;
    }
    
    if (formData.password.length < 8) {
      setError('Password must be at least 8 characters long');
      setShowConfirmationModal(false);
      return;
    }
    
    setIsLoading(true);
    setError('');
    setShowConfirmationModal(false);
    
    try {
      // Get current date in YYYY-MM-DD format for incorporation_date
      const today = new Date();
      const formattedDate = today.toISOString().split('T')[0];
      
      // Transform form data to match the expected API format
      // Combine the address fields into a single address string
      const fullAddress = `${formData.streetAddress}, ${formData.city}, ${formData.country}`;

      const fullPhoneNumber = `${formData.countryCode}${formData.phone}`.replace(/[^0-9+]/g, '');

      const internalSignupData = {
        org_in: {
          name: formData.companyName,
          type: formData.orgType as 'company' | 'supplier',
          address: fullAddress,
          telephone: fullPhoneNumber,  // Includes country code
          contact_person: `${formData.firstName} ${formData.lastName}`,
          status: 'pending',
          country: formData.country,
          incorporation_date: formattedDate
        },
        admin_in: {
          email: formData.email,
          password: formData.password,
          first_name: formData.firstName,
          last_name: formData.lastName,
          telephone_number: fullPhoneNumber,  // Also use full number with country code
          role_names: [formData.orgType === 'company' ? 'c_admin' : 's_admin']
        }
      };

      console.log('Submitting signup data:', internalSignupData);

      await signup(internalSignupData);

      // Generate verification token and send email
      const verificationToken = generateVerificationToken(formData.email);
      const emailSent = await sendVerificationEmail(formData.email, verificationToken);

      if (emailSent) {
        toast.success('Account created successfully! Please check your email to verify your account.');
        router.push(`/verify-email?email=${encodeURIComponent(formData.email)}`);
      } else {
        toast.success('Account created successfully! Please sign in with your credentials.');
        toast.warning('Verification email could not be sent. Please contact support if needed.');
        router.push('/login');
      }
    } catch (err) {
      console.error('Signup error:', err);
      const errorMessage = err instanceof Error ? err.message : 'Failed to create account. Please try again.';
      setError(errorMessage);
      toast.error('Failed to create account');
    } finally {
      setIsLoading(false);
    }
  };

  const inputClass = "block w-full rounded-md border-0 py-2.5 px-3 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ring-gray-300 dark:ring-gray-600 placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:ring-ascension-blue dark:bg-gray-700 sm:text-sm sm:leading-6";
  const labelClass = "block text-sm font-medium leading-6 text-gray-900 dark:text-gray-200";

  // Form field configuration
  const personalInfoFields: FormFieldConfig[] = [
    {
      id: 'firstName',
      label: 'First name',
      type: 'text',
      autoComplete: 'given-name',
      placeholder: 'John',
      required: true,
      colSpan: 1
    },
    {
      id: 'lastName',
      label: 'Last name',
      type: 'text',
      autoComplete: 'family-name',
      placeholder: 'Doe',
      required: true,
      colSpan: 1
    },
  ];

  const accountCredentialFields: FormFieldConfig[] = [
    {
      id: 'email',
      label: 'Email address',
      type: 'email',
      autoComplete: 'email',
      placeholder: 'Company or Business email',
      required: true,
      colSpan: 1
    },
    {
      id: 'confirmEmail',
      label: 'Confirm email',
      type: 'email',
      placeholder: 'Confirm your email',
      required: true,
      colSpan: 1,
      value: confirmEmail,
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => setConfirmEmail(e.target.value)
    },
    {
      id: 'password',
      label: 'Password',
      type: 'password',
      autoComplete: 'new-password',
      placeholder: '••••••••',
      required: true,
      minLength: 8,
      colSpan: 1
    },
    {
      id: 'confirmPassword',
      label: 'Confirm password',
      type: 'password',
      autoComplete: 'new-password',
      placeholder: '••••••••',
      required: true,
      minLength: 8,
      colSpan: 1,
      value: confirmPassword,
      onChange: (e: React.ChangeEvent<HTMLInputElement>) => setConfirmPassword(e.target.value)
    },
  ];

  // Dynamic company name field that changes based on account type
  const getCompanyInfoFields = (): FormFieldConfig[] => [
    {
      id: 'orgType',
      label: 'Select Account',
      type: 'select',
      required: true,
      colSpan: 1,
      options: [
        { value: 'company', label: 'Company' },
        { value: 'supplier', label: 'Supplier' }
      ]
    },
    {
      id: 'phone',
      label: 'Phone number',
      type: 'tel',
      autoComplete: 'tel',
      placeholder: '*********',
      required: true,
      colSpan: 1
    },
    {
      id: 'companyName',
      label: formData.orgType === 'company' ? 'Company name' : 'Business name',
      type: 'text',
      autoComplete: 'organization',
      placeholder: formData.orgType === 'company' ? 'Company/Organisation full name' : 'Registered business name',
      required: true,
      colSpan: 2
    },
    {
      id: 'streetAddress',
      label: 'Physical Address',
      type: 'text',
      autoComplete: 'street-address',
      placeholder: '123 Main St',
      required: true,
      colSpan: 2
    },
    {
      id: 'city',
      label: 'City',
      type: 'text',
      autoComplete: 'address-level2',
      placeholder: 'Kampala',
      required: true,
      colSpan: 1
    },
    {
      id: 'country',
      label: 'Country',
      type: 'customSelect',
      required: true,
      colSpan: 1
    }
  ];

  const formFields: FormFieldConfig[] = [...personalInfoFields, ...accountCredentialFields, ...getCompanyInfoFields()];

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
      <div className="w-full max-w-xl">
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <div className="text-center mb-4">
              <Link href="/" className="inline-flex items-center">
                <Logo />
              </Link>
            </div>
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Just a few details to get you started</h1>
          </div>
          
          {error && (
            <div className="mb-6 rounded-md bg-red-50 dark:bg-red-900/30 p-4">
              <div className="flex">
                <div className="flex-shrink-0">
                  <svg className="h-5 w-5 text-red-400 dark:text-red-300" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.28 7.22a.75.75 0 00-1.06 1.06L8.94 10l-1.72 1.72a.75.75 0 101.06 1.06L10 11.06l1.72 1.72a.75.75 0 101.06-1.06L11.06 10l1.72-1.72a.75.75 0 00-1.06-1.06L10 8.94 8.28 7.22z" clipRule="evenodd" />
                  </svg>
                </div>
                <div className="ml-3">
                  <p className="text-sm font-medium text-red-800 dark:text-red-300">{error}</p>
                </div>
              </div>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div className="mb-4 text-center text-sm text-gray-600 dark:text-gray-400">
              Step {currentStep} of 2
            </div>

            {/* Step 1: User & Account Details */}
            {currentStep === 1 && (
              <>
                {/* Personal Information Section */}
                <section>
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Personal Information</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">
                    {personalInfoFields.map((field) => (
                      <div key={field.id} className={`sm:col-span-${field.colSpan}`}>
                        <label htmlFor={field.id} className={labelClass}>
                          {field.label}
                          {field.required && <span className="text-red-500">*</span>}
                        </label>
                        <div className="mt-2.5">
                          <input
                            type={showPassword && (field.id === 'password' || field.id === 'confirmPassword') ? 'text' : field.type}
                            name={field.id}
                            id={field.id}
                            autoComplete={'autoComplete' in field ? field.autoComplete : undefined}
                            placeholder={'placeholder' in field ? field.placeholder : undefined}
                            required={field.required}
                            value={('value' in field && field.value) || formData[field.id as keyof FormData] as string}
                            onChange={('onChange' in field && field.onChange) || handleInputChange}
                            className={inputClass}
                            minLength={'minLength' in field ? field.minLength : undefined}
                          />
                          {/* Validation errors are handled separately now */}
                        </div>
                      </div>
                    ))}
                  </div>
                </section>

                {/* Account Credentials Section */}
                <section className="pt-6">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Account Credentials</h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">
                    {accountCredentialFields.map((field) => (
                      <div key={field.id} className={field.colSpan === 2 ? 'sm:col-span-2' : ''}>
                        <label htmlFor={field.id} className={labelClass}>
                          {field.label}
                          {field.required && <span className="text-red-500">*</span>}
                        </label>
                        <div className="mt-1.5">
                          {(() => {
                            const commonProps = {
                              id: field.id,
                              name: field.id,
                              className: inputClass,
                              required: field.required,
                            };
                            switch (field.type) {
                              case 'email':
                                return <input {...commonProps} type="email" autoComplete={field.autoComplete} placeholder={field.placeholder} value={field.value !== undefined ? field.value : formData[field.id as keyof FormData] as string} onChange={field.onChange || handleInputChange} onPaste={(e) => e.preventDefault()} />;
                              case 'password':
                                return (
                                  <div className="w-full relative">
                                    <input 
                                      {...commonProps} 
                                      type={field.id === 'password' ? (showPassword ? 'text' : 'password') : (showConfirmPassword ? 'text' : 'password')} 
                                      autoComplete={field.autoComplete} 
                                      placeholder={field.placeholder} 
                                      value={field.value !== undefined ? field.value : formData[field.id as keyof FormData] as string} 
                                      onChange={field.onChange || handleInputChange} 
                                      minLength={8}
                                      onPaste={(e) => e.preventDefault()} 
                                      pattern="(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}"
                                      title="Password must be at least 8 characters long and include uppercase, lowercase, number, and special character"
                                      style={{ paddingRight: '2.5rem' }}
                                    />
                                    <button 
                                      type="button" 
                                      className={`absolute right-0 pr-3 text-gray-500 hover:text-gray-700 focus:outline-none ${field.id === 'password' ? 'password-eye-icon' : ''}`}
                                      style={field.id === 'password' ? { top: '11px' } : { top: '50%', transform: 'translateY(-50%)' }}
                                      onClick={field.id === 'password' ? togglePasswordVisibility : toggleConfirmPasswordVisibility}
                                      aria-label={field.id === 'password' ? 
                                        (showPassword ? 'Hide password' : 'Show password') : 
                                        (showConfirmPassword ? 'Hide password' : 'Show password')}
                                    >
                                      {/* Use the same eye icon logic for both password fields */}
                                      {(field.id === 'password' && showPassword) || (field.id === 'confirmPassword' && showConfirmPassword) ? (
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                                          <path strokeLinecap="round" strokeLinejoin="round" d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.773 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.242 4.242L9.88 9.88" />
                                        </svg>
                                      ) : (
                                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" strokeWidth={1.5} stroke="currentColor" className="w-5 h-5">
                                          <path strokeLinecap="round" strokeLinejoin="round" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z" />
                                          <path strokeLinecap="round" strokeLinejoin="round" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                                        </svg>
                                      )}
                                    </button>
                                    {field.id === 'password' && (
                                      <div className="mt-1 h-8"> {/* Fixed height container to prevent layout shift */}
                                        {formData.password && (
                                          <>
                                            <div className="text-xs text-gray-500 mb-1">Password strength:</div>
                                            <div className="h-1 w-full bg-gray-200 rounded-full overflow-hidden">
                                              <div 
                                                className={`h-full ${getPasswordStrengthClass(formData.password)}`} 
                                                style={{ width: `${getPasswordStrength(formData.password)}%` }}
                                              ></div>
                                            </div>
                                          </>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                );
                              default: return null;
                            }
                          })()}
                        </div>
                      </div>
                    ))}
                  </div>
                </section>
              </>
            )}

            {/* Step 2: Company Information Section */}
            {currentStep === 2 && (
              <section>
                <h2 className="text-lg font-semibold text-gray-900 dark:text-white mb-3">Company Information</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-6 gap-y-4">
                  {getCompanyInfoFields().map((field) => (
                    <div key={field.id} className={field.colSpan === 2 ? 'sm:col-span-2' : ''}>
                      <label htmlFor={field.id} className={labelClass}>
                        {field.label.endsWith('*') ? field.label.slice(0, -1) : field.label}
                        {(field.required && !field.label.endsWith('*')) && <span className="text-red-500">*</span>}
                      </label>
                      <div className="mt-1.5">
                        {(() => {
                          const commonProps = {
                            id: field.id,
                            name: field.id,
                            className: inputClass,
                            required: field.required,
                          };
                          switch (field.type) {
                            case 'text':
                              return <input {...commonProps} type="text" autoComplete={field.autoComplete} placeholder={field.placeholder} value={formData[field.id as keyof FormData]as string} onChange={handleInputChange} />;
                            case 'tel':
                              return (
                                <div className="flex">
                                  <div className="relative">
                                    <select
                                      id="countryCode"
                                      name="countryCode"
                                      className={`${inputClass} rounded-r-none border-r-0 h-full appearance-none`}
                                      value={formData.countryCode}
                                      onChange={handleInputChange}
                                      aria-label="Country code"
                                      style={{ paddingLeft: '2.75rem', paddingRight: '3rem', width: '9rem' }}
                                    >
                                      {countries.map(country => (
                                        <option key={country.code} value={country.dialCode}>
                                          {country.dialCode}
                                        </option>
                                      ))}
                                    </select>
                                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                                      <Image
                                        src={countryList.find(c => c.cca2 === countries.find(country => country.dialCode === formData.countryCode)?.code)?.flags.svg || ''}
                                        alt=""
                                        width={15}
                                        height={15}
                                        objectFit="contain"
                                      />
                                    </div>
                                    <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                      <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path></svg>
                                    </div>
                                  </div>
                                  <input
                                    id={field.id}
                                    name="phone"
                                    type="tel"
                                    autoComplete={field.autoComplete}
                                    placeholder={field.placeholder}
                                    value={formData.phone}
                                    onChange={handleInputChange}
                                    className={`${inputClass} rounded-l-none flex-1`}
                                    required={field.required}
                                  />
                                </div>
                              );
                            case 'customSelect':
                              // Custom select for country with flags
                              return (
                                <div className="relative">
                                  <select
                                    {...commonProps}
                                    value={formData[field.id as keyof FormData] as string}
                                    onChange={handleInputChange}
                                    style={{ height: '42px', paddingLeft: '45px', paddingRight: '2.5rem' }} /* Add padding for flag and dropdown icon */
                                    className={`${inputClass} pl-12 pr-10 appearance-none`}
                                  >
                                    {isLoadingCountries ? (
                                      <option value="Uganda">Loading countries...</option>
                                    ) : (
                                      countryList.map(country => (
                                        <option 
                                          key={country.cca2} 
                                          value={country.name.common}
                                          data-flag={country.flags.svg}
                                        >
                                          {country.name.common}
                                        </option>
                                      ))
                                    )}
                                  </select>
                                  {/* Show selected country flag */}
                                  {!isLoadingCountries && countryList.length > 0 && (
                                    <div className="absolute left-3 top-1/2 transform -translate-y-1/2 w-6 h-4 overflow-hidden">
                                      <Image 
                                        src={countryList.find(c => c.name.common === formData.country)?.flags.svg || 
                                             countryList.find(c => c.name.common === 'Uganda')?.flags.svg || 
                                             '/images/default-flag.svg'}
                                        alt="Country flag"
                                        className="w-full h-full object-cover rounded-sm"
                                        width={24}
                                        height={16}
                                      />
                                    </div>
                                  )}
                                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path></svg>
                                  </div>
                                </div>
                              );
                            case 'select':
                              return (
                                <div className="relative">
                                  <select
                                    {...commonProps}
                                    value={formData[field.id as keyof FormData] as string}
                                    onChange={handleInputChange}
                                    style={{ height: '42px' }} /* Match height with phone number field */
                                    className={`${inputClass} appearance-none pr-10`}
                                  >
                                    {field.options.map(option => (
                                      <option key={option.value} value={option.value}>{option.label}</option>
                                    ))}
                                  </select>
                                  <div className="absolute inset-y-0 right-0 flex items-center pr-3 pointer-events-none">
                                    <svg className="w-5 h-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 9l-7 7-7-7"></path></svg>
                                  </div>
                                </div>
                              );
                            default: return null;
                          }
                        })()}
                      </div>
                    </div>
                  ))}
                </div>
              </section>
            )}

            <div className="pt-2 flex justify-between items-center">
              {currentStep > 1 && (
                <button
                  type="button"
                  onClick={() => setCurrentStep(prev => prev - 1)}
                  className="py-2.5 px-4 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#14546c] dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
                >
                  Previous
                </button>
              )}
              {currentStep < 2 && (
                <button
                  type="button"
                  onClick={() => validateAndProceed()}
                  className="ml-auto py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#14546c] hover:bg-[#0e3e50] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#14546c] dark:bg-[#14546c] dark:hover:bg-[#14546c]"
                >
                  Next
                </button>
              )}
              {currentStep === 2 && (
                <button
                  type="submit"
                  disabled={isLoading}
                  className="ml-auto py-2.5 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#14546c] hover:bg-[#0e3e50] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#14546c] dark:bg-[#14546c] dark:hover:bg-[#14546c]"
                >
                  {isLoading ? 'Creating Account...' : 'Sign Up'}
                </button>
              )}
            </div>
          </form>

          <p className="mt-6 text-center text-sm text-gray-600 dark:text-gray-400">
            Already have an Account?{' '}
            <Link href="/login" className="font-semibold leading-6 text-ascension-blue hover:text-ascension-blue-dark dark:text-blue-400 dark:hover:text-blue-300">Log in</Link>
          </p>
        </div>
      </div>

      {/* Confirmation Modal - with animation and improved styling */}
      {showConfirmationModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-60 backdrop-blur-sm transition-opacity duration-300">
          <div 
            className="w-full max-w-lg bg-white dark:bg-gray-800 rounded-xl shadow-2xl p-8 relative transform transition-all duration-300 ease-in-out animate-fadeIn"
            style={{
              boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25)',
              animation: 'fadeIn 0.3s ease-out'
            }}
          >
            {/* Close button - redesigned as a circle */}
            <button 
              onClick={() => setShowConfirmationModal(false)} 
              className="absolute top-4 right-4 rounded-full bg-gray-100 dark:bg-gray-700 w-8 h-8 flex items-center justify-center text-gray-500 hover:text-gray-700 hover:bg-gray-200 dark:hover:bg-gray-600 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              aria-label="Close dialog"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
            
            {/* Header with icon and title */}
            <div className="flex flex-col items-center mb-8">
              <div className="flex justify-center mb-4 transform transition-transform hover:scale-105">
                <div className="rounded-full bg-amber-100 dark:bg-amber-900 p-3">
                  <svg className="w-10 h-10 text-amber-500 dark:text-amber-400" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z"></path>
                  </svg>
                </div>
              </div>
              <h3 className="text-xl font-bold text-center text-gray-900 dark:text-white">Administrator Confirmation</h3>
              <div className="h-1 w-16 bg-amber-500 rounded-full mt-2 mb-4"></div>
              <p className="mt-2 text-center text-gray-600 dark:text-gray-300 max-w-md">
                A guest user can only sign up as the administrator of Ascension for their company or business.
              </p>
            </div>
            
            {/* Admin responsibilities - with icons */}
            <div className="mb-6 bg-gray-50 dark:bg-gray-700 rounded-lg p-4 border-l-4 border-amber-500">
              <h4 className="font-semibold text-gray-800 dark:text-gray-200 mb-2">The administrator:</h4>
              <ul className="space-y-3 text-gray-600 dark:text-gray-300">
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"></path>
                  </svg>
                  <span>Onboards the rest of the users and cannot onboard themselves as a different type of user</span>
                </li>
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                  </svg>
                  <span>Manages user system access and subscription (where applicable)</span>
                </li>
                <li className="flex items-start">
                  <svg className="h-5 w-5 text-green-500 mr-2 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                  </svg>
                  <span>Sets up the working folders, controls and settings as per the company/business policy</span>
                </li>
              </ul>
            </div>
            
            {/* Confirmation question */}
            <p className="mb-6 text-center text-gray-700 dark:text-gray-300 font-medium">
              Are you the designated administrator?
            </p>
            
            {/* Action buttons - with improved styling */}
            <div className="flex justify-center space-x-4">
              <button
                onClick={() => setShowConfirmationModal(false)}
                className="px-5 py-2.5 border border-gray-300 rounded-lg text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500 transition-colors duration-200 dark:bg-gray-700 dark:text-gray-200 dark:border-gray-600 dark:hover:bg-gray-600"
              >
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12"></path>
                  </svg>
                  No, Cancel
                </span>
              </button>
              <button
                onClick={handleConfirmSignup}
                className="px-5 py-2.5 border border-transparent rounded-lg text-sm font-medium text-white bg-[#14546c] hover:bg-[#0a3e53] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#14546c] transition-all duration-200 shadow-md hover:shadow-lg dark:bg-[#14546c] dark:hover:bg-[#0a3e53] transform hover:-translate-y-0.5"
              >
                <span className="flex items-center">
                  <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
                  </svg>
                  Yes, Sign up
                </span>
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
