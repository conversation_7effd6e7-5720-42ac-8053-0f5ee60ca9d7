'use client';

import React, { useState, useEffect, Suspense } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { EyeIcon, EyeSlashIcon } from '@heroicons/react/24/solid';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';
import { Logo } from '@/components/logo';
import Link from 'next/link';

interface InvitationData {
  id: string;
  email: string;
  role: string;
  organization_name: string;
  invited_by_name: string;
  expires_at: string;
  status: string;
}

const SetPasswordContent = () => {
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    businessName: '',
    businessEmail: '',
    confirmEmail: '',
    addressLine1: '',
    addressLine2: '',
    addressLine3: '',
    country: '',
    telephone: '',
    password: '',
    confirmPassword: '',
  });
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [invitation, setInvitation] = useState<InvitationData | null>(null);
  const [invitationLoading, setInvitationLoading] = useState(true);
  const [success, setSuccess] = useState(false);
  
  const router = useRouter();
  const searchParams = useSearchParams();
  const { completeSetup } = useAuth();

  const token = searchParams.get('token');
  const invitationId = searchParams.get('invitation_id');

  useEffect(() => {
    const fetchInvitationData = async () => {
      if (!token && !invitationId) {
        setErrors({ general: 'Invalid invitation link. Please contact your administrator.' });
        setInvitationLoading(false);
        return;
      }

      try {
        // Try to fetch invitation data from our test API
        const response = await fetch(`/api/test-invitation?token=${token}&invitation_id=${invitationId}`);

        if (response.ok) {
          const result = await response.json();
          if (result.success && result.data) {
            setInvitation(result.data);

            // Pre-fill the form with invitation data
            setFormData(prev => ({
              ...prev,
              businessName: result.data.organization_name,
              businessEmail: result.data.email,
              confirmEmail: result.data.email
            }));
          } else {
            throw new Error('Invalid invitation data received');
          }
        } else {
          // Fallback to mock data if API fails
          const mockInvitation: InvitationData = {
            id: invitationId || 'inv_123',
            email: searchParams.get('email') || '<EMAIL>',
            role: 'supplier_user',
            organization_name: 'ABC Supplies Ltd',
            invited_by_name: 'John Smith',
            expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
            status: 'pending'
          };

          setInvitation(mockInvitation);

          // Pre-fill the form with invitation data
          setFormData(prev => ({
            ...prev,
            businessName: mockInvitation.organization_name,
            businessEmail: mockInvitation.email,
            confirmEmail: mockInvitation.email
          }));
        }

      } catch (error) {
        console.error('Error fetching invitation:', error);

        // Fallback to mock data with URL parameters
        const mockInvitation: InvitationData = {
          id: invitationId || 'inv_123',
          email: searchParams.get('email') || '<EMAIL>',
          role: 'supplier_user',
          organization_name: 'ABC Supplies Ltd',
          invited_by_name: 'John Smith',
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
          status: 'pending'
        };

        setInvitation(mockInvitation);

        // Pre-fill the form with invitation data
        setFormData(prev => ({
          ...prev,
          businessName: mockInvitation.organization_name,
          businessEmail: mockInvitation.email,
          confirmEmail: mockInvitation.email
        }));
      } finally {
        setInvitationLoading(false);
      }
    };

    fetchInvitationData();
  }, [token, invitationId, searchParams]);

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.firstName.trim()) {
      newErrors.firstName = 'First name is required';
    }

    if (!formData.lastName.trim()) {
      newErrors.lastName = 'Last name is required';
    }

    if (!formData.businessName.trim()) {
      newErrors.businessName = 'Business name is required';
    }

    if (!formData.businessEmail.trim()) {
      newErrors.businessEmail = 'Business email is required';
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.businessEmail)) {
      newErrors.businessEmail = 'Please enter a valid email address';
    }

    if (!formData.confirmEmail.trim()) {
      newErrors.confirmEmail = 'Please confirm your email';
    } else if (formData.businessEmail !== formData.confirmEmail) {
      newErrors.confirmEmail = 'Email addresses do not match';
    }

    if (!formData.addressLine1.trim()) {
      newErrors.addressLine1 = 'Address line 1 is required';
    }

    if (!formData.country.trim()) {
      newErrors.country = 'Country is required';
    }

    if (!formData.telephone.trim()) {
      newErrors.telephone = 'Telephone is required';
    }

    if (!formData.password) {
      newErrors.password = 'Password is required';
    } else if (formData.password.length < 8) {
      newErrors.password = 'Password must be at least 8 characters long';
    } else if (!/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(formData.password)) {
      newErrors.password = 'Password must contain at least one uppercase letter, one lowercase letter, and one number';
    }

    if (!formData.confirmPassword) {
      newErrors.confirmPassword = 'Please confirm your password';
    } else if (formData.password !== formData.confirmPassword) {
      newErrors.confirmPassword = 'Passwords do not match';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors(prev => ({
        ...prev,
        [name]: ''
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setLoading(true);
    setErrors({});

    try {
      // Complete the setup using the token
      await completeSetup({
        token: token || '',
        password: formData.password,
        profile: {
          first_name: formData.firstName,
          last_name: formData.lastName,
          email: formData.businessEmail
        }
      });
      
      setSuccess(true);
      toast.success('Password set successfully! You can now sign in.');
    } catch (error: any) {
      console.error('Set password error:', error);
      setErrors({ 
        general: error.response?.data?.error?.message || 
                error.message || 
                'Failed to set password. Please try again.' 
      });
      toast.error(error.message || 'Failed to set password');
    } finally {
      setLoading(false);
    }
  };

  const handleOkClick = () => {
    router.push('/login');
  };

  if (invitationLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#18546c]"></div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
        <div className="w-full max-w-md">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8 text-center">
            <div className="text-center mb-6">
              <Link href="/" className="inline-flex items-center">
                <Logo />
              </Link>
            </div>
            
            {/* Success Icon */}
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-green-100 mb-6">
              <svg className="h-8 w-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
            
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Password Set Successfully!
            </h1>
            <p className="text-gray-600 dark:text-gray-400 mb-8">
              Your password has been set successfully. You can now sign in to your account.
            </p>
            
            <Button 
              onClick={handleOkClick}
              className="w-full bg-[#18546c] hover:bg-[#134255] text-white"
            >
              Ok
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-50 p-4">
      <div className="w-full max-w-2xl">
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-8">
          <div className="text-center mb-8">
            <div className="text-center mb-6">
              <Link href="/" className="inline-flex items-center">
                <Logo />
              </Link>
            </div>
            <p className="text-gray-600 mb-2">
              Just a few details to get you started.
            </p>
            <h1 className="text-xl font-medium text-gray-900 italic">
              Set your password
            </h1>
          </div>

          {errors.general && (
            <div className="mb-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 text-sm">{errors.general}</p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            {/* User Type - Read Only */}
            <div>
              <Input
                value="Supplier user"
                readOnly
                className="bg-gray-100 text-gray-600 border-gray-300"
              />
            </div>

            {/* First and Last Name Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Input
                  id="firstName"
                  name="firstName"
                  type="text"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className={errors.firstName ? 'border-red-500' : ''}
                  placeholder="[First name]"
                />
                {errors.firstName && (
                  <p className="mt-1 text-sm text-red-600">{errors.firstName}</p>
                )}
              </div>
              <div>
                <Input
                  id="lastName"
                  name="lastName"
                  type="text"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className={errors.lastName ? 'border-red-500' : ''}
                  placeholder="[Last name]"
                />
                {errors.lastName && (
                  <p className="mt-1 text-sm text-red-600">{errors.lastName}</p>
                )}
              </div>
            </div>

            {/* Business Name and Address Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Input
                  id="businessName"
                  name="businessName"
                  type="text"
                  value={formData.businessName}
                  onChange={handleInputChange}
                  className={`${errors.businessName ? 'border-red-500' : ''} bg-gray-100`}
                  placeholder="[Registered business name from admin details]"
                  readOnly
                />
                {errors.businessName && (
                  <p className="mt-1 text-sm text-red-600">{errors.businessName}</p>
                )}
              </div>
              <div>
                <Input
                  id="addressLine1"
                  name="addressLine1"
                  type="text"
                  value={formData.addressLine1}
                  onChange={handleInputChange}
                  className={errors.addressLine1 ? 'border-red-500' : ''}
                  placeholder="Address line 1"
                />
                {errors.addressLine1 && (
                  <p className="mt-1 text-sm text-red-600">{errors.addressLine1}</p>
                )}
              </div>
            </div>

            {/* Business Email and Address Line 2 Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Input
                  id="businessEmail"
                  name="businessEmail"
                  type="email"
                  value={formData.businessEmail}
                  onChange={handleInputChange}
                  className={`${errors.businessEmail ? 'border-red-500' : ''} bg-gray-100`}
                  placeholder="[Business email address from admin's account]"
                  readOnly
                />
                {errors.businessEmail && (
                  <p className="mt-1 text-sm text-red-600">{errors.businessEmail}</p>
                )}
              </div>
              <div>
                <Input
                  id="addressLine2"
                  name="addressLine2"
                  type="text"
                  value={formData.addressLine2}
                  onChange={handleInputChange}
                  placeholder="Address line 2"
                />
              </div>
            </div>

            {/* Confirm Email and Address Line 3 Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Input
                  id="confirmEmail"
                  name="confirmEmail"
                  type="email"
                  value={formData.confirmEmail}
                  onChange={handleInputChange}
                  className={errors.confirmEmail ? 'border-red-500' : ''}
                  placeholder="Confirm email address"
                />
                {errors.confirmEmail && (
                  <p className="mt-1 text-sm text-red-600">{errors.confirmEmail}</p>
                )}
              </div>
              <div>
                <Input
                  id="addressLine3"
                  name="addressLine3"
                  type="text"
                  value={formData.addressLine3}
                  onChange={handleInputChange}
                  placeholder="Address line 3"
                />
              </div>
            </div>

            {/* Password and Country Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="relative">
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    value={formData.password}
                    onChange={handleInputChange}
                    className={errors.password ? 'border-red-500 pr-10' : 'pr-10'}
                    placeholder="Password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowPassword(!showPassword)}
                  >
                    {showPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.password && (
                  <p className="mt-1 text-sm text-red-600">{errors.password}</p>
                )}
              </div>
              <div>
                <select
                  id="country"
                  name="country"
                  value={formData.country}
                  onChange={(e) => handleInputChange(e as any)}
                  className={`w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-[#18546c] focus:border-transparent ${errors.country ? 'border-red-500' : ''}`}
                >
                  <option value="">Country</option>
                  <option value="UG">Uganda</option>
                  <option value="KE">Kenya</option>
                  <option value="TZ">Tanzania</option>
                  <option value="RW">Rwanda</option>
                  <option value="US">United States</option>
                  <option value="UK">United Kingdom</option>
                  <option value="CA">Canada</option>
                </select>
                {errors.country && (
                  <p className="mt-1 text-sm text-red-600">{errors.country}</p>
                )}
              </div>
            </div>

            {/* Confirm Password and Telephone Row */}
            <div className="grid grid-cols-2 gap-4">
              <div>
                <div className="relative">
                  <Input
                    id="confirmPassword"
                    name="confirmPassword"
                    type={showConfirmPassword ? 'text' : 'password'}
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    className={errors.confirmPassword ? 'border-red-500 pr-10' : 'pr-10'}
                    placeholder="Confirm password"
                  />
                  <button
                    type="button"
                    className="absolute inset-y-0 right-0 pr-3 flex items-center"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                  >
                    {showConfirmPassword ? (
                      <EyeSlashIcon className="h-5 w-5 text-gray-400" />
                    ) : (
                      <EyeIcon className="h-5 w-5 text-gray-400" />
                    )}
                  </button>
                </div>
                {errors.confirmPassword && (
                  <p className="mt-1 text-sm text-red-600">{errors.confirmPassword}</p>
                )}
              </div>
              <div>
                <Input
                  id="telephone"
                  name="telephone"
                  type="tel"
                  value={formData.telephone}
                  onChange={handleInputChange}
                  className={errors.telephone ? 'border-red-500' : ''}
                  placeholder="Telephone"
                />
                {errors.telephone && (
                  <p className="mt-1 text-sm text-red-600">{errors.telephone}</p>
                )}
              </div>
            </div>

            <div className="text-center mt-8">
              <Button
                type="submit"
                disabled={loading}
                className="bg-[#18546c] hover:bg-[#134255] text-white px-8 py-2 rounded-md"
              >
                {loading ? (
                  <>
                    <span className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span>
                    Submitting...
                  </>
                ) : (
                  'Submit'
                )}
              </Button>
            </div>
          </form>
        </div>

        {/* Footer */}
        <div className="text-center mt-8">
          <p className="text-gray-600 font-medium">
            Ascension. Intuitive purchasing.
          </p>
        </div>
      </div>
    </div>
  );
};

// Loading component for Suspense fallback
const SetPasswordLoading = () => (
  <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center">
    <div className="bg-white dark:bg-gray-800 p-8 rounded-lg shadow-lg">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
      <p className="mt-4 text-gray-600 dark:text-gray-300">Loading...</p>
    </div>
  </div>
);

// Main component with Suspense wrapper
const SetPasswordPage = () => {
  return (
    <Suspense fallback={<SetPasswordLoading />}>
      <SetPasswordContent />
    </Suspense>
  );
};

export default SetPasswordPage;
