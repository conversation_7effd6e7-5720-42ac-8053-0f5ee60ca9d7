'use client';
import React, { useState, useEffect, Suspense } from 'react';
import { useAuth } from '@/context/AuthContext';
import { useRouter, useSearchParams } from 'next/navigation';
import Link from 'next/link';
import { Logo } from '@/components/logo';
import { toast } from 'sonner';
import { getRoleDescription } from '@/utils/roleRedirect';
import { useGlobalPreloader } from '@/hooks/useGlobalPreloader';

const LoginForm = () => {
  const [formData, setFormData] = useState({
    email: '',
    password: '',
    rememberMe: false
  });
  const [loading, setLoading] = useState(false);
  const [errors, setErrors] = useState<{ email?: string; password?: string; general?: string }>({});
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login, user: authUser, mockSuperAdminLogin, mockUserLogin } = useAuth();
  const { showPreloader, hidePreloader } = useGlobalPreloader();

  useEffect(() => {
    // Show success message if user was redirected from email verification
    if (searchParams.get('verified') === 'true') {
      toast.success('Email verified successfully! You can now sign in.');
    }

    // Show success message if user was redirected from password reset
    if (searchParams.get('reset') === 'true') {
      toast.success('Password reset successfully! You can now sign in with your new password.');
    }

    // Clear any credentials from the URL for security
    if (typeof window !== 'undefined') {
      const url = new URL(window.location.href);
      if (url.searchParams.has('email') || url.searchParams.has('password')) {
        url.searchParams.delete('email');
        url.searchParams.delete('password');
        window.history.replaceState({}, document.title, url.toString());
      }
    }
  }, [searchParams]);

  const validateForm = () => {
    const newErrors: { email?: string; password?: string } = {};
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!formData.password) {
      newErrors.password = 'Password is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value, type, checked } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: type === 'checkbox' ? checked : value,
    }));
    
    // Clear error when user starts typing
    if (errors[name as keyof typeof errors]) {
      setErrors(prev => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!validateForm()) return;
    
    setLoading(true);
    setErrors({});

    try {
      // Show beautiful preloader
      showPreloader('Signing you in...', 'elegant');

      // Perform login first
      await login(formData.email, formData.password);

      // Get updated user data after login (the login function stores it)
      const currentUser = JSON.parse(localStorage.getItem('user') || '{}');
      const userName = currentUser?.first_name || currentUser?.last_name || 'User';
      const role = currentUser?.role;
      const roleDescription = getRoleDescription(role);

      console.log('Login successful, user data:', currentUser);
      console.log('User role:', role);

      // Update preloader with welcome message
      showPreloader(`Welcome back, ${userName}! Redirecting to ${roleDescription} dashboard...`, 'modern');

      toast.success(`Welcome back, ${userName}!`);

      // Store remember me preference
      if (formData.rememberMe) {
        localStorage.setItem('rememberMe', 'true');
      } else {
        localStorage.removeItem('rememberMe');
      }

      // Debug: Check what we have in localStorage
      console.log('=== LOGIN DEBUG ===');
      console.log('Current user from localStorage:', currentUser);
      console.log('Role:', role);
      console.log('Role description:', roleDescription);
      console.log('Access token exists:', !!localStorage.getItem('access_token'));
      console.log('==================');

      // Use window.location for immediate redirect to avoid React state issues
      if (role) {
        console.log(`Redirecting ${roleDescription} (${role}) to their dashboard`);
        const dashboardUrl = role === 'c_admin' ? '/company-dashboard' :
                           role === 'c_stores_manager' ? '/stores-manager-dashboard' :
                           role === 'c_accountant' ? '/accountant-dashboard' :
                           role === 'c_procurement_officer' ? '/procurement-officer-dashboard' :
                           role === 'c_approving_manager' ? '/approving-manager-dashboard' :
                           role === 'c_requesting_user' ? '/requesting-user-dashboard' :
                           role === 's_admin' ? '/supplier-dashboard' :
                           role === 'super_admin' || role === 'admin' ? '/super-admin-dashboard' :
                           role === 'supplier' || role === 'supplier_user' || role === 's_user' ? '/supplier-user-dashboard' :
                           '/dashboard';
        console.log('Dashboard URL:', dashboardUrl);

        // Small delay to show the welcome message, then redirect
        setTimeout(() => {
          window.location.href = dashboardUrl;
        }, 1500);
      } else {
        console.warn('No role found, redirecting to generic dashboard');
        setTimeout(() => {
          window.location.href = '/dashboard';
        }, 1500);
      }

    } catch (error: any) {
      console.error('Login error:', error);

      // Hide preloader on error
      hidePreloader();

      const errorMessage = error.response?.data?.error?.message ||
                          error.message ||
                          'Login failed. Please check your credentials and try again.';
      setErrors({ general: errorMessage });
      toast.error(errorMessage);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen flex items-center justify-center bg-gray-100 dark:bg-gray-900 p-4">
      <div className="w-full max-w-md">
        
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
          <div className="text-center mb-8">
            <div className="text-center mb-4">
              <Link href="/" className="inline-flex items-center">
                <Logo />
              </Link>
              <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
                Seamless procurement, better collaboration with your team awaits!
              </p>
            </div>
            <h1 className="text-2xl font-bold text-black dark:text-white">Sign in to your account</h1>
          </div>

          <form className="space-y-6" onSubmit={handleSubmit}>
            {errors.general && (
              <div className="p-3 text-sm text-red-800 dark:text-red-300 rounded-lg bg-red-50 dark:bg-red-900/30">
                {errors.general}
              </div>
            )}

            <div>
              <label htmlFor="email" className="block text-sm font-medium leading-6 text-gray-900 dark:text-gray-200">
                Email Address
              </label>
              <div className="mt-2 relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 12a4 4 0 10-8 0 4 4 0 008 0zm0 0v1.5a2.5 2.5 0 005 0V12a9 9 0 10-9 9m4.5-1.206a8.959 8.959 0 01-4.5 1.207" />
                  </svg>
                </div>
                <input
                  id="email"
                  name="email"
                  type="email"
                  required
                  className={`block w-full rounded-md border-0 py-2.5 pl-10 pr-3 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ${errors.email ? 'ring-red-300 dark:ring-red-500' : 'ring-gray-300 dark:ring-gray-600'} placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:ring-ascension-blue dark:bg-gray-700 sm:text-sm sm:leading-6`}
                  placeholder="<EMAIL>"
                  value={formData.email}
                  onChange={handleChange}
                />
              </div>
              {errors.email && <p className="mt-2 text-sm text-red-600">{errors.email}</p>}
            </div>

            <div>
              <label htmlFor="password" className="block text-sm font-medium leading-6 text-gray-900 dark:text-gray-200">
                Password
              </label>
              <div className="mt-2 relative">
                <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                  <svg className="w-5 h-5 text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
                  </svg>
                </div>
                <input
                  id="password"
                  name="password"
                  type="password"
                  required
                  className={`block w-full rounded-md border-0 py-2.5 pl-10 pr-3 text-gray-900 dark:text-white shadow-sm ring-1 ring-inset ${errors.password ? 'ring-red-300 dark:ring-red-500' : 'ring-gray-300 dark:ring-gray-600'} placeholder:text-gray-400 dark:placeholder:text-gray-500 focus:ring-2 focus:ring-inset focus:ring-ascension-blue dark:bg-gray-700 sm:text-sm sm:leading-6`}
                  placeholder="••••••••"
                  value={formData.password}
                  onChange={handleChange}
                />
              </div>
              {errors.password && <p className="mt-2 text-sm text-red-600">{errors.password}</p>}
            </div>

            <div className="flex items-center justify-between">
              <div className="flex items-center">
                <input
                  id="rememberMe"
                  name="rememberMe"
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 dark:border-gray-600 text-ascension-blue focus:ring-ascension-blue dark:bg-gray-700"
                  checked={formData.rememberMe}
                  onChange={handleChange}
                />
                <label htmlFor="rememberMe" className="ml-2 block text-sm text-gray-900 dark:text-gray-200">
                  Remember me
                </label>
              </div>
              <div className="text-sm">
                <Link href="/forgot-password" className="font-medium text-sm text-ascension-blue hover:text-blue-500 dark:text-blue-400 dark:hover:text-blue-300">
                  Forgot password?
                </Link>
              </div>
            </div>

            <div>
              <button
                type="submit"
                disabled={loading}
                className="w-full flex justify-center py-3 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#14546c] hover:bg-[#0e3e50] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#14546c] disabled:opacity-70 disabled:cursor-not-allowed"
              >
                {loading ? (
                  <>
                    <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                      <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                      <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                    </svg>
                    Signing in...
                  </>
                ) : (
                  'Sign in'
                )}
              </button>
            </div>
          </form>

          {/* Mock Super Admin Login - Development Only */}
          <div className="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
            <button
              onClick={() => {
                mockSuperAdminLogin();
                router.push('/super-admin-dashboard');
              }}
              className="w-full flex justify-center py-2 px-4 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm text-sm font-medium text-gray-700 dark:text-gray-300 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500"
            >
              🔧 Mock Super Admin Login (Dev)
            </button>
          </div>

          <p className="mt-8 text-center text-sm text-gray-600 dark:text-gray-400">
            Don&apos;t have an account?{' '}
            <Link href="/signup" className="font-semibold text-ascension-blue hover:text-ascension-blue-dark dark:text-blue-400 dark:hover:text-blue-300">
              Sign up
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
};

const LoginPage = () => {
  return (
    <Suspense fallback={<div className="min-h-screen flex items-center justify-center">Loading...</div>}>
      <LoginForm />
    </Suspense>
  );
};

export default LoginPage;
