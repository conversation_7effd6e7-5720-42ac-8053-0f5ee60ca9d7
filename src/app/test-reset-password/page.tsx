'use client';
import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { Logo } from '@/components/logo';
import { toast } from 'sonner';
import { PasswordService } from '@/services/password.service';
import { useAuth } from '@/context/AuthContext';

const TestResetPasswordPage = () => {
  const [email, setEmail] = useState('');
  const [token, setToken] = useState('test-token-123');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [testResults, setTestResults] = useState<Array<{step: string, status: 'pending' | 'success' | 'error', message: string}>>([]);
  const { completeSetup } = useAuth();

  const addTestResult = (step: string, status: 'pending' | 'success' | 'error', message: string) => {
    setTestResults(prev => [...prev, { step, status, message }]);
  };

  const testRequestPasswordReset = async () => {
    if (!email) {
      toast.error('Please enter an email address');
      return;
    }

    setLoading(true);
    addTestResult('Request Password Reset', 'pending', `Sending reset request for ${email}...`);

    try {
      // Test the forgot-password endpoint
      const response = await PasswordService.requestPasswordReset(email);
      addTestResult('Request Password Reset', 'success', `Success: ${response.message}`);
      toast.success('Password reset email request sent successfully');
    } catch (error: any) {
      console.error('Password reset request test failed:', error);
      addTestResult('Request Password Reset', 'error', `Error: ${error.message}`);
      toast.error(error.message || 'Failed to send password reset email');
    } finally {
      setLoading(false);
    }
  };

  const testVerifyToken = async () => {
    if (!token) {
      toast.error('Please enter a token');
      return;
    }

    setLoading(true);
    addTestResult('Verify Token', 'pending', `Verifying token: ${token}...`);

    try {
      // Test the verify-token endpoint
      const isValid = await PasswordService.verifyResetToken(token);
      if (isValid) {
        addTestResult('Verify Token', 'success', 'Token is valid');
        toast.success('Token verification successful');
      } else {
        addTestResult('Verify Token', 'error', 'Token is invalid');
        toast.error('Token verification failed');
      }
    } catch (error: any) {
      console.error('Token verification test failed:', error);
      addTestResult('Verify Token', 'error', `Error: ${error.message}`);
      toast.error(error.message || 'Failed to verify token');
    } finally {
      setLoading(false);
    }
  };

  const testResetPassword = async () => {
    if (!token || !password) {
      toast.error('Please enter both token and password');
      return;
    }

    if (password.length < 8) {
      toast.error('Password must be at least 8 characters');
      return;
    }

    setLoading(true);
    addTestResult('Reset Password', 'pending', 'Resetting password...');

    try {
      // Test the reset-password endpoint using the complete-setup endpoint
      await completeSetup({
        token,
        password
      });
      
      addTestResult('Reset Password', 'success', 'Password reset successful');
      toast.success('Password reset successful');
    } catch (error: any) {
      console.error('Password reset test failed:', error);
      addTestResult('Reset Password', 'error', `Error: ${error.message}`);
      toast.error(error.message || 'Failed to reset password');
    } finally {
      setLoading(false);
    }
  };

  const testDirectResetPassword = async () => {
    if (!token || !password) {
      toast.error('Please enter both token and password');
      return;
    }

    setLoading(true);
    addTestResult('Direct Reset Password', 'pending', 'Testing direct password reset...');

    try {
      // Test the password service's resetPassword method
      const response = await PasswordService.resetPassword(token, password);
      addTestResult('Direct Reset Password', 'success', `Success: ${response.message}`);
      toast.success('Direct password reset successful');
    } catch (error: any) {
      console.error('Direct password reset test failed:', error);
      addTestResult('Direct Reset Password', 'error', `Error: ${error.message}`);
      toast.error(error.message || 'Failed to reset password directly');
    } finally {
      setLoading(false);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="min-h-screen p-8 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-3xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="text-center mb-8">
            <Link href="/" className="inline-flex items-center justify-center">
              <Logo />
            </Link>
            <h1 className="text-2xl font-bold text-black dark:text-white mt-4">
              Reset Password Flow Test
            </h1>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Test each step of the password reset flow
            </p>
          </div>

          <div className="space-y-8">
            {/* Step 1: Request Password Reset */}
            <div className="border rounded-lg p-4 dark:border-gray-700">
              <h2 className="text-lg font-semibold mb-4">Step 1: Request Password Reset</h2>
              <div className="space-y-4">
                <div>
                  <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Email Address
                  </label>
                  <input
                    type="email"
                    id="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-ascension-blue focus:border-ascension-blue dark:bg-gray-700 dark:text-white"
                    placeholder="Enter your email"
                  />
                </div>
                <button
                  onClick={testRequestPasswordReset}
                  disabled={loading}
                  className="w-full bg-ascension-blue text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ascension-blue disabled:opacity-50"
                >
                  {loading ? 'Testing...' : 'Test Request Password Reset'}
                </button>
              </div>
            </div>

            {/* Step 2: Verify Token */}
            <div className="border rounded-lg p-4 dark:border-gray-700">
              <h2 className="text-lg font-semibold mb-4">Step 2: Verify Token</h2>
              <div className="space-y-4">
                <div>
                  <label htmlFor="token" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    Reset Token
                  </label>
                  <input
                    type="text"
                    id="token"
                    value={token}
                    onChange={(e) => setToken(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-ascension-blue focus:border-ascension-blue dark:bg-gray-700 dark:text-white"
                    placeholder="Enter reset token"
                  />
                </div>
                <button
                  onClick={testVerifyToken}
                  disabled={loading}
                  className="w-full bg-ascension-blue text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ascension-blue disabled:opacity-50"
                >
                  {loading ? 'Testing...' : 'Test Verify Token'}
                </button>
              </div>
            </div>

            {/* Step 3: Reset Password */}
            <div className="border rounded-lg p-4 dark:border-gray-700">
              <h2 className="text-lg font-semibold mb-4">Step 3: Reset Password</h2>
              <div className="space-y-4">
                <div>
                  <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                    New Password
                  </label>
                  <input
                    type="password"
                    id="password"
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-ascension-blue focus:border-ascension-blue dark:bg-gray-700 dark:text-white"
                    placeholder="Enter new password"
                  />
                  <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                    Password must be at least 8 characters
                  </p>
                </div>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <button
                    onClick={testResetPassword}
                    disabled={loading}
                    className="w-full bg-ascension-blue text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ascension-blue disabled:opacity-50"
                  >
                    {loading ? 'Testing...' : 'Test via Auth Context'}
                  </button>
                  <button
                    onClick={testDirectResetPassword}
                    disabled={loading}
                    className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
                  >
                    {loading ? 'Testing...' : 'Test via Password Service'}
                  </button>
                </div>
              </div>
            </div>

            {/* Test Results */}
            <div className="border rounded-lg p-4 dark:border-gray-700">
              <div className="flex justify-between items-center mb-4">
                <h2 className="text-lg font-semibold">Test Results</h2>
                <button
                  onClick={clearResults}
                  className="text-sm text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200"
                >
                  Clear Results
                </button>
              </div>
              
              {testResults.length === 0 ? (
                <p className="text-gray-500 dark:text-gray-400 italic">No tests run yet</p>
              ) : (
                <div className="space-y-2">
                  {testResults.map((result, index) => (
                    <div key={index} className={`p-3 rounded ${
                      result.status === 'success' ? 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300' : 
                      result.status === 'error' ? 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300' : 
                      'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300'
                    }`}>
                      <div className="font-medium">{result.step}: {
                        result.status === 'success' ? '✓' : 
                        result.status === 'error' ? '✗' : 
                        '⟳'
                      }</div>
                      <div className="text-sm">{result.message}</div>
                    </div>
                  ))}
                </div>
              )}
            </div>

            {/* Navigation Links */}
            <div className="border-t pt-4 dark:border-gray-700">
              <div className="flex flex-wrap gap-2">
                <Link href="/test-auth" className="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200">
                  Back to Auth Test
                </Link>
                <Link href="/forgot-password" className="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200">
                  Forgot Password Page
                </Link>
                <Link href="/reset-password?token=test-token-123" className="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200">
                  Reset Password Page
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestResetPasswordPage;
