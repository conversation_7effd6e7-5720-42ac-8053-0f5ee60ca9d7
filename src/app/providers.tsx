"use client";

import { SidebarProvider } from "@/components/Layouts/sidebar/sidebar-context";
import { ThemeProvider } from "next-themes";
import { AuthProvider } from "@/context/AuthContext";
import { NotificationProvider } from "@/context/NotificationContext";
import { MessageProvider } from "@/context/MessageContext";
import { Toaster } from 'sonner';
import { ModalProvider } from "@/components/providers/ModalProvider";
import { GlobalPreloaderProvider } from "@/components/providers/GlobalPreloaderProvider";

export function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider defaultTheme="light" attribute="class">
      <AuthProvider>
        <NotificationProvider>
          <MessageProvider>
            <ModalProvider>
              <GlobalPreloaderProvider>
                <SidebarProvider>
                  {children}
                  <Toaster richColors position="top-right" />
                </SidebarProvider>
              </GlobalPreloaderProvider>
            </ModalProvider>
          </MessageProvider>
        </NotificationProvider>
      </AuthProvider>
    </ThemeProvider>
  );
}
