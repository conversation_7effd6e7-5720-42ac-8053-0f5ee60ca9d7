'use client';
import React, { useState } from 'react';
import <PERSON> from 'next/link';
import { Logo } from '@/components/logo';
import { toast } from 'sonner';
import axios from 'axios';

const TestLoginPage = () => {
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [loading, setLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);
  const [error, setError] = useState<any>(null);

  const testDirectLogin = async () => {
    if (!email || !password) {
      toast.error('Please enter both email and password');
      return;
    }

    setLoading(true);
    setResponse(null);
    setError(null);

    try {
      console.log('Attempting direct login with:', { email });
      
      // Create form data as required by the API
      const formData = new URLSearchParams();
      formData.append('grant_type', 'password');
      formData.append('username', email);
      formData.append('password', password);
      formData.append('scope', '');
      formData.append('client_id', 'string');
      formData.append('client_secret', 'string');
      
      // Make direct request to the API
      const response = await axios.post(
        'https://dev.ascensionservices.net/api/v1/auth/login', 
        formData.toString(),
        {
          headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Accept': 'application/json'
          }
        }
      );
      
      setResponse(response.data);
      toast.success('Direct login successful!');
    } catch (err: any) {
      console.error('Direct login error:', err);
      setError({
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });
      toast.error(`Direct login failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testProxyLogin = async () => {
    if (!email || !password) {
      toast.error('Please enter both email and password');
      return;
    }

    setLoading(true);
    setResponse(null);
    setError(null);

    try {
      console.log('Attempting proxy login with:', { email });
      
      // Use our proxy endpoint
      const response = await axios.post('/api/proxy/auth/login', {
        email,
        password
      });
      
      setResponse(response.data);
      toast.success('Proxy login successful!');
      
      // Store token in localStorage for testing
      if (response.data.access_token) {
        localStorage.setItem('access_token', response.data.access_token);
        if (response.data.refresh_token) {
          localStorage.setItem('refresh_token', response.data.refresh_token);
        }
      }
    } catch (err: any) {
      console.error('Proxy login error:', err);
      setError({
        message: err.message,
        response: err.response?.data,
        status: err.response?.status
      });
      toast.error(`Proxy login failed: ${err.message}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="min-h-screen p-8 bg-gray-50 dark:bg-gray-900">
      <div className="max-w-3xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <div className="text-center mb-8">
            <Link href="/" className="inline-flex items-center justify-center">
              <Logo />
            </Link>
            <h1 className="text-2xl font-bold text-black dark:text-white mt-4">
              Login Debug Page
            </h1>
            <p className="mt-2 text-sm text-gray-600 dark:text-gray-400">
              Test login functionality directly and through proxy
            </p>
          </div>

          <div className="space-y-6">
            <div className="space-y-4">
              <div>
                <label htmlFor="email" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Email Address
                </label>
                <input
                  type="email"
                  id="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-ascension-blue focus:border-ascension-blue dark:bg-gray-700 dark:text-white"
                  placeholder="Enter your email"
                />
              </div>
              <div>
                <label htmlFor="password" className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
                  Password
                </label>
                <input
                  type="password"
                  id="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md shadow-sm focus:outline-none focus:ring-ascension-blue focus:border-ascension-blue dark:bg-gray-700 dark:text-white"
                  placeholder="Enter your password"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <button
                onClick={testDirectLogin}
                disabled={loading}
                className="w-full bg-ascension-blue text-white py-2 px-4 rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-ascension-blue disabled:opacity-50"
              >
                {loading ? 'Testing...' : 'Test Direct API Login'}
              </button>
              <button
                onClick={testProxyLogin}
                disabled={loading}
                className="w-full bg-green-600 text-white py-2 px-4 rounded-md hover:bg-green-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500 disabled:opacity-50"
              >
                {loading ? 'Testing...' : 'Test Proxy Login'}
              </button>
            </div>

            {/* Response Display */}
            {response && (
              <div className="mt-6 border rounded-lg p-4 bg-green-50 dark:bg-green-900/30">
                <h3 className="text-lg font-medium text-green-800 dark:text-green-300 mb-2">Success Response:</h3>
                <pre className="bg-white dark:bg-gray-800 p-3 rounded overflow-auto max-h-60 text-sm">
                  {JSON.stringify(response, null, 2)}
                </pre>
              </div>
            )}

            {/* Error Display */}
            {error && (
              <div className="mt-6 border rounded-lg p-4 bg-red-50 dark:bg-red-900/30">
                <h3 className="text-lg font-medium text-red-800 dark:text-red-300 mb-2">Error Response:</h3>
                <pre className="bg-white dark:bg-gray-800 p-3 rounded overflow-auto max-h-60 text-sm">
                  {JSON.stringify(error, null, 2)}
                </pre>
              </div>
            )}

            {/* Navigation Links */}
            <div className="border-t pt-4 dark:border-gray-700">
              <div className="flex flex-wrap gap-2">
                <Link href="/test-auth" className="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200">
                  Back to Auth Test
                </Link>
                <Link href="/test-reset-password" className="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200">
                  Test Reset Password
                </Link>
                <Link href="/login" className="px-4 py-2 bg-gray-200 dark:bg-gray-700 rounded hover:bg-gray-300 dark:hover:bg-gray-600 text-gray-800 dark:text-gray-200">
                  Regular Login Page
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TestLoginPage;
