import { getEmailTemplate } from '@/services/emailTemplates';
import { EmailTemplateForm } from '../../components/template-form';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';

interface EditEmailTemplatePageProps {
  params: Promise<{
    slug: string;
  }>;
}

export default async function EditEmailTemplatePage({ params }: EditEmailTemplatePageProps) {
  // Fetching data on the server side
  let template = null;
  try {
    const { slug } = await params;
    template = await getEmailTemplate(slug);
  } catch (error) {
    // Handled below by checking if template is null
    console.error("Failed to fetch template for editing:", error);
  }

  if (!template) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Template Not Found</CardTitle>
          <CardDescription>
            The email template you are trying to edit does not exist.
          </CardDescription>
        </CardHeader>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Edit Email Template</CardTitle>
        <CardDescription>
          Modify the details of the &quot;{template.name}&quot; template.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <EmailTemplateForm initialData={template} />
      </CardContent>
    </Card>
  );
}
