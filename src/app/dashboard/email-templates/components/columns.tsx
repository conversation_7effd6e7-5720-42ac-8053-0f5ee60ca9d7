"use client"

import { ColumnDef, Row, Table, Column } from "@tanstack/react-table"
import { MoreHorizontal } from "lucide-react"

import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { toast } from "sonner"
import { ApiEmailTemplate, deleteEmailTemplate } from "@/services/emailTemplates"

export const columns: ColumnDef<ApiEmailTemplate>[] = [
  {
    id: "select",
    header: ({ table }: { table: Table<ApiEmailTemplate> }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Select all"
      />
    ),
    cell: ({ row }: { row: Row<ApiEmailTemplate> }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Select row"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "subject",
    header: "Subject",
  },
  {
    accessorKey: "slug",
    header: "Slug",
  },
  {
    accessorKey: "isActive",
    header: "Active",
    cell: ({ row }) => {
      const isActive = row.getValue("isActive")
      return isActive ? "Yes" : "No"
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const template = row.original
      // Don't use hooks inside regular functions
      // Using window.history instead of useRouter hook
      const history = window.history
      
      const handleDelete = async () => {
        try {
          await deleteEmailTemplate(template.slug)
          toast.success(`The template "${template.name}" has been deleted.`)
          window.location.reload() // Refresh data
        } catch (error) {
          toast.error("Failed to delete template.")
        }
      }

      return (
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="h-8 w-8 p-0">
              <span className="sr-only">Open menu</span>
              <MoreHorizontal className="h-4 w-4" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Actions</DropdownMenuLabel>
            <DropdownMenuItem
              onClick={() => navigator.clipboard.writeText(template.slug)}
            >
              Copy slug
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <Link href={`/dashboard/email-templates/edit/${template.slug}`}>
              <DropdownMenuItem>Edit Template</DropdownMenuItem>
            </Link>
            <DropdownMenuItem onClick={handleDelete} className="text-red-600">
              Delete Template
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      )
    },
  },
]
