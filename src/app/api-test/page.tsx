'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';

export default function ApiTestPage() {
  const [email, setEmail] = useState('<EMAIL>');
  const [organizationId, setOrganizationId] = useState<string | null>(null);
  const [tokenPrefix, setTokenPrefix] = useState<string>('');
  const [showToken, setShowToken] = useState<boolean>(false);
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [rawResponse, setRawResponse] = useState<string>('');
  
  // Load user data from localStorage on component mount
  React.useEffect(() => {
    try {
      // Get token
      const token = localStorage.getItem('access_token');
      if (token) {
        setTokenPrefix(token.substring(0, 10) + '...');
      }
      
      // Get organization ID (check both spellings)
      const userDataString = localStorage.getItem('user');
      if (userDataString) {
        const userData = JSON.parse(userDataString);
        if (userData?.organization_id) {
          setOrganizationId(userData.organization_id);
          console.log('Found organization_id:', userData.organization_id);
        } else if (userData?.organisation_id) {
          setOrganizationId(userData.organisation_id);
          console.log('Found organisation_id:', userData.organisation_id);
        } else {
          console.log('No organization_id or organisation_id found in user data');
        }
      }
    } catch (err) {
      console.error('Error loading user data:', err);
    }
  }, []);

  const runTest = async () => {
    setLoading(true);
    setError(null);
    setResult(null);
    setRawResponse('');
    
    try {
      // Get the token from localStorage
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No access token found. Please log in first.');
      }

      // Create a test payload with organization_id if available
      const testPayload: any = {
        email: email,
        includeOrgId: !!organizationId
      };
      
      if (organizationId) {
        testPayload.organizationId = organizationId;
      }
      
      console.log('Sending test with payload:', testPayload);
      
      // Call our test endpoint
      const response = await fetch('/api/test-invitation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(testPayload)
      });
      
      // Get raw response text first
      const responseText = await response.text();
      setRawResponse(responseText);
      
      let data;
      try {
        // Try to parse as JSON
        data = JSON.parse(responseText);
        setResult(data);
      } catch (parseError: any) {
        setError(`Failed to parse response as JSON: ${parseError.message}`);
        setResult({
          responseType: 'text',
          responseText: responseText
        });
      }
      
      if (!response.ok) {
        setError(`API Error: ${data?.error || response.statusText} (Status: ${response.status})`);
      }
    } catch (err: any) {
      setError(`Error: ${err.message}`);
      console.error('Test error:', err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      <h1 className="text-2xl font-bold mb-6">Supplier Invitation API Test</h1>
      
      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">Test Email</label>
          <Input 
            type="email" 
            value={email} 
            onChange={(e) => setEmail(e.target.value)} 
            placeholder="Email to test invitation"
            className="w-full"
          />
        </div>
        
        <div className="mb-4">
          <label className="block text-sm font-medium mb-1">Current Organization ID</label>
          <div className="flex items-center">
            <Input 
              type="text" 
              value={organizationId || ''} 
              onChange={(e) => setOrganizationId(e.target.value)} 
              placeholder="No organization ID found"
              className="w-full mr-2"
            />
            <Button 
              type="button" 
              variant="outline" 
              className="whitespace-nowrap"
              onClick={() => {
                try {
                  const userDataString = localStorage.getItem('user');
                  if (userDataString) {
                    const userData = JSON.parse(userDataString);
                    // Check both spellings
                    const orgId = userData?.organization_id || userData?.organisation_id || null;
                    setOrganizationId(orgId);
                  }
                } catch (err) {
                  console.error('Error refreshing organization ID:', err);
                }
              }}
            >
              Refresh
            </Button>
          </div>
          <p className="text-xs text-gray-500 mt-1">
            {organizationId ? 'Organization ID found in user data' : 'No organization ID found in localStorage'}
          </p>
        </div>
        
        <div className="flex space-x-2">
          <Button 
            onClick={runTest}
            disabled={loading}
            className="w-full"
          >
            {loading ? 'Testing...' : 'Run API Test'}
          </Button>
        </div>
        
        <div className="mt-2 text-sm text-gray-600">
          <p>Token: {tokenPrefix || 'Not found'}</p>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-50 p-4 rounded-lg border border-red-200 mb-6">
          <p className="text-red-600">{error}</p>
        </div>
      )}
      
      {result && (
        <div className="bg-gray-50 p-6 rounded-lg border border-gray-200">
          <h2 className="text-lg font-semibold mb-2">Test Results</h2>
          
          <div className="mb-4">
            <p className="text-sm font-medium mb-1">Status: 
              <span className={result.success ? "text-green-600 ml-2" : "text-red-600 ml-2"}>
                {result.response?.status} {result.response?.statusText}
              </span>
            </p>
          </div>
          
          <div>
            <p className="text-sm font-medium mb-1">Response:</p>
            <Textarea 
              readOnly 
              className="font-mono text-xs h-64" 
              value={JSON.stringify(result.response?.data, null, 2)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
