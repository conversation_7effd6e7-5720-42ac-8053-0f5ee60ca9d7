'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

export default function CheckPermissionsPage() {
  const [userInfo, setUserInfo] = useState<any>(null);
  const [loading, setLoading] = useState(false);

  useEffect(() => {
    // Load stored user data
    const storedUser = localStorage.getItem('user');
    if (storedUser) {
      try {
        setUserInfo({ stored: JSON.parse(storedUser) });
      } catch (e) {
        console.error('Error parsing stored user:', e);
      }
    }
  }, []);

  const fetchFreshUserData = async () => {
    setLoading(true);
    try {
      // Import the auth service to use its normalization logic
      const { AuthService } = await import('@/services/auth.service');

      const freshData = await AuthService.getCurrentUser();
      setUserInfo((prev: any) => ({ ...prev, fresh: freshData, normalized: true }));

      console.log('Fresh user data (normalized):', freshData);
    } catch (error: any) {
      console.error('Error fetching fresh user data:', error);
      alert(`Error: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const testRolesEndpoint = async () => {
    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No access token found');
      }

      // Try to fetch available roles
      const response = await fetch('https://dev.ascensionservices.net/api/v1/roles', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        }
      });

      console.log('Roles endpoint response:', response.status, response.statusText);
      
      if (response.ok) {
        const roles = await response.json();
        console.log('Available roles:', roles);
        alert(`Roles endpoint accessible. Check console for details.`);
      } else {
        const errorText = await response.text();
        console.log('Roles endpoint error:', errorText);
        alert(`Roles endpoint returned ${response.status}: ${response.statusText}`);
      }
    } catch (error: any) {
      console.error('Error testing roles endpoint:', error);
      alert(`Error testing roles: ${error.message}`);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-4xl">
      <Card>
        <CardHeader>
          <CardTitle>User Permissions & Role Check</CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          
          <div className="flex gap-4">
            <Button onClick={fetchFreshUserData} disabled={loading}>
              {loading ? 'Loading...' : 'Fetch Fresh User Data'}
            </Button>
            <Button onClick={testRolesEndpoint} variant="outline">
              Test Roles Endpoint
            </Button>
          </div>

          {userInfo && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              
              {/* Stored User Data */}
              {userInfo.stored && (
                <div className="p-4 bg-blue-50 border border-blue-200 rounded-md">
                  <h3 className="font-medium text-blue-800 mb-2">Stored User Data (localStorage)</h3>
                  <div className="text-sm space-y-1">
                    <p><strong>Email:</strong> {userInfo.stored.email}</p>
                    <p><strong>Role:</strong> {userInfo.stored.role}</p>
                    <p><strong>Role Names:</strong> {JSON.stringify(userInfo.stored.role_names)}</p>
                    <p><strong>Org ID:</strong> {userInfo.stored.organization_id || userInfo.stored.organisation_id}</p>
                    <p><strong>Name:</strong> {userInfo.stored.first_name} {userInfo.stored.last_name}</p>
                  </div>
                </div>
              )}

              {/* Fresh User Data */}
              {userInfo.fresh && (
                <div className="p-4 bg-green-50 border border-green-200 rounded-md">
                  <h3 className="font-medium text-green-800 mb-2">Fresh User Data (API)</h3>
                  <div className="text-sm space-y-1">
                    <p><strong>Email:</strong> {userInfo.fresh.email}</p>
                    <p><strong>Role:</strong> {userInfo.fresh.role}</p>
                    <p><strong>Role Names:</strong> {JSON.stringify(userInfo.fresh.role_names)}</p>
                    <p><strong>Org ID:</strong> {userInfo.fresh.organization_id || userInfo.fresh.organisation_id}</p>
                    <p><strong>Name:</strong> {userInfo.fresh.first_name} {userInfo.fresh.last_name}</p>
                    <p><strong>Is Active:</strong> {userInfo.fresh.is_active ? 'Yes' : 'No'}</p>
                  </div>
                </div>
              )}
            </div>
          )}

          {userInfo?.stored && userInfo?.fresh && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-md">
              <h3 className="font-medium text-yellow-800 mb-2">Role Comparison</h3>
              <div className="text-sm">
                {userInfo.stored.role !== userInfo.fresh.role ? (
                  <p className="text-red-600">
                    ⚠️ <strong>Role Mismatch:</strong> Stored=&quot;{userInfo.stored.role}&quot; vs Fresh=&quot;{userInfo.fresh.role}&quot;
                  </p>
                ) : (
                  <p className="text-green-600">
                    ✅ <strong>Roles Match:</strong> &quot;{userInfo.stored.role}&quot;
                  </p>
                )}
              </div>
            </div>
          )}

          <div className="text-sm text-gray-600">
            <p><strong>Typical Role Permissions:</strong></p>
            <ul className="list-disc list-inside mt-1 space-y-1">
              <li><strong>c_admin:</strong> Company Admin - Can invite suppliers</li>
              <li><strong>s_admin:</strong> Supplier Admin - Manages supplier organization</li>
              <li><strong>user:</strong> Regular user - Limited permissions</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
