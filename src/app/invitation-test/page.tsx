'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';

export default function InvitationTestPage() {
  const [loading, setLoading] = useState(false);
  const [results, setResults] = useState<any>({});
  const [token, setToken] = useState<string | null>(null);
  const [organizationId, setOrganizationId] = useState<number>(6);
  const [email, setEmail] = useState('<EMAIL>');

  useEffect(() => {
    // Load token from localStorage
    const storedToken = localStorage.getItem('access_token');
    if (storedToken) {
      setToken(storedToken);
    }

    // We're using organization_id 6 as specified
    console.log('Using organization ID: 6');
    // No need to load from localStorage since we're using a fixed value
  }, []);

  const runTest = async (format: string) => {
    if (!token) {
      alert('No authentication token found. Please log in first.');
      return;
    }

    setLoading(true);
    setResults((prev: any) => ({ ...prev, [format]: { loading: true } }));

    try {
      // Create payload
      const payload = {
        emails: [email],
        role: 'supplier',
        message: 'Test invitation from diagnostic tool',
        organization_id: organizationId,
        expires_in_days: 7
      };

      let response;
      let formData;

      // Make the request based on format using our Next.js API proxy route
      if (format === 'json') {
        // JSON format - standard API format
        response = await fetch('/api/proxy/invitations/bulk', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(payload)
        });
      } else if (format === 'form') {
        // For form-urlencoded format, we'll create a special proxy route
        // Create a request to our test invitation API
        response = await fetch('/api/test-invitation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            email: email,
            organizationId: organizationId,
            useFormEncoded: true
          })
        });
      } else if (format === 'formArray') {
        // For testing the form array format, create another test endpoint call
        response = await fetch('/api/debug-invitation', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify({
            email: email,
            organizationId: organizationId
          })
        });
      }

      // Process response
      let responseData;
      try {
        responseData = await response!.json();
      } catch (e) {
        const text = await response!.text();
        responseData = { text };
      }

      setResults((prev: any) => ({
        ...prev,
        [format]: {
          status: response!.status,
          ok: response!.ok,
          data: responseData,
          loading: false
        }
      }));

    } catch (error: any) {
      setResults((prev: any) => ({
        ...prev,
        [format]: {
          error: error.message,
          loading: false
        }
      }));
    } finally {
      setLoading(false);
    }
  };

  const handleOrgIdChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setOrganizationId(value ? Number(value) : 6); // Default to 6 if empty
  };

  const handleEmailChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setEmail(e.target.value);
  };

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-2xl font-bold mb-6">Invitation API Test Tool</h1>

      <div className="bg-white p-6 rounded-lg shadow-md mb-6">
        <div className="grid grid-cols-1 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium mb-1">Auth Token</label>
            <div className="text-xs p-2 bg-gray-50 rounded overflow-hidden text-ellipsis">
              {token ? `${token.substring(0, 15)}...` : 'Not found'}
            </div>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Organization ID</label>
            <input
              type="number"
              value={organizationId === null ? '' : organizationId}
              onChange={handleOrgIdChange}
              className="w-full p-2 border rounded"
              placeholder="Enter organization ID"
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Test Email</label>
            <input
              type="email"
              value={email}
              onChange={handleEmailChange}
              className="w-full p-2 border rounded"
              placeholder="Email to invite"
            />
          </div>
        </div>

        <div className="grid grid-cols-3 gap-4">
          <Button
            onClick={() => runTest('json')}
            disabled={loading}
            className="w-full"
          >
            {results.json?.loading ? 'Testing...' : 'Test JSON Format'}
          </Button>
          
          <Button
            onClick={() => runTest('form')}
            disabled={loading}
            className="w-full"
          >
            {results.form?.loading ? 'Testing...' : 'Test URL-Encoded'}
          </Button>
          
          <Button
            onClick={() => runTest('formArray')}
            disabled={loading}
            className="w-full"
          >
            {results.formArray?.loading ? 'Testing...' : 'Test URL-Encoded Array'}
          </Button>
        </div>
      </div>

      {Object.keys(results).length > 0 && (
        <div className="bg-gray-50 p-6 rounded-lg border">
          <h2 className="text-lg font-semibold mb-4">Test Results</h2>

          {Object.entries(results).map(([format, result]: [string, any]) => (
            <div key={format} className="mb-6 border rounded p-4">
              <h3 className="font-medium mb-2">
                {format === 'json' && 'JSON Format'}
                {format === 'form' && 'URL-Encoded Format'}
                {format === 'formArray' && 'URL-Encoded Array Format'}
              </h3>

              {result.loading ? (
                <p>Testing...</p>
              ) : result.error ? (
                <div className="text-red-500">Error: {result.error}</div>
              ) : (
                <>
                  <div className={`font-medium ${result.ok ? 'text-green-600' : 'text-red-600'}`}>
                    Status: {result.status} {result.ok ? '(Success)' : '(Failed)'}
                  </div>
                  
                  <div className="mt-2">
                    <pre className="text-xs bg-gray-100 p-2 rounded overflow-auto max-h-40">
                      {JSON.stringify(result.data, null, 2)}
                    </pre>
                  </div>
                </>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
