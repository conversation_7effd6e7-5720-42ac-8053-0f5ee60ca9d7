'use client';

import React, { useState } from 'react';
import { Home, Plus, Edit, Trash2, Eye } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';
import Link from 'next/link';

type TemplateOffice = {
  id: string;
  name: string;
  address: string;
};

type DocumentTemplate = {
  id: string;
  name: string;
  type: string;
  description: string;
  offices: TemplateOffice[];
  numberFormat: {
    increaseBy: [number, number];
    preview: string;
  };
};

const DocumentTemplatesPage = () => {
  const { user } = useAuth();
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Sample data for document templates
  const templates: DocumentTemplate[] = [
    {
      id: '1',
      name: 'PO template',
      type: 'PO',
      description: 'Create, view and edit templates here',
      offices: [
        { id: '1', name: 'UNRA Head Office', address: '24 Luthuli Avenue, Bugolobi - Kampala' },
        { id: '2', name: 'UNRA Moya Office', address: '21 Kaweewa Lane, Moya' },
        { id: '3', name: 'UNRA Lira Office', address: '42 Ogwal Road, Lira' },
        { id: '4', name: 'UNRA Mbale Office', address: '31 Memorial Road, Mbale' }
      ],
      numberFormat: {
        increaseBy: [0, 0],
        preview: ''
      }
    },
    {
      id: '2',
      name: 'GRN template',
      type: 'GRN',
      description: 'Create, view and edit GRN templates here',
      offices: [
        { id: '1', name: 'UNRA Head Office', address: '24 Luthuli Avenue, Bugolobi - Kampala' },
        { id: '2', name: 'UNRA Moya Office', address: '21 Kaweewa Lane, Moya' }
      ],
      numberFormat: {
        increaseBy: [0, 0],
        preview: ''
      }
    },
    {
      id: '3',
      name: 'Debit note template',
      type: 'Debit note',
      description: 'Create, view and edit Debit note templates here',
      offices: [
        { id: '1', name: 'UNRA Head Office', address: '24 Luthuli Avenue, Bugolobi - Kampala' },
        { id: '2', name: 'UNRA Moya Office', address: '21 Kaweewa Lane, Moya' }
      ],
      numberFormat: {
        increaseBy: [0, 0],
        preview: ''
      }
    }
  ];

  return (
    <div className="h-full overflow-auto">
      {/* Page Header */}
      <div className="w-full">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Home className="h-4 w-4" />
              <span>Document templates</span>
            </nav>
            <h1 className="text-xl font-medium text-gray-900">Document templates</h1>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-6 py-6">

        {/* Template Cards Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {templates.map((template) => (
            <div key={template.id} className="bg-white rounded-lg border border-gray-200 shadow-sm">
              {/* Template Header */}
              <div className="p-6 border-b border-gray-200">
                <div className="flex items-center justify-between mb-2">
                  <h3 className="text-lg font-semibold text-gray-900">{template.name}</h3>
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="bg-[#18546c] hover:bg-[#164158] text-white px-3 py-1 rounded text-sm flex items-center"
                  >
                    <Plus className="h-4 w-4 mr-1" />
                    Create New
                  </button>
                </div>
                <p className="text-sm text-gray-600">{template.description}</p>
              </div>

              {/* Template Items */}
              <div className="p-6 space-y-3">
                {template.offices.map((office) => (
                  <div key={office.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded flex items-center justify-center">
                        <span className="text-blue-600 text-sm font-medium">📄</span>
                      </div>
                      <div>
                        <div className="text-sm font-medium text-gray-900">{office.name}</div>
                        <div className="text-xs text-gray-500">{office.address}</div>
                      </div>
                    </div>
                    <div className="flex items-center space-x-2">
                      <button className="text-blue-600 hover:text-blue-800 p-1">
                        <Edit className="h-4 w-4" />
                      </button>
                      <button className="text-red-600 hover:text-red-800 p-1">
                        <Trash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Number Format Section */}
              <div className="p-6 border-t border-gray-200">
                <div className="flex items-center justify-between mb-3">
                  <h4 className="text-sm font-semibold text-gray-900">{template.type} number</h4>
                  <button className="text-[#18546c] hover:text-[#164158] text-sm font-medium">
                    Reset format
                  </button>
                </div>

                <p className="text-xs text-gray-500 mb-4">Create, view and {template.type} number format</p>

                {/* Increase by controls */}
                <div className="mb-4">
                  <div className="flex items-center justify-between mb-3">
                    <span className="text-sm text-gray-700">Increase by</span>
                    <span className="text-sm text-gray-700">Increase by</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <input
                      type="number"
                      defaultValue="0"
                      className="w-16 px-2 py-1 border border-gray-300 rounded text-sm text-center"
                    />
                    <span className="text-gray-400">-</span>
                    <input
                      type="number"
                      defaultValue="0"
                      className="w-16 px-2 py-1 border border-gray-300 rounded text-sm text-center"
                    />
                  </div>
                </div>

                {/* Preview */}
                <div className="flex items-center space-x-2 mb-4">
                  <Eye className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Preview</span>
                </div>

                {/* Action buttons */}
                <div className="flex space-x-2">
                  <button className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded text-sm font-medium">
                    Set same as reference
                  </button>
                  <button className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded text-sm font-medium">
                    Set format
                  </button>
                </div>
              </div>
            </div>
          ))}
        </div>

      </div>

      {/* Create New Template Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">Create new template</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                ✕
              </button>
            </div>

            <div className="space-y-3">
              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg text-left transition-colors duration-200">
                PO Template
              </button>

              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg text-left transition-colors duration-200">
                GRN Template
              </button>

              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg text-left transition-colors duration-200">
                Debit Note Template
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentTemplatesPage;
