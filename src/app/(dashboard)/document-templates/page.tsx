'use client';

import React, { useState } from 'react';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Search, Edit, Trash2, Eye, ChevronRight, Home, ChevronDown, X } from 'lucide-react';
import { FiFileText } from 'react-icons/fi';
import Link from 'next/link';

type DocumentTemplate = {
  id: string;
  name: string;
  type: string;
  description: string;
  offices: Array<{
    name: string;
    address: string;
    actions: {
      edit: boolean;
      delete: boolean;
    };
  }>;
};

const DocumentTemplatesPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');
  const [showCreateModal, setShowCreateModal] = useState(false);

  // Sample data for document templates
  const templates: DocumentTemplate[] = [
    {
      id: '1',
      name: 'PO template',
      type: 'PO',
      description: 'Create, view and edit templates here',
      offices: [
        { name: 'UNRA Head Office', address: '24 Luthuli Avenue, Bugolobi - Kampala', actions: { edit: true, delete: true } },
        { name: 'UNRA Moya Office', address: '21 Kaweewa Lane, Moya', actions: { edit: true, delete: true } },
        { name: 'UNRA Lira Office', address: '30 Ogwal Road, Lira', actions: { edit: true, delete: true } },
        { name: 'UNRA Mbale Office', address: '13 Memorial Road, Mbale', actions: { edit: true, delete: true } }
      ]
    },
    {
      id: '2',
      name: 'GRN template',
      type: 'GRN',
      description: 'Create, view and edit templates here',
      offices: [
        { name: 'UNRA Head Office', address: '24 Luthuli Avenue, Bugolobi - Kampala', actions: { edit: true, delete: true } },
        { name: 'UNRA Moya Office', address: '21 Kaweewa Lane, Moya', actions: { edit: true, delete: true } }
      ]
    },
    {
      id: '3',
      name: 'Debit note template',
      type: 'Debit note',
      description: 'Create, view and edit templates here',
      offices: [
        { name: 'UNRA Head Office', address: '24 Luthuli Avenue, Bugolobi - Kampala', actions: { edit: true, delete: true } },
        { name: 'UNRA Moya Office', address: '21 Kaweewa Lane, Moya', actions: { edit: true, delete: true } }
      ]
    }
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.offices.some(office =>
                           office.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                           office.address.toLowerCase().includes(searchTerm.toLowerCase())
                         );
    const matchesFilter = filter === 'all' || template.type === filter;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="h-full flex flex-col">
      {/* Page Header */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Link href="/dashboard" className="flex items-center hover:text-gray-700">
                <Home className="h-4 w-4 mr-1" />
                Dashboard
              </Link>
              <ChevronRight className="h-4 w-4" />
              <span className="text-gray-900 font-medium">Document templates</span>
            </nav>
            <h1 className="text-xl font-semibold text-gray-900">Document templates</h1>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 p-16 overflow-auto">

          {/* Template Cards Grid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            {filteredTemplates.map((template) => (
              <div key={template.id} className="bg-white rounded-lg border border-gray-200 p-6">
                {/* Template Header */}
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-lg font-medium text-gray-900">{template.name}</h3>
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm flex items-center"
                  >
                    <span className="mr-1">+</span>
                    Create New
                  </button>
                </div>

                <p className="text-sm text-gray-600 mb-4">{template.description}</p>

                {/* Template Items */}
                <div className="space-y-2 mb-6">
                  {template.offices.map((office, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                      <div className="flex items-center space-x-3">
                        <FiFileText className="h-4 w-4 text-gray-500" />
                        <div>
                          <div className="text-sm font-medium text-gray-900">{office.name}</div>
                          <div className="text-xs text-gray-500">{office.address}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <button className="text-blue-600 hover:text-blue-800 text-sm">
                          <Edit className="h-4 w-4" />
                        </button>
                        <button className="text-red-600 hover:text-red-800 text-sm">
                          <Trash2 className="h-4 w-4" />
                        </button>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Number Format Section */}
                <div className="border-t pt-4">
                  <div className="flex items-center justify-between mb-2">
                    <h4 className="text-sm font-medium text-gray-900">{template.type} number</h4>
                    <button className="text-blue-600 hover:text-blue-800 text-sm">Reset format</button>
                  </div>

                  <p className="text-xs text-gray-500 mb-4">Create, view and {template.type} number format</p>

                  <div className="bg-gray-50 p-4 rounded mb-4">
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm text-gray-700">Increase by</span>
                      <div className="flex items-center space-x-2">
                        <input type="number" defaultValue="0" className="w-16 px-2 py-1 border border-gray-300 rounded text-sm" />
                        <span className="text-gray-500">-</span>
                        <input type="number" defaultValue="0" className="w-16 px-2 py-1 border border-gray-300 rounded text-sm" />
                      </div>
                    </div>

                    <div className="flex items-center space-x-2 mb-3">
                      <Eye className="h-4 w-4 text-gray-500" />
                      <span className="text-sm text-gray-600">Preview</span>
                    </div>
                  </div>

                  <div className="flex space-x-2">
                    <button className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded text-sm">
                      Set same as reference
                    </button>
                    <button className="flex-1 bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-2 rounded text-sm">
                      Set format
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>

        {/* Back Button */}
        <div className="mt-8">
          <button className="flex items-center text-gray-600 hover:text-gray-800">
            <ChevronRight className="h-4 w-4 mr-1 rotate-180" />
            Back
          </button>
        </div>
      </div>

      {/* Create New Requisition Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">Create new requisition</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <X className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-3">
              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg text-left transition-colors duration-200">
                Goods procurement
              </button>

              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg text-left transition-colors duration-200">
                Services procurement
              </button>

              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg text-left transition-colors duration-200">
                Stores requisition
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default DocumentTemplatesPage;
