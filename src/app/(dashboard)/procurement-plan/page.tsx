'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { PlusIcon, ChevronRightIcon, MagnifyingGlassIcon, FunnelIcon, ArrowsUpDownIcon, ChevronLeftIcon } from '@heroicons/react/20/solid';

// Mock procurement plans for super admin view
const mockProcurementPlans = [
  {
    id: 1,
    planName: 'Q1 2024 Office Supplies',
    department: 'Administration',
    status: 'Active',
    priority: 'Medium',
    totalBudget: 50000,
    spentAmount: 32500,
    startDate: '2024-01-01',
    endDate: '2024-03-31',
    approver: '<PERSON>',
    itemsCount: 15,
    createdDate: '2024-01-15',
    description: 'Quarterly procurement plan for office supplies and equipment'
  },
  {
    id: 2,
    planName: 'Vehicle Maintenance 2024',
    department: 'Mechanical',
    status: 'Active',
    priority: 'High',
    totalBudget: 120000,
    spentAmount: 45000,
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    approver: '<PERSON>',
    itemsCount: 28,
    createdDate: '2023-12-20',
    description: 'Annual vehicle maintenance and spare parts procurement'
  },
  {
    id: 3,
    planName: 'IT Infrastructure Upgrade',
    department: 'IT',
    status: 'Draft',
    priority: 'High',
    totalBudget: 200000,
    spentAmount: 0,
    startDate: '2024-04-01',
    endDate: '2024-09-30',
    approver: 'Michael Brown',
    itemsCount: 12,
    createdDate: '2024-02-10',
    description: 'Technology refresh and infrastructure improvements'
  },
  {
    id: 4,
    planName: 'Construction Materials Q2',
    department: 'Civil Works',
    status: 'Completed',
    priority: 'Medium',
    totalBudget: 75000,
    spentAmount: 73500,
    startDate: '2024-04-01',
    endDate: '2024-06-30',
    approver: 'David Wilson',
    itemsCount: 22,
    createdDate: '2024-03-01',
    description: 'Building materials and construction supplies'
  },
  {
    id: 5,
    planName: 'Security Equipment',
    department: 'Security',
    status: 'Pending Approval',
    priority: 'High',
    totalBudget: 85000,
    spentAmount: 0,
    startDate: '2024-07-01',
    endDate: '2024-12-31',
    approver: 'Lisa Anderson',
    itemsCount: 8,
    createdDate: '2024-05-15',
    description: 'Security systems and surveillance equipment'
  },
  {
    id: 6,
    planName: 'Legal Services Annual',
    department: 'Legal & Audit',
    status: 'Active',
    priority: 'Low',
    totalBudget: 35000,
    spentAmount: 12000,
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    approver: 'Robert Clark',
    itemsCount: 5,
    createdDate: '2023-11-30',
    description: 'Annual legal and audit services procurement'
  },
  {
    id: 7,
    planName: 'Design Software Licenses',
    department: 'Design',
    status: 'Active',
    priority: 'Medium',
    totalBudget: 45000,
    spentAmount: 15000,
    startDate: '2024-01-01',
    endDate: '2024-12-31',
    approver: 'Daniel Martinez',
    itemsCount: 8,
    createdDate: '2023-12-10',
    description: 'Annual design software and licensing'
  },
  {
    id: 8,
    planName: 'Emergency Supplies',
    department: 'Administration',
    status: 'Urgent',
    priority: 'High',
    totalBudget: 25000,
    spentAmount: 5000,
    startDate: '2024-08-01',
    endDate: '2024-10-31',
    approver: 'John Smith',
    itemsCount: 12,
    createdDate: '2024-07-25',
    description: 'Emergency supplies and equipment procurement'
  }
];

export default function ProcurementPlanPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [statusFilter, setStatusFilter] = useState('All');

  // Filter plans based on search term and status
  const filteredPlans = mockProcurementPlans.filter(plan => {
    const matchesSearch = plan.planName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plan.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         plan.department.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesStatus = statusFilter === 'All' || plan.status === statusFilter;
    return matchesSearch && matchesStatus;
  });

  const total = filteredPlans.length;
  const totalPages = Math.max(1, Math.ceil(total / rowsPerPage));
  const safePage = Math.min(currentPage, totalPages);
  const startIndex = total === 0 ? 0 : (safePage - 1) * rowsPerPage;
  const endIndex = Math.min(startIndex + rowsPerPage, total);
  const pageItems = filteredPlans.slice(startIndex, endIndex);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Active': return 'bg-green-100 text-green-800';
      case 'Draft': return 'bg-gray-100 text-gray-800';
      case 'Completed': return 'bg-blue-100 text-blue-800';
      case 'Pending Approval': return 'bg-yellow-100 text-yellow-800';
      case 'Urgent': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'High': return 'bg-red-100 text-red-800';
      case 'Medium': return 'bg-yellow-100 text-yellow-800';
      case 'Low': return 'bg-green-100 text-green-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const calculateProgress = (spent: number, total: number) => {
    return total > 0 ? Math.round((spent / total) * 100) : 0;
  };
  return (
    <div className="px-16 py-6">
      {/* Breadcrumb */}
      <div className="mb-4">
        <div className="text-sm text-gray-500">
          🏠 Tools / Procurement plan
        </div>
      </div>

      {/* Header Bar */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h1 className="text-xl font-semibold">Procurement plan</h1>
        </div>
        <Button className="bg-[#18546c] hover:bg-[#1a6985] text-white">
          <PlusIcon className="w-4 h-4 mr-2" />
          Create New
        </Button>
      </div>

      {/* Toolbar */}
      <div className="flex items-center justify-between bg-white border rounded-md px-3 py-2 mb-3">
        <div className="flex items-center gap-3 flex-1">
          <div className="flex items-center bg-gray-50 border rounded-md px-3 py-2 w-full max-w-md">
            <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 mr-2" />
            <input
              value={searchTerm}
              onChange={(e) => { setSearchTerm(e.target.value); setCurrentPage(1); }}
              placeholder="Search plans, departments..."
              className="bg-transparent outline-none w-full text-sm"
            />
          </div>
          <select
            value={statusFilter}
            onChange={(e) => { setStatusFilter(e.target.value); setCurrentPage(1); }}
            className="border rounded-md px-3 py-2 text-sm bg-white"
          >
            <option value="All">All Status</option>
            <option value="Active">Active</option>
            <option value="Draft">Draft</option>
            <option value="Completed">Completed</option>
            <option value="Pending Approval">Pending Approval</option>
            <option value="Urgent">Urgent</option>
          </select>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="text-sm">
            <FunnelIcon className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" className="text-sm">
            <ArrowsUpDownIcon className="w-4 h-4 mr-2" />
            Sort by
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead>
              <tr className="bg-gray-50 text-gray-600">
                <th className="px-4 py-3 text-left font-medium">Plan Name</th>
                <th className="px-4 py-3 text-left font-medium">Department</th>
                <th className="px-4 py-3 text-left font-medium">Status</th>
                <th className="px-4 py-3 text-left font-medium">Priority</th>
                <th className="px-4 py-3 text-left font-medium">Budget Progress</th>
                <th className="px-4 py-3 text-left font-medium">Duration</th>
                <th className="px-4 py-3 text-left font-medium">Items</th>
                <th className="px-4 py-3 text-left font-medium">Approver</th>
                <th className="px-4 py-3 text-left font-medium">Created</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {pageItems.length === 0 ? (
                <tr>
                  <td colSpan={9} className="px-4 py-10 text-center text-gray-500">
                    No procurement plans found matching your criteria.
                  </td>
                </tr>
              ) : (
                pageItems.map((plan) => {
                  const progress = calculateProgress(plan.spentAmount, plan.totalBudget);
                  return (
                    <tr key={plan.id} className="hover:bg-gray-50 cursor-pointer">
                      <td className="px-4 py-3">
                        <div>
                          <div className="font-medium text-gray-900">{plan.planName}</div>
                          <div className="text-gray-500 text-xs truncate max-w-xs">{plan.description}</div>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-gray-900">{plan.department}</td>
                      <td className="px-4 py-3">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(plan.status)}`}>
                          {plan.status}
                        </span>
                      </td>
                      <td className="px-4 py-3">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getPriorityColor(plan.priority)}`}>
                          {plan.priority}
                        </span>
                      </td>
                      <td className="px-4 py-3">
                        <div className="w-full">
                          <div className="flex justify-between text-xs mb-1">
                            <span>{formatCurrency(plan.spentAmount)}</span>
                            <span>{progress}%</span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-600 h-2 rounded-full"
                              style={{ width: `${Math.min(progress, 100)}%` }}
                            ></div>
                          </div>
                          <div className="text-xs text-gray-500 mt-1">of {formatCurrency(plan.totalBudget)}</div>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-gray-900">
                        <div className="text-xs">
                          <div>{new Date(plan.startDate).toLocaleDateString()}</div>
                          <div className="text-gray-500">to {new Date(plan.endDate).toLocaleDateString()}</div>
                        </div>
                      </td>
                      <td className="px-4 py-3 text-gray-900">{plan.itemsCount}</td>
                      <td className="px-4 py-3 text-gray-900">{plan.approver}</td>
                      <td className="px-4 py-3 text-gray-500">{new Date(plan.createdDate).toLocaleDateString()}</td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between px-3 py-2 border-t bg-white text-sm">
          <div className="text-gray-500">Showing {total === 0 ? 0 : startIndex + 1} to {endIndex} of {total}</div>
          <div className="flex items-center gap-2">
            <select
              value={rowsPerPage}
              onChange={(e) => { setRowsPerPage(Number(e.target.value)); setCurrentPage(1); }}
              className="text-gray-500 bg-transparent border-0 text-sm focus:ring-0"
            >
              <option value={5}>5 rows</option>
              <option value={10}>10 rows</option>
              <option value={15}>15 rows</option>
              <option value={20}>20 rows</option>
            </select>
            <div className="flex items-center gap-1">
              <Button variant="outline" size="icon" disabled={safePage === 1} onClick={() => setCurrentPage(p => Math.max(1, p - 1))}>
                <ChevronLeftIcon className="w-4 h-4" />
              </Button>
              <span className="px-2">{safePage}</span>
              <Button variant="outline" size="icon" disabled={safePage === totalPages} onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}>
                <ChevronRightIcon className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
