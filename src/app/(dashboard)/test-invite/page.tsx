'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { toast } from 'sonner';
import Link from 'next/link';

// Test page for invite functionality
export default function TestInvitePage() {
  const [email, setEmail] = useState('');
  const [role, setRole] = useState('c_requesting_user');
  const [isLoading, setIsLoading] = useState(false);
  const [response, setResponse] = useState<any>(null);

  const sendTestInvite = async () => {
    if (!email) {
      toast.error('Please enter an email address');
      return;
    }

    setIsLoading(true);
    setResponse(null);

    try {
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      // Use auth invite endpoint for all c_admin invitable roles
      const endpoint = '/api/proxy/auth/invite';
      const payload = { email: email, role_name: role };

      console.log('Test invite:', { endpoint, payload });

      const res = await fetch(endpoint, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(payload)
      });

      const data = await res.json();
      setResponse({ status: res.status, data });

      if (res.ok) {
        toast.success(`Invitation sent successfully to ${email}`);
      } else {
        toast.error(data.detail || 'Failed to send invitation');
      }
    } catch (error: any) {
      console.error('Error:', error);
      toast.error(error.message || 'Failed to send invitation');
      setResponse({ error: error.message });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-2xl mx-auto">
      <div className="mb-6">
        <Link href="/admin-settings/manage-users" className="text-blue-600 hover:underline">
          ← Back to Manage Users
        </Link>
      </div>

      <div className="bg-white dark:bg-gray-800 rounded-lg shadow p-6">
        <h1 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white">
          Test Invite Functionality
        </h1>

        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Email Address
            </label>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
              className="w-full"
              disabled={isLoading}
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
              Role
            </label>
            <select
              value={role}
              onChange={(e) => setRole(e.target.value)}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
              disabled={isLoading}
            >
              <option value="c_requesting_user">Requesting User</option>
              <option value="c_stores_manager">Stores Manager</option>
              <option value="c_accountant">Accountant</option>
              <option value="c_procurement_officer">Procurement Officer</option>
              <option value="c_approving_manager">Approving Manager</option>
            </select>
          </div>

          <Button
            onClick={sendTestInvite}
            disabled={isLoading}
            className="w-full bg-[#18546c] hover:bg-[#18546c]/90 text-white"
          >
            {isLoading ? 'Sending Invite...' : 'Send Test Invite'}
          </Button>
        </div>

        {response && (
          <div className="mt-6 p-4 bg-gray-100 dark:bg-gray-700 rounded-lg">
            <h3 className="font-medium text-gray-900 dark:text-white mb-2">Response:</h3>
            <pre className="text-sm text-gray-700 dark:text-gray-300 overflow-auto">
              {JSON.stringify(response, null, 2)}
            </pre>
          </div>
        )}

        <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <h3 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
            API Endpoint Details:
          </h3>
          <div className="text-sm text-blue-700 dark:text-blue-300 space-y-2">
            <p><strong>Endpoint:</strong> POST /api/proxy/auth/invite</p>
            <p><strong>Backend:</strong> {'{BASE_URL}'}/auth/invite</p>
            <p><strong>Access:</strong> c_admin only</p>
            <p><strong>Payload:</strong></p>
            <pre className="bg-blue-100 dark:bg-blue-800 p-2 rounded text-xs">
{`{
  "email": "${email || '{TEST_COMPANY_USER_EMAIL}'}",
  "role_name": "${role}"
}`}
            </pre>
          </div>
        </div>
      </div>
    </div>
  );
}
