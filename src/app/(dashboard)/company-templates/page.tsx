'use client';

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Edit, Trash2, Eye, ChevronRight, Home } from 'lucide-react';
import { FiFileText } from 'react-icons/fi';
import Link from 'next/link';

type CompanyTemplate = {
  id: string;
  name: string;
  type: string;
  description: string;
  office: string;
  actions: {
    edit: boolean;
    delete: boolean;
    preview: boolean;
  };
};

const CompanyTemplatesPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filter, setFilter] = useState('all');

  // Sample data for company templates
  const templates: CompanyTemplate[] = [
    {
      id: '1',
      name: 'Company template',
      type: 'Company Agreement',
      description: 'Create, view and edit templates here',
      office: 'UNRA Head Office',
      actions: { edit: true, delete: true, preview: true }
    },
    {
      id: '2',
      name: 'Corporate template',
      type: 'Corporate Policy',
      description: 'Create, view and edit templates here',
      office: 'UNRA Head Office',
      actions: { edit: true, delete: true, preview: true }
    },
    {
      id: '3',
      name: 'Business template',
      type: 'Business Plan',
      description: 'Create, view and edit templates here',
      office: 'UNRA Head Office',
      actions: { edit: true, delete: true, preview: true }
    },
    {
      id: '4',
      name: 'Policy template',
      type: 'Company Policy',
      description: 'Create, view and edit templates here',
      office: 'UNRA Moya Office',
      actions: { edit: true, delete: true, preview: true }
    },
    {
      id: '5',
      name: 'Procedure template',
      type: 'Standard Procedure',
      description: 'Create, view and edit templates here',
      office: 'UNRA Lira Office',
      actions: { edit: true, delete: true, preview: true }
    },
    {
      id: '6',
      name: 'Manual template',
      type: 'User Manual',
      description: 'Create, view and edit templates here',
      office: 'UNRA Mbale Office',
      actions: { edit: true, delete: true, preview: true }
    }
  ];

  const filteredTemplates = templates.filter(template => {
    const matchesSearch = template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         template.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filter === 'all' || template.type === filter;
    return matchesSearch && matchesFilter;
  });

  return (
    <div className="flex flex-col h-full p-16">
      {/* Breadcrumb Navigation */}
      <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-4">
        <Link href="/dashboard" className="flex items-center hover:text-gray-700">
          <Home className="h-4 w-4 mr-1" />
          Dashboard
        </Link>
        <ChevronRight className="h-4 w-4" />
        <span className="text-gray-900 dark:text-white font-medium">Company Templates</span>
      </nav>

      {/* Page Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center space-x-4">
          <h1 className="text-2xl font-bold text-gray-900 dark:text-white">Company templates</h1>
        </div>
        <button className="bg-gray-100 text-[#18546c] px-4 py-2 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md">
          <span className="mr-1.5 text-base">+</span>
          Create New
        </button>
      </div>

      {/* Template Cards Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredTemplates.map((template) => (
          <Card key={template.id} className="bg-white dark:bg-slate-800 border border-gray-200 dark:border-gray-700">
            <CardHeader className="pb-3">
              <div className="flex items-center justify-between">
                <CardTitle className="text-lg font-semibold text-blue-600 dark:text-blue-400">
                  {template.name}
                </CardTitle>
                <button className="bg-gray-100 text-[#18546c] px-3 py-1 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 flex items-center font-medium text-xs shadow hover:shadow-md">
                  <span className="mr-1 text-sm">+</span>
                  Create New
                </button>
              </div>
              <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
                {template.description}
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Template Items */}
              <div className="space-y-2">
                <div className="flex items-center justify-between p-2 bg-gray-50 dark:bg-slate-700 rounded">
                  <div className="flex items-center space-x-2">
                    <FiFileText className="h-4 w-4 text-gray-500" />
                    <span className="text-sm font-medium">{template.office}</span>
                  </div>
                  <div className="flex items-center space-x-1">
                    {template.actions.edit && (
                      <button className="px-2 py-1 text-[#18546c] hover:bg-[#18546c] hover:text-white rounded transition-colors duration-200 flex items-center text-xs font-medium">
                        <Edit className="h-3 w-3 mr-1" />
                        Edit
                      </button>
                    )}
                    {template.actions.delete && (
                      <button className="px-2 py-1 text-red-600 hover:bg-red-600 hover:text-white rounded transition-colors duration-200 flex items-center text-xs font-medium">
                        <Trash2 className="h-3 w-3 mr-1" />
                        Delete
                      </button>
                    )}
                  </div>
                </div>
              </div>

              {/* Number Format Section */}
              <div className="border-t pt-4">
                <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-2">
                  {template.type} number
                </h4>
                <p className="text-xs text-gray-500 mb-3">
                  Create, view and {template.type.toLowerCase()} number format
                </p>

                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm text-gray-600">Reset format</span>
                </div>

                <div className="flex items-center justify-between mb-3">
                  <span className="text-sm">Increase by</span>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm">0</span>
                    <span className="text-xs text-gray-500">-</span>
                  </div>
                </div>

                <div className="flex items-center space-x-2 mb-4">
                  <Eye className="h-4 w-4 text-gray-500" />
                  <span className="text-sm text-gray-600">Preview</span>
                </div>

                <div className="flex space-x-2">
                  <button className="bg-gray-100 text-[#18546c] px-3 py-1 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 font-medium text-xs shadow hover:shadow-md">
                    Set same as reference
                  </button>
                  <button className="bg-gray-100 text-[#18546c] px-3 py-1 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 font-medium text-xs shadow hover:shadow-md">
                    Set format
                  </button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>


    </div>
  );
};

export default CompanyTemplatesPage;
