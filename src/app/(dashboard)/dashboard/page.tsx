'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import { Loading } from '@/components/global/Loading';
import { fastAuthRedirect, getRoleDescription } from '@/utils/roleRedirect';

export default function DashboardPage() {
  const { user, isAuthenticated } = useAuth();
  const router = useRouter();
  const [redirectAttempted, setRedirectAttempted] = useState(false);

  useEffect(() => {
    // Prevent infinite redirection loops
    if (redirectAttempted) {
      return;
    }

    if (isAuthenticated === false) {
      console.log('Dashboard: Not authenticated, redirecting to login');
      router.push('/login');
      setRedirectAttempted(true);
      return;
    }

    if (isAuthenticated === true && user) {
      console.log('Dashboard: User is authenticated, user data:', user);
      
      // Fast auth-based redirection - this should rarely be reached due to middleware
      if (!user.role) {
        console.warn('User role is missing, showing generic dashboard');
        setRedirectAttempted(true);
        // Don't redirect, just show a generic dashboard below
      } else {
        const roleDescription = getRoleDescription(user.role);
        console.log(`Dashboard fallback: ${roleDescription} (${user.role}) - middleware should have handled this`);

        // Use fast auth redirect as fallback (middleware should handle most cases)
        fastAuthRedirect(user.role, router, `Dashboard fallback redirect for ${roleDescription}`);
        setRedirectAttempted(true);
      }
    }
  }, [isAuthenticated, router, user, redirectAttempted]);

  // If we've attempted to redirect but we're still here, show a generic dashboard
  if (redirectAttempted || !user) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Dashboard</h1>
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
          <h2 className="text-xl font-semibold mb-4">Welcome to Ascension</h2>
          <p className="mb-4">Your user account is active, but your role configuration may be incomplete.</p>
          
          {user && (
            <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-md">
              <h3 className="font-medium mb-2">Your Account Information</h3>
              <p><strong>Email:</strong> {user.email}</p>
              <p><strong>Name:</strong> {user.full_name || 'Not provided'}</p>
              <p><strong>Role:</strong> {user.role || 'Not assigned'}</p>
            </div>
          )}
          
          <div className="mt-6">
            <p>Please contact your administrator if you need access to specific features.</p>
          </div>
        </div>
      </div>
    );
  }

  // Show loading state while redirecting
  return <Loading fullScreen text="Loading your dashboard..." size="lg" />;
}
