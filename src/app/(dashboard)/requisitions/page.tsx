'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { PlusIcon, MagnifyingGlassIcon, FunnelIcon, ArrowsUpDownIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/20/solid';

// Mock requisitions data matching the screenshot
const mockRequisitions = [
  { id: 1, projectId: '0002', year: 2024, caseNo: '180', reference: 'UNICEF/0002/24/180', subject: 'Spare parts vehicle UAG 257R', status: 'Requisition', dateCreated: 'Oct 14, 2024', budgetCode: '228002', budgetCategory: 'Maintenance vehicles' },
  { id: 2, projectId: '0002', year: 2024, caseNo: '179', reference: 'UNICEF/0002/24/179', subject: 'Lawn mower', status: 'Requisition', dateCreated: 'Oct 12, 2024', budgetCode: '228003', budgetCategory: 'Maintenance machinery' },
  { id: 3, projectId: '0002', year: 2024, caseNo: '178', reference: 'UNICEF/0002/24/178', subject: 'Water pump spares', status: 'Requisition', dateCreated: 'Oct 11, 2024', budgetCode: '228004', budgetCategory: 'Construction equipment' },
  { id: 4, projectId: '0002', year: 2024, caseNo: '177', reference: 'UNICEF/0002/24/177', subject: 'Carburetor assembly', status: 'Requisition', dateCreated: 'Oct 13, 2024', budgetCode: '228005', budgetCategory: 'Maintenance machinery' },
  { id: 5, projectId: '0002', year: 2024, caseNo: '176', reference: 'UNICEF/0002/24/176', subject: 'Spark plugs set', status: 'Requisition', dateCreated: 'Oct 10, 2024', budgetCode: '228006', budgetCategory: 'Maintenance machinery' },
  { id: 6, projectId: '0002', year: 2024, caseNo: '175', reference: 'UNICEF/0002/24/175', subject: 'Muffler exhaust pipe', status: 'Submitted', dateCreated: 'Oct 9, 2024', budgetCode: '228007', budgetCategory: 'Maintenance machinery' },
  { id: 7, projectId: '0002', year: 2024, caseNo: '174', reference: 'UNICEF/0002/24/174', subject: 'Piston rings kit', status: 'Submitted', dateCreated: 'Oct 8, 2024', budgetCode: '228008', budgetCategory: 'Maintenance vehicles' },
  { id: 8, projectId: '0002', year: 2024, caseNo: '173', reference: 'UNICEF/0002/24/173', subject: 'Fuel filter replacement', status: 'Submitted', dateCreated: 'Oct 7, 2024', budgetCode: '228009', budgetCategory: 'Maintenance vehicles' },
  { id: 9, projectId: '0002', year: 2024, caseNo: '172', reference: 'UNICEF/0002/24/172', subject: 'Air filter element', status: 'Submitted', dateCreated: 'Oct 6, 2024', budgetCode: '228010', budgetCategory: 'Maintenance vehicles' },
  { id: 10, projectId: '0002', year: 2024, caseNo: '171', reference: 'UNICEF/0002/24/171', subject: 'Ignition coil module', status: 'Submitted', dateCreated: 'Oct 5, 2024', budgetCode: '228011', budgetCategory: 'Maintenance vehicles' },
  { id: 11, projectId: '0002', year: 2024, caseNo: '170', reference: 'UNICEF/0002/24/170', subject: 'Oil change kit', status: 'Submitted', dateCreated: 'Oct 4, 2024', budgetCode: '228012', budgetCategory: 'Maintenance machinery' },
  { id: 12, projectId: '0002', year: 2024, caseNo: '169', reference: 'UNICEF/0002/24/169', subject: 'Brake pads set', status: 'Submitted', dateCreated: 'Oct 3, 2024', budgetCode: '228013', budgetCategory: 'Maintenance vehicles' },
  { id: 13, projectId: '0002', year: 2024, caseNo: '168', reference: 'UNICEF/0002/24/168', subject: 'Transmission fluid', status: 'Submitted', dateCreated: 'Oct 2, 2024', budgetCode: '228014', budgetCategory: 'Maintenance vehicles' },
  { id: 14, projectId: '0002', year: 2024, caseNo: '167', reference: 'UNICEF/0002/24/167', subject: 'Wheel alignment kit', status: 'Submitted', dateCreated: 'Oct 1, 2024', budgetCode: '228015', budgetCategory: 'Maintenance vehicles' },
  { id: 15, projectId: '0002', year: 2024, caseNo: '166', reference: 'UNICEF/0002/24/166', subject: 'Radiator coolant', status: 'Submitted', dateCreated: 'Sep 31, 2024', budgetCode: '228016', budgetCategory: 'Maintenance vehicles' },
  { id: 16, projectId: '0002', year: 2024, caseNo: '165', reference: 'UNICEF/0002/24/165', subject: 'Wheel alignment kit', status: 'Submitted', dateCreated: 'Sep 30, 2024', budgetCode: '228017', budgetCategory: 'Maintenance vehicles' },
  { id: 17, projectId: '0002', year: 2024, caseNo: '164', reference: 'UNICEF/0002/24/164', subject: 'Spare parts vehicle UAG 257R', status: 'Submitted', dateCreated: 'Sep 29, 2024', budgetCode: '228018', budgetCategory: 'Maintenance vehicles' },
];

export default function RequisitionsPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage] = useState(17);

  // Filter requisitions based on search term
  const filteredRequisitions = mockRequisitions.filter(req => {
    const term = searchTerm.toLowerCase();
    return (
      req.projectId.toLowerCase().includes(term) ||
      req.year.toString().includes(term) ||
      req.caseNo.toLowerCase().includes(term) ||
      req.reference.toLowerCase().includes(term) ||
      req.subject.toLowerCase().includes(term) ||
      req.status.toLowerCase().includes(term) ||
      req.budgetCode.toLowerCase().includes(term) ||
      req.budgetCategory.toLowerCase().includes(term)
    );
  });

  const total = filteredRequisitions.length;
  const totalPages = Math.max(1, Math.ceil(total / rowsPerPage));
  const safePage = Math.min(currentPage, totalPages);
  const startIndex = total === 0 ? 0 : (safePage - 1) * rowsPerPage;
  const endIndex = Math.min(startIndex + rowsPerPage, total);
  const pageItems = filteredRequisitions.slice(startIndex, endIndex);

  return (
    <div className="px-4 py-6">
      {/* Header Bar */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h1 className="text-xl font-semibold">Requisitions</h1>
        </div>
        <Button className="bg-[#18546c] hover:bg-[#1a6985], text-white">
          <PlusIcon className="w-4 h-4 mr-2" />
          Create New
        </Button>
      </div>

      {/* Toolbar */}
      <div className="flex items-center justify-between bg-white border rounded-md px-3 py-2 mb-3">
        <div className="flex items-center gap-3 flex-1">
          <div className="flex items-center bg-gray-50 border rounded-md px-3 py-2 w-full max-w-md">
            <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 mr-2" />
            <input
              value={searchTerm}
              onChange={(e) => { setSearchTerm(e.target.value); setCurrentPage(1); }}
              placeholder="Search ID, Year, Subject, Reference..."
              className="bg-transparent outline-none w-full text-sm"
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="text-sm">
            <FunnelIcon className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" className="text-sm">
            <ArrowsUpDownIcon className="w-4 h-4 mr-2" />
            Sort by
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead>
              <tr className="bg-gray-50 text-gray-600">
                <th className="px-4 py-3 text-left font-medium">Project ID</th>
                <th className="px-4 py-3 text-left font-medium">Year</th>
                <th className="px-4 py-3 text-left font-medium">Case No.</th>
                <th className="px-4 py-3 text-left font-medium">Reference</th>
                <th className="px-4 py-3 text-left font-medium">Subject</th>
                <th className="px-4 py-3 text-left font-medium">Status</th>
                <th className="px-4 py-3 text-left font-medium">Date created</th>
                <th className="px-4 py-3 text-left font-medium">Budget code</th>
                <th className="px-4 py-3 text-left font-medium">Budget category</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {pageItems.length === 0 ? (
                <tr>
                  <td colSpan={9} className="px-4 py-10 text-center text-gray-500">No requisitions found.</td>
                </tr>
              ) : (
                pageItems.map((req, index) => (
                  <tr key={req.id} className={`hover:bg-gray-100 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                    <td className="px-4 py-3">{req.projectId}</td>
                    <td className="px-4 py-3">{req.year}</td>
                    <td className="px-4 py-3">{req.caseNo}</td>
                    <td className="px-4 py-3 text-[#18546c]">{req.reference}</td>
                    <td className="px-4 py-3">{req.subject}</td>
                    <td className="px-4 py-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        req.status === 'Submitted' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                      }`}>
                        {req.status}
                      </span>
                    </td>
                    <td className="px-4 py-3">{req.dateCreated}</td>
                    <td className="px-4 py-3">{req.budgetCode}</td>
                    <td className="px-4 py-3">{req.budgetCategory}</td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between px-3 py-2 border-t bg-white text-sm">
          <div className="text-gray-500">Showing {total === 0 ? 0 : startIndex + 1} to {endIndex} of {total}</div>
          <div className="flex items-center gap-2">
            <span className="text-gray-500">{rowsPerPage} rows</span>
            <div className="flex items-center gap-1">
              <Button variant="outline" size="icon" disabled={safePage === 1} onClick={() => setCurrentPage(p => Math.max(1, p - 1))}>
                <ChevronLeftIcon className="w-4 h-4" />
              </Button>
              <span className="px-2">{safePage}</span>
              <Button variant="outline" size="icon" disabled={safePage === totalPages} onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}>
                <ChevronRightIcon className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
