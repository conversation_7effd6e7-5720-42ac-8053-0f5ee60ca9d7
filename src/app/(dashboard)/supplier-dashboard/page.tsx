'use client';

import React, { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import SupplierDashboard from '@/components/dashboard/SupplierDashboard';
import { useGlobalPreloader } from '@/hooks/useGlobalPreloader';

export default function SupplierDashboardPage() {
  const { user, isAuthenticated, logout } = useAuth();
  const router = useRouter();
  const { showPreloader, hidePreloader } = useGlobalPreloader();
  const [accessError, setAccessError] = useState<string | null>(null);

  React.useEffect(() => {
    if (isAuthenticated === false) {
      router.push('/login');
    } else if (isAuthenticated === true && user) {
      // Hide preloader once authenticated
      hidePreloader();
      // Check if user has the correct role
      if (!user.role) {
        setAccessError('Your user account is missing a role assignment.');
      } else if (user.role !== 's_admin') {
        setAccessError(`Your current role (${user.role}) does not have permission to access the supplier dashboard.`);
      } else {
        // Role is correct, clear any error
        setAccessError(null);
      }
    } else {
      // Show beautiful preloader while checking authentication
      showPreloader('Checking authentication...', 'elegant');
    }
  }, [isAuthenticated, router, user, showPreloader, hidePreloader]);

  if (!isAuthenticated) {
    // The beautiful preloader is shown via the global preloader system
    return null;
  }



  // Show access error message if there's an issue with the user's role
  if (accessError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <h1 className="text-2xl font-bold mb-6">Access Restricted</h1>
        
        <div className="bg-white dark:bg-slate-800 p-6 rounded-lg shadow-md">
          <div className="flex items-center text-amber-600 mb-4">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
            </svg>
            <h2 className="text-xl font-semibold">Permission Issue</h2>
          </div>
          
          <p className="mb-4">{accessError}</p>
          
          <p className="mb-6">Please contact your administrator if you believe you should have access to this page.</p>
          
          <div className="flex space-x-4">
            <button 
              onClick={() => router.push('/dashboard')} 
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Go to Dashboard
            </button>
            <button 
              onClick={() => logout()} 
              className="px-4 py-2 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-100 rounded hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If we get here, user has the correct role and is not null
  // (TypeScript doesn't recognize our checks above guarantee user is not null)
  return user ? <SupplierDashboard user={user} /> : null;
}
