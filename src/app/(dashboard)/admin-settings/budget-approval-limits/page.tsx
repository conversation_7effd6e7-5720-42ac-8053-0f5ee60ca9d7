'use client';

import React, { useState } from 'react';
import { PlusIcon, ChevronDownIcon } from '@heroicons/react/20/solid';
import { Home, Settings } from 'lucide-react';

export default function BudgetApprovalLimitsPage() {
  const [budgetLimits, setBudgetLimits] = useState([
    { id: 1, currency: 'UGX', amount: '5,000,000', level: 'Level 1' },
    { id: 2, currency: 'UGX', amount: '50,000,000', level: 'Level 2' },
    { id: 3, currency: 'UGX', amount: '100,000,000', level: 'Level 3' },
    { id: 4, currency: 'UGX', amount: '', level: 'Select applicable' },
    { id: 5, currency: 'UGX', amount: '', level: 'Select applicable' },
  ]);



  const [isEditing, setIsEditing] = useState(false);

  const addNewLimit = () => {
    const newLimit = {
      id: budgetLimits.length + 1,
      currency: 'UGX',
      amount: '',
      level: 'Select applicable'
    };
    setBudgetLimits([...budgetLimits, newLimit]);
  };

  const removeLastLimit = () => {
    if (budgetLimits.length > 3) { // Keep at least the first 3 items
      setBudgetLimits(budgetLimits.slice(0, -1));
    }
  };

  const updateBudgetLimit = (id: number, field: string, value: string) => {
    setBudgetLimits(budgetLimits.map(limit =>
      limit.id === id ? { ...limit, [field]: value } : limit
    ));
  };

  const hasFilledData = (limit: any) => {
    return limit.amount && limit.amount !== '' && limit.level !== 'Select applicable';
  };

  return (
    <div className="h-full overflow-auto">
      {/* Page Header */}
      <div className="w-full">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Home className="h-4 w-4" />
              <span>Budget approval limits</span>
            </nav>
            <h1 className="text-xl font-medium text-gray-900">Budget approval limits</h1>
          </div>
          <button className="bg-gray-100 text-[#18546c] px-4 py-2 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md">
            <span>Save & publish</span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="px-96 py-6 space-y-4">

        {/* Budget approval limits section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200">
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <Settings className="h-5 w-5 text-gray-600" />
              <h2 className="text-lg font-medium text-gray-900">Budget approval limits</h2>
            </div>
          </div>

          <div className="p-6">
            <div className="grid grid-cols-2 gap-8 mb-6">
              <div className="text-sm font-medium text-gray-700">Budget amount up to</div>
              <div className="text-sm font-medium text-gray-700">Approver level</div>
            </div>

            <div className="space-y-4">
              {budgetLimits.map((limit) => {
                const isFilled = hasFilledData(limit);
                const shouldBeInactive = isFilled && !isEditing;

                return (
                  <div key={limit.id} className="grid grid-cols-2 gap-8 items-center">
                    <div className="flex items-center space-x-2">
                      <div className="relative">
                        <select
                          value={limit.currency}
                          onChange={(e) => updateBudgetLimit(limit.id, 'currency', e.target.value)}
                          disabled={shouldBeInactive}
                          className={`appearance-none border rounded px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                            shouldBeInactive
                              ? 'bg-gray-100 border-gray-200 text-gray-500 cursor-not-allowed'
                              : 'bg-white border-gray-300 text-gray-900'
                          }`}
                        >
                          <option value="UGX">UGX</option>
                          <option value="USD">USD</option>
                          <option value="EUR">EUR</option>
                        </select>
                        <ChevronDownIcon className={`absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 pointer-events-none ${
                          shouldBeInactive ? 'text-gray-300' : 'text-gray-400'
                        }`} />
                      </div>
                      <input
                        type="text"
                        value={limit.amount}
                        onChange={(e) => updateBudgetLimit(limit.id, 'amount', e.target.value)}
                        disabled={shouldBeInactive}
                        className={`flex-1 border rounded px-3 py-2 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                          shouldBeInactive
                            ? 'bg-gray-100 border-gray-200 text-gray-500 cursor-not-allowed'
                            : 'bg-white border-gray-300 text-gray-900'
                        }`}
                        placeholder="Amount here..."
                      />
                    </div>
                    <div className="relative">
                      <select
                        value={limit.level}
                        onChange={(e) => updateBudgetLimit(limit.id, 'level', e.target.value)}
                        disabled={shouldBeInactive}
                        className={`appearance-none w-full border rounded px-3 py-2 pr-8 text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent ${
                          shouldBeInactive
                            ? 'bg-gray-100 border-gray-200 text-gray-500 cursor-not-allowed'
                            : 'bg-white border-gray-300 text-gray-900'
                        }`}
                      >
                        <option value="Level 1">Level 1</option>
                        <option value="Level 2">Level 2</option>
                        <option value="Level 3">Level 3</option>
                        <option value="Select applicable">Select applicable</option>
                      </select>
                      <ChevronDownIcon className={`absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 pointer-events-none ${
                        shouldBeInactive ? 'text-gray-300' : 'text-gray-400'
                      }`} />
                    </div>
                  </div>
                );
              })}
            </div>

            <div className="flex items-center justify-between mt-6 pt-4 border-t border-gray-200">
              <div className="flex items-center space-x-4">
                <button
                  onClick={addNewLimit}
                  className="flex items-center space-x-2 text-blue-600 hover:text-blue-800 text-sm"
                >
                  <PlusIcon className="w-4 h-4" />
                  <span>Add new limit</span>
                </button>
                {budgetLimits.length > 3 && (
                  <button
                    onClick={removeLastLimit}
                    className="flex items-center space-x-2 text-red-600 hover:text-red-800 text-sm"
                  >
                    <span>×</span>
                    <span>Remove last</span>
                  </button>
                )}
              </div>
              <div className="flex space-x-3">
                <button
                  onClick={() => setIsEditing(!isEditing)}
                  className="text-gray-600 hover:text-gray-800 text-sm"
                >
                  {isEditing ? 'Done editing' : 'Edit details'}
                </button>
                <button className="bg-[#18546c] hover:bg-[#164158] text-white px-6 py-2 rounded text-sm">
                  Save
                </button>
              </div>
            </div>
          </div>
        </div>


      </div>
    </div>
  );
}
