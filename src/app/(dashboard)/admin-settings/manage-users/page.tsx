'use client';

import { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ChevronRightIcon, ChevronLeftIcon, ChevronDownIcon, MagnifyingGlassIcon, ExclamationTriangleIcon } from '@heroicons/react/24/solid';
import Link from 'next/link';
import { toast } from 'sonner';
import { useAuth } from '@/context/AuthContext';

interface User {
  id: number;
  firstName: string;
  lastName: string;
  email: string;
  role: string;
  phoneNumber: string;
  status: 'Active' | 'Deactivated' | 'Pending';
}

// API User type (from backend)
interface ApiUser {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  telephone_number?: string;
  is_active: boolean;
  status?: string; // Add status field for pending/active/inactive
  organisation_id?: number;
  role_names?: string[];
  role?: string;
}

// Function to map API user to frontend User type
const mapApiUserToUser = (apiUser: ApiUser): User => {
  // Map role names to display names
  const roleMapping: { [key: string]: string } = {
    'c_stores_manager': 'Stores Manager',
    'c_accountant': 'Accountant',
    'c_procurement_officer': 'Procurement Officer',
    'c_approving_manager': 'Approving Manager',
    'c_requesting_user': 'Requesting User',
    'c_admin': 'Administrator'
  };

  // Get the first role or use the role field
  const roleKey = apiUser.role_names?.[0] || apiUser.role || '';
  const displayRole = roleMapping[roleKey] || roleKey || 'Unknown';

  // Map status - prioritize explicit status field over is_active
  let userStatus: 'Active' | 'Deactivated' | 'Pending' = 'Deactivated';
  if (apiUser.status) {
    // If explicit status is provided, use it
    switch (apiUser.status.toLowerCase()) {
      case 'pending':
        userStatus = 'Pending';
        break;
      case 'active':
        userStatus = 'Active';
        break;
      case 'inactive':
      case 'deactivated':
        userStatus = 'Deactivated';
        break;
      default:
        // For unknown statuses, default to Deactivated
        userStatus = 'Deactivated';
    }
  } else {
    // Fallback to is_active field
    userStatus = apiUser.is_active ? 'Active' : 'Deactivated';
  }

  return {
    id: apiUser.id,
    firstName: apiUser.first_name,
    lastName: apiUser.last_name,
    email: apiUser.email,
    role: displayRole,
    phoneNumber: apiUser.telephone_number || 'N/A',
    status: userStatus
  };
};

// Cache for users data
let usersCache: {
  data: User[] | null;
  timestamp: number;
  expiry: number;
} = {
  data: null,
  timestamp: 0,
  expiry: 5 * 60 * 1000 // 5 minutes cache
};

// Function to clear users cache
const clearUsersCache = () => {
  usersCache = {
    data: null,
    timestamp: 0,
    expiry: 5 * 60 * 1000
  };
  console.log('🗑️ Users cache cleared');
};

// Function to fetch users from the backend with caching
const fetchUsers = async (forceRefresh: boolean = false): Promise<User[]> => {
  try {
    // Check cache first (unless force refresh is requested)
    const now = Date.now();
    if (!forceRefresh && usersCache.data && (now - usersCache.timestamp) < usersCache.expiry) {
      console.log('📦 Using cached users data');
      return usersCache.data;
    }

    // Get auth token from localStorage
    const token = localStorage.getItem('access_token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    console.log('🔄 Fetching users from backend...');

    // Call the proxy endpoint
    const response = await fetch('/api/proxy/auth/users', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`,
        // Add cache control headers
        'Cache-Control': 'no-cache',
        'Pragma': 'no-cache'
      }
    });

    console.log('📡 Users API response status:', response.status);

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('❌ Failed to fetch users:', errorData);
      throw new Error(errorData.detail || errorData.message || 'Failed to fetch users');
    }

    const apiUsers: ApiUser[] = await response.json();
    console.log('✅ Users fetched successfully:', apiUsers.length, 'users');
    console.log('📋 Raw API response:', apiUsers);

    // Map API users to frontend User type
    const users = apiUsers.map(mapApiUserToUser);
    console.log('🔄 Mapped users:', users);
    console.log('📊 User statuses:', users.map(u => ({ email: u.email, status: u.status })));

    // Update cache
    usersCache = {
      data: users,
      timestamp: now,
      expiry: 5 * 60 * 1000 // 5 minutes
    };

    return users;
  } catch (error) {
    console.error('Error fetching users:', error);
    throw error;
  }
};

// Register user and send invitation - two-step process
const registerAndInviteUser = async (email: string, roleName: string, firstName: string, lastName: string, contactNumber: string, currentUser: any) => {
  console.log('🚀 NEW CODE: registerAndInviteUser function called with:', { email, roleName });
  try {
    // Get auth token from localStorage
    const token = localStorage.getItem('access_token');
    if (!token) {
      throw new Error('No authentication token found');
    }

    // Get current user's organization ID (check both spellings)
    const organizationId = currentUser?.organization_id || currentUser?.organisation_id;
    if (!organizationId) {
      throw new Error('Current user organization ID not found. Please contact support.');
    }

    console.log('Using organization ID:', organizationId);

    // Step 1: Create user in the EXISTING organization using the users/register endpoint
    console.log('🔍 Current user organization details:', {
      id: organizationId,
      name: currentUser?.organization_name || currentUser?.organisation_name,
      type: currentUser?.organization_type || 'company'
    });

    // Use the correct invite endpoint as per API documentation
    const inviteEndpoint = '/api/proxy/auth/invite';
    const invitePayload = {
      email: email,
      role_name: roleName // Use the internal role name (e.g., 'c_requesting_user')
    };

    console.log('🚀 Step 1: Inviting user to organization using correct API endpoint');
    console.log('🚀 Invite payload:', JSON.stringify(invitePayload, null, 2));

    // Send the invite request
    const inviteResponse = await fetch(inviteEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${token}`
      },
      body: JSON.stringify(invitePayload)
    });

    console.log('🚀 Invite response status:', inviteResponse.status);

    if (!inviteResponse.ok) {
      const errorData = await inviteResponse.json().catch(() => ({}));
      console.error('🚀 Invite error response:', errorData);

      // Extract error message from various possible formats
      let errorMessage = 'Unknown error occurred';

      if (typeof errorData === 'string') {
        errorMessage = errorData;
      } else if (errorData.detail) {
        // Handle both string and array details
        if (Array.isArray(errorData.detail)) {
          errorMessage = errorData.detail.map((err: any) => {
            if (typeof err === 'string') return err;
            if (err.msg) return `${err.loc ? err.loc.join('.') + ': ' : ''}${err.msg}`;
            if (err.message) return err.message;
            return JSON.stringify(err);
          }).join(', ');
        } else {
          errorMessage = errorData.detail;
        }
      } else if (errorData.message) {
        errorMessage = errorData.message;
      } else if (errorData.error) {
        errorMessage = errorData.error;
      } else if (Array.isArray(errorData) && errorData.length > 0) {
        errorMessage = errorData.map(err => {
          if (typeof err === 'string') return err;
          if (err.msg) return `${err.loc ? err.loc.join('.') + ': ' : ''}${err.msg}`;
          if (err.message) return err.message;
          return JSON.stringify(err);
        }).join(', ');
      } else if (typeof errorData === 'object') {
        errorMessage = JSON.stringify(errorData);
      }

      if (inviteResponse.status === 401) {
        throw new Error('Authentication failed. Please log in again.');
      } else if (inviteResponse.status === 403) {
        throw new Error('Access denied. You do not have permission to invite users.');
      } else if (inviteResponse.status === 400) {
        throw new Error(`Invalid request: ${errorMessage}`);
      } else if (inviteResponse.status === 422) {
        throw new Error(`Validation error: ${errorMessage}`);
      } else {
        throw new Error(`User invitation failed (${inviteResponse.status}): ${errorMessage}`);
      }
    }

    const inviteData = await inviteResponse.json();
    console.log('✅ User invitation sent successfully:', inviteData);

    return {
      invite: inviteData,
      message: 'User invitation sent successfully'
    };
  } catch (error) {
    console.error('Error in invite process:', error);

    // Handle network errors
    if (error instanceof TypeError && error.message.includes('fetch')) {
      throw new Error('Network error. Please check your connection and try again.');
    }

    throw error;
  }
};

// Add User Form Component
const AddUserModal = ({
  isOpen,
  onClose,
  onUserCreated
}: {
  isOpen: boolean;
  onClose: () => void;
  onUserCreated: () => Promise<void>;
}) => {
  const { user } = useAuth();
  const [formData, setFormData] = useState({
    firstName: '',
    lastName: '',
    businessEmail: '',
    businessRole: '',
    contactNumber: '',
    countryCode: '+256',
    status: 'Active',
    accessType: 'permanent',
    fromDate: '',
    toDate: ''
  });

  const [isSubmitting, setIsSubmitting] = useState(false);

  // Check if current user has c_admin role
  const isAdmin = user?.role === 'c_admin';

  // Define allowed roles for c_admin users
  const allowedRoles = [
    { display: 'Stores Manager', value: 'c_stores_manager' },
    { display: 'Accountant', value: 'c_accountant' },
    { display: 'Procurement Officer', value: 'c_procurement_officer' },
    { display: 'Approving Manager', value: 'c_approving_manager' },
    { display: 'Requesting User', value: 'c_requesting_user' }
  ];

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  // Email validation function
  const isValidEmail = (email: string) => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Validate required fields
    if (!formData.businessEmail || !formData.businessRole || !formData.firstName || !formData.lastName) {
      toast.error('Please fill in all required fields (Email, Role, First Name, Last Name)');
      return;
    }

    if (!isValidEmail(formData.businessEmail)) {
      toast.error('Please enter a valid email address');
      return;
    }

    // Show confirmation dialog
    const confirmed = window.confirm(
      `Are you sure you want to create an account and send an invitation to ${formData.businessEmail} (${formData.firstName} ${formData.lastName}) with the role "${formData.businessRole}"?`
    );

    if (!confirmed) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Check if user has permission to create users and send invites
      if (!isAdmin) {
        toast.error('Access denied: Only administrators can create users and send invitations');
        return;
      }

      // Map the business role display name to the API role name
      // Only c_admin can invite these specific roles
      const roleMapping: { [key: string]: string } = {
        'Stores Manager': 'c_stores_manager',
        'Accountant': 'c_accountant',
        'Procurement Officer': 'c_procurement_officer',
        'Approving Manager': 'c_approving_manager',
        'Requesting User': 'c_requesting_user'
      };

      const roleName = roleMapping[formData.businessRole];

      if (!roleName) {
        toast.error('Invalid role selected');
        return;
      }

      // Register user and send invitation
      console.log('🚀 Creating user with data:', {
        email: formData.businessEmail,
        role: roleName,
        firstName: formData.firstName,
        lastName: formData.lastName,
        contactNumber: formData.contactNumber,
        currentUser: user
      });

      const result = await registerAndInviteUser(
        formData.businessEmail,
        roleName,
        formData.firstName,
        formData.lastName,
        formData.contactNumber,
        user // Pass current user data to get organization ID
      );

      console.log('✅ User creation result:', result);
      toast.success(`User invitation sent successfully to ${formData.businessEmail}`);

      // Clear cache and refresh users list to get the real data from backend
      clearUsersCache();

      // Show loading toast while refreshing
      const refreshToast = toast.loading('Refreshing users list...');

      try {
        await onUserCreated();
        toast.dismiss(refreshToast);
        toast.success('Users list updated! Invited user should appear with pending status.');
      } catch (error) {
        toast.dismiss(refreshToast);
        toast.error('Failed to refresh users list. Please refresh manually.');
      }

      // Reset form and close modal
      setFormData({
        firstName: '',
        lastName: '',
        businessEmail: '',
        businessRole: '',
        contactNumber: '',
        countryCode: '+256',
        status: 'Active',
        accessType: 'permanent',
        fromDate: '',
        toDate: ''
      });

      onClose();
    } catch (error: any) {
      console.error('Error in invite process:', error);

      // Better error message handling
      let errorMessage = 'Failed to create user account and send invitation';

      if (error?.message) {
        errorMessage = error.message;
      } else if (typeof error === 'string') {
        errorMessage = error;
      } else if (typeof error === 'object') {
        errorMessage = JSON.stringify(error);
      }

      console.log('Displaying error message:', errorMessage);
      toast.error(errorMessage);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!isOpen) return null;

  // If user is not admin, show access denied message
  if (!isAdmin) {
    return (
      <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md p-6 relative">
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-medium text-gray-900 dark:text-white">Access Denied</h2>
            <button
              onClick={onClose}
              className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
            >
              <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
              </svg>
            </button>
          </div>

          <div className="flex items-center space-x-3 p-4 bg-red-50 dark:bg-red-900/20 rounded-lg">
            <ExclamationTriangleIcon className="h-8 w-8 text-red-500" />
            <div>
              <h3 className="text-sm font-medium text-red-800 dark:text-red-200">
                Insufficient Permissions
              </h3>
              <p className="text-sm text-red-700 dark:text-red-300 mt-1">
                Only administrators (c_admin) can invite users. Your current role is: {user?.role || 'Unknown'}
              </p>
            </div>
          </div>

          <div className="flex justify-end mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
            >
              Close
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg w-full max-w-md p-6 relative">
        <div className="flex justify-between items-center mb-4">
          <div>
            <h2 className="text-xl font-medium text-gray-900 dark:text-white">Create User & Send Invite</h2>
            <p className="text-sm text-gray-600 dark:text-gray-400 mt-1">
              A user account will be created with pending status, then an invitation email will be sent for them to set their password.
            </p>
            <p className="text-xs text-green-600 dark:text-green-400 mt-1">
              ✓ Administrator access confirmed. You can create users with the following roles.
            </p>
          </div>
          <button
            onClick={onClose}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
            </svg>
          </button>
        </div>
        
        <form onSubmit={handleSubmit}>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">First name</label>
              <Input
                type="text"
                name="firstName"
                value={formData.firstName}
                onChange={handleChange}
                className="w-full"
                disabled={isSubmitting}
                required
              />
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Last name</label>
              <Input
                type="text"
                name="lastName"
                value={formData.lastName}
                onChange={handleChange}
                className="w-full"
                disabled={isSubmitting}
                required
              />
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Business email</label>
            <Input
              type="email"
              name="businessEmail"
              value={formData.businessEmail}
              onChange={handleChange}
              className="w-full"
              disabled={isSubmitting}
              required
            />
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Business role</label>
            <select
              name="businessRole"
              value={formData.businessRole}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
              disabled={isSubmitting}
              required
            >
              <option value="" disabled>Select role to invite</option>
              {allowedRoles.map((role) => (
                <option key={role.value} value={role.display}>
                  {role.display}
                </option>
              ))}
            </select>
            <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
              Available roles for c_admin to invite
            </p>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Contact number</label>
            <div className="flex">
              <select
                name="countryCode"
                value={formData.countryCode}
                onChange={handleChange}
                className="p-2 border border-gray-300 dark:border-gray-600 rounded-l-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
              >
                <option value="+256">🇺🇬 +256</option>
                <option value="+1">🇺🇸 +1</option>
                <option value="+44">🇬🇧 +44</option>
                <option value="+254">🇰🇪 +254</option>
              </select>
              <Input
                type="tel"
                name="contactNumber"
                value={formData.contactNumber}
                onChange={handleChange}
                className="w-full rounded-l-none"
                placeholder="(0) XXXXXXXXX"
                required
              />
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">Status</label>
            <select
              name="status"
              value={formData.status}
              onChange={handleChange}
              className="w-full p-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300"
            >
              <option value="Active">Active</option>
              <option value="Deactivated">Deactivated</option>
            </select>
          </div>
          
          <div className="mb-4">
            <div className="flex items-center mb-2">
              <input
                type="radio"
                id="permanent"
                name="accessType"
                value="permanent"
                checked={formData.accessType === 'permanent'}
                onChange={handleChange}
                className="mr-2"
              />
              <label htmlFor="permanent" className="text-sm font-medium text-gray-700 dark:text-gray-300">Permanent access</label>
            </div>
            <div className="flex items-center">
              <input
                type="radio"
                id="temporary"
                name="accessType"
                value="temporary"
                checked={formData.accessType === 'temporary'}
                onChange={handleChange}
                className="mr-2"
              />
              <label htmlFor="temporary" className="text-sm font-medium text-gray-700 dark:text-gray-300">Temporary access</label>
            </div>
          </div>
          
          {formData.accessType === 'temporary' && (
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">From</label>
                <Input
                  type="date"
                  name="fromDate"
                  value={formData.fromDate}
                  onChange={handleChange}
                  className="w-full"
                  required={formData.accessType === 'temporary'}
                />
              </div>
              <div>
                <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">To</label>
                <Input
                  type="date"
                  name="toDate"
                  value={formData.toDate}
                  onChange={handleChange}
                  className="w-full"
                  required={formData.accessType === 'temporary'}
                />
              </div>
            </div>
          )}
          
          <div className="flex justify-end gap-2 mt-6">
            <Button
              type="button"
              variant="outline"
              onClick={onClose}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="bg-[#18546c] hover:bg-[#18546c]/90 text-white"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'Creating User & Sending Invite...' : 'Create User & Send Invite'}
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default function ManageUsersPage() {
  const { user } = useAuth();
  const [searchQuery, setSearchQuery] = useState('');
  const [users, setUsers] = useState<User[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
  const [fetchError, setFetchError] = useState<string | null>(null);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  // Check if current user has c_admin role
  const isAdmin = user?.role === 'c_admin';

  // Fetch users when component mounts
  useEffect(() => {
    const loadUsers = async () => {
      if (!user) {
        console.log('⏳ User not loaded yet, waiting...');
        return;
      }

      try {
        setLoading(true);
        setFetchError(null);
        console.log('🔄 Loading users...');

        const fetchedUsers = await fetchUsers();
        setUsers(fetchedUsers);
        setLastUpdated(new Date());
        console.log('✅ Users loaded successfully:', fetchedUsers.length, 'users');
      } catch (error: any) {
        console.error('❌ Failed to load users:', error);
        setFetchError(error.message || 'Failed to load users');
        toast.error(error.message || 'Failed to load users');
      } finally {
        setLoading(false);
      }
    };

    loadUsers();
  }, [user]); // Re-run when user changes

  // Filter users based on search query
  const filteredUsers = users.filter(user => {
    if (!searchQuery.trim()) return true;

    const query = searchQuery.toLowerCase();
    return (
      user.firstName.toLowerCase().includes(query) ||
      user.lastName.toLowerCase().includes(query) ||
      user.email.toLowerCase().includes(query) ||
      user.role.toLowerCase().includes(query)
    );
  });

  // Function to refresh users list (force refresh cache)
  const refreshUsers = async () => {
    try {
      setLoading(true);
      setFetchError(null);
      const fetchedUsers = await fetchUsers(true); // Force refresh cache
      setUsers(fetchedUsers);
      setLastUpdated(new Date());
      console.log('🔄 Users refreshed successfully');
      toast.success(`Refreshed! Found ${fetchedUsers.length} users in your organization.`);
    } catch (error: any) {
      console.error('❌ Failed to refresh users:', error);
      setFetchError(error.message);
      toast.error('Failed to refresh users list: ' + error.message);
    } finally {
      setLoading(false);
    }
  };

  // Debug function to test API directly
  const testUsersAPI = async () => {
    try {
      const token = localStorage.getItem('access_token');
      console.log('🧪 Testing users API directly...');
      console.log('🔑 Using token:', token ? 'Present' : 'Missing');

      const response = await fetch('/api/proxy/auth/users', {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        }
      });

      console.log('📡 Direct API response status:', response.status);
      const data = await response.json();
      console.log('📋 Direct API response data:', data);

      if (response.ok) {
        toast.success(`Direct API test: Found ${data.length} users`);
      } else {
        toast.error(`Direct API test failed: ${response.status}`);
      }
    } catch (error) {
      console.error('🧪 Direct API test error:', error);
      toast.error('Direct API test failed');
    }
  };

  // Debug function to test different registration endpoints
  const testRegisterEndpoints = async () => {
    const token = localStorage.getItem('access_token');
    const testPayload = {
      email: "<EMAIL>",
      password: "TempPassword123!",
      first_name: "Test",
      last_name: "User",
      telephone_number: "000000000",
      role_names: ["c_requesting_user"],
      organization_id: 1,
      status: "pending"
    };

    const endpoints = [
      '/api/proxy/auth/register',
      '/api/proxy/users/register',
      '/api/proxy/auth/signup'
    ];

    console.log('🧪 Testing registration endpoints...');

    for (const endpoint of endpoints) {
      try {
        console.log(`🔍 Testing ${endpoint}...`);
        const response = await fetch(endpoint, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(testPayload)
        });

        console.log(`📡 ${endpoint} response status:`, response.status);
        const data = await response.text();
        console.log(`📋 ${endpoint} response:`, data);

        if (response.ok) {
          toast.success(`${endpoint} works! Status: ${response.status}`);
          break; // Stop testing if we find a working endpoint
        } else {
          toast.error(`${endpoint} failed: ${response.status}`);
        }
      } catch (error) {
        console.error(`🧪 ${endpoint} test error:`, error);
        toast.error(`${endpoint} test failed`);
      }
    }
  };


  
  return (
    <div className="px-6 py-4">
      <div className="flex items-center mb-4">
        <Link href="/" className="hover:text-blue-600">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="20" 
            height="20" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="2" 
            strokeLinecap="round" 
            strokeLinejoin="round" 
            className="inline-block"
          >
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
        </Link>
        <ChevronRightIcon className="h-4 w-4 mx-2 text-gray-400" />
        <span className="text-gray-500 text-sm">Manage users</span>
      </div>
      
      {/* Page header */}
      <div className="flex justify-between items-center mb-6">
        <div>
          <h1 className="text-2xl font-medium text-gray-900 dark:text-white">Manage users</h1>
          {!isAdmin && (
            <p className="text-sm text-amber-600 dark:text-amber-400 mt-1">
              ⚠️ Limited access: Only administrators can invite new users
            </p>
          )}
        </div>
        <div className="flex space-x-2">
          <Button
            variant="outline"
            className="border-[#18546c] text-[#18546c] hover:bg-[#18546c]/10"
          >
            Save
          </Button>
          {isAdmin ? (
            <Button
              className="bg-[#18546c] hover:bg-[#18546c]/90 text-white"
              onClick={() => setIsAddUserModalOpen(true)}
            >
              + Invite user
            </Button>
          ) : (
            <Button
              className="bg-gray-400 text-white cursor-not-allowed"
              disabled
              title="Only administrators can invite users"
            >
              + Invite user (Admin Only)
            </Button>
          )}
        </div>
      </div>
      
      {/* Role indicator and search */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center space-x-4">
          <div className="relative">
            <MagnifyingGlassIcon className="h-5 w-5 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search user name..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 w-80 border-gray-300 dark:border-gray-600 rounded-md"
            />
          </div>
          <div className="flex items-center space-x-2 px-3 py-1 bg-gray-100 dark:bg-gray-700 rounded-md">
            <span className="text-xs text-gray-600 dark:text-gray-400">Your role:</span>
            <span className={`text-xs font-medium px-2 py-1 rounded ${
              isAdmin
                ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                : 'bg-gray-200 text-gray-700 dark:bg-gray-600 dark:text-gray-300'
            }`}>
              {user?.role || 'Unknown'}
            </span>
          </div>
        </div>
        <div className="flex items-center space-x-2">
          <Button
            variant="outline"
            className="flex items-center gap-2"
            onClick={refreshUsers}
            disabled={loading}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className={loading ? "animate-spin" : ""}
            >
              <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
              <path d="M21 3v5h-5"/>
              <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
              <path d="M3 21v-5h5"/>
            </svg>
            <span>{loading ? 'Refreshing...' : 'Refresh'}</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
            onClick={testUsersAPI}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M9 12l2 2 4-4"/>
              <path d="M21 12c-1 0-3-1-3-3s2-3 3-3 3 1 3 3-2 3-3 3"/>
              <path d="M3 12c1 0 3-1 3-3s-2-3-3-3-3 1-3 3 2 3 3 3"/>
              <path d="M3 12h6m6 0h6"/>
            </svg>
            <span>Test API</span>
          </Button>
          <Button
            variant="outline"
            className="flex items-center gap-2 bg-purple-50 border-purple-200 text-purple-700 hover:bg-purple-100"
            onClick={testRegisterEndpoints}
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"/>
              <circle cx="9" cy="7" r="4"/>
              <path d="m22 2-5 10-5-5 10-5z"/>
            </svg>
            <span>Test Register</span>
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <span>Filter</span>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <polygon points="22 3 2 3 10 12.46 10 19 14 21 14 12.46 22 3"></polygon>
            </svg>
          </Button>
          <Button variant="outline" className="flex items-center gap-2">
            <span>Sort by</span>
            <ChevronDownIcon className="h-4 w-4" />
          </Button>
        </div>
      </div>
      
      {/* Status indicator */}
      <div className="mb-4 flex items-center justify-between">
        <div className="text-sm text-gray-600 dark:text-gray-400">
          Showing {filteredUsers.length} of {users.length} users
          {searchQuery.trim() && (
            <span className="ml-2 text-blue-600 dark:text-blue-400">
              (filtered by &quot;{searchQuery}&quot;)
            </span>
          )}
          <div className="mt-1 text-xs text-gray-500 dark:text-gray-400">
            <span className="inline-flex items-center gap-1">
              <span className="w-2 h-2 bg-yellow-400 rounded-full"></span>
              Pending users have been invited but haven&apos;t completed setup yet
            </span>
          </div>
        </div>
        {lastUpdated && (
          <div className="text-sm text-gray-500 dark:text-gray-400 flex items-center gap-2">
            <svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="12" cy="12" r="10"/>
              <polyline points="12,6 12,12 16,14"/>
            </svg>
            <span>Last updated: {lastUpdated.toLocaleTimeString()}</span>
            {usersCache.data && (
              <span className="text-green-600 dark:text-green-400">• Cached</span>
            )}
          </div>
        )}
      </div>

      {/* Users table */}
      <div className="bg-white dark:bg-gray-800 rounded-md shadow overflow-hidden" style={{ minHeight: '480px' }}>
        <table className="min-w-full divide-y divide-gray-200 dark:divide-gray-700">
          <thead className="bg-gray-50 dark:bg-gray-900">
            <tr>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                No.
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                First name ↑
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Last name ↑
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Business email
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Business role ↑
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Contact number ↑
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Status ↑
              </th>
              <th scope="col" className="px-6 py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Action
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {loading ? (
              <tr>
                <td colSpan={8} className="px-6 py-8 text-center">
                  <div className="flex items-center justify-center">
                    <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-[#18546c]"></div>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">Loading users...</span>
                  </div>
                </td>
              </tr>
            ) : fetchError ? (
              <tr>
                <td colSpan={8} className="px-6 py-8 text-center">
                  <div className="text-red-600 dark:text-red-400">
                    <p>Failed to load users: {fetchError}</p>
                    <button
                      onClick={refreshUsers}
                      className="mt-2 text-sm text-[#18546c] hover:underline"
                    >
                      Try again
                    </button>
                  </div>
                </td>
              </tr>
            ) : filteredUsers.length === 0 ? (
              <tr>
                <td colSpan={8} className="px-6 py-8 text-center text-gray-500 dark:text-gray-400">
                  {searchQuery.trim() ? <>No users found matching &quot;{searchQuery}&quot;</> : 'No users found'}
                </td>
              </tr>
            ) : (
              filteredUsers.map((user, index) => (
              <tr key={user.id} className={index % 2 === 0 ? "bg-white dark:bg-gray-800" : "bg-gray-50 dark:bg-gray-700"}>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {user.id}.
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {user.firstName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {user.lastName}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {user.email}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {user.role}
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  {user.phoneNumber}
                </td>
                <td className="px-6 py-4 whitespace-nowrap">
                  <span
                    className={`px-2 py-1 text-xs rounded-full ${
                      user.status === 'Active'
                        ? 'bg-green-100 text-green-600 dark:bg-green-900 dark:text-green-200'
                        : user.status === 'Pending'
                        ? 'bg-yellow-100 text-yellow-600 dark:bg-yellow-900 dark:text-yellow-200'
                        : 'bg-orange-100 text-orange-600 dark:bg-orange-900 dark:text-orange-200'
                    }`}
                  >
                    {user.status}
                  </span>
                </td>
                <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900 dark:text-white">
                  <button className="text-gray-600 hover:text-gray-900 dark:text-gray-400 dark:hover:text-white">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M6 10a2 2 0 11-4 0 2 2 0 014 0zM12 10a2 2 0 11-4 0 2 2 0 014 0zM16 12a2 2 0 100-4 2 2 0 000 4z" />
                    </svg>
                  </button>
                </td>
              </tr>
              ))
            )}

            {/* Empty placeholder rows to maintain consistent table height (fill to exactly 10 rows total) */}
            {!loading && !fetchError && Array.from({ length: Math.max(0, 10 - filteredUsers.length) }).map((_, index) => (
              <tr
                key={`empty-${index}`}
                className={`h-12 border-b border-gray-200 dark:border-gray-700 ${(index + filteredUsers.length) % 2 === 0 ? "bg-white dark:bg-gray-800" : "bg-gray-50 dark:bg-gray-700"}`
              }>
                <td className="px-6 py-4"></td>
                <td className="px-6 py-4"></td>
                <td className="px-6 py-4"></td>
                <td className="px-6 py-4"></td>
                <td className="px-6 py-4"></td>
                <td className="px-6 py-4"></td>
                <td className="px-6 py-4"></td>
                <td className="px-6 py-4"></td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>
      
      {/* Pagination */}
      <div className="flex items-center justify-between mt-4">
        <div className="text-sm text-gray-700 dark:text-gray-300">
          Showing 1 to 8 of 8 subcounties
        </div>
        <div className="flex items-center space-x-4">
          <span className="text-sm text-gray-700 dark:text-gray-300">Page 1 of 1</span>
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" className="h-8 w-8">
              <ChevronLeftIcon className="h-4 w-4" />
            </Button>
            <Button variant="default" size="icon" className="h-8 w-8 bg-[#18546c]">
              1
            </Button>
            <Button variant="outline" size="icon" className="h-8 w-8">
              <ChevronRightIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <div className="flex items-center space-x-2 text-sm">
          <span className="text-gray-700 dark:text-gray-300">Items per page</span>
          <select className="border border-gray-300 dark:border-gray-600 rounded text-gray-700 dark:text-gray-300 dark:bg-gray-800">
            <option>25</option>
            <option>50</option>
            <option>100</option>
          </select>
        </div>
      </div>
      
      {/* Add User Modal */}
      <AddUserModal
        isOpen={isAddUserModalOpen}
        onClose={() => setIsAddUserModalOpen(false)}
        onUserCreated={refreshUsers}
      />
    </div>
  );
}
