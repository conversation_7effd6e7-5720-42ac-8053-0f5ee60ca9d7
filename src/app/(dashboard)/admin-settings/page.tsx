'use client';

import React from 'react';
import Link from 'next/link';
import { 
  UsersIcon, 
  CogIcon, 
  ShieldCheckIcon, 
  EnvelopeIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  ChevronRightIcon,
  CurrencyDollarIcon,
  UserGroupIcon,
  ClipboardDocumentListIcon
} from '@heroicons/react/24/solid';
import { useAuth } from '@/context/AuthContext';

interface AdminSettingCard {
  title: string;
  description: string;
  icon: React.ReactNode;
  href: string;
  iconBgColor: string;
  iconTextColor: string;
  requiresRole?: string[];
}

export default function AdminSettingsPage() {
  const { user } = useAuth();

  const adminSettings: AdminSettingCard[] = [
    {
      title: 'Manage Users',
      description: 'Add, edit, and manage user accounts and permissions',
      icon: <UsersIcon className="h-6 w-6" />,
      href: '/admin-settings/manage-users',
      iconBgColor: 'bg-blue-100 dark:bg-blue-900/30',
      iconTextColor: 'text-blue-600 dark:text-blue-400',
      requiresRole: ['super_admin', 's_admin', 'c_admin']
    },
    {
      title: 'Budget Approval Limits',
      description: 'Set minimum quotations and budget approval thresholds',
      icon: <CurrencyDollarIcon className="h-6 w-6" />,
      href: '/admin-settings/budget-approval-limits',
      iconBgColor: 'bg-emerald-100 dark:bg-emerald-900/30',
      iconTextColor: 'text-emerald-600 dark:text-emerald-400',
      requiresRole: ['super_admin', 'c_admin']
    },
    {
      title: 'Procurement Groups',
      description: 'Manage procurement groups for projects and departments',
      icon: <UserGroupIcon className="h-6 w-6" />,
      href: '/admin-settings/procurement-groups',
      iconBgColor: 'bg-orange-100 dark:bg-orange-900/30',
      iconTextColor: 'text-orange-600 dark:text-orange-400',
      requiresRole: ['super_admin', 'c_admin']
    },
    {
      title: 'Budget Codes',
      description: 'Create and manage budget codes for financial tracking',
      icon: <ClipboardDocumentListIcon className="h-6 w-6" />,
      href: '/admin-settings/budget-codes',
      iconBgColor: 'bg-teal-100 dark:bg-teal-900/30',
      iconTextColor: 'text-teal-600 dark:text-teal-400',
      requiresRole: ['super_admin', 'c_admin']
    },
    {
      title: 'Organization Settings',
      description: 'Configure organization details and preferences',
      icon: <BuildingOfficeIcon className="h-6 w-6" />,
      href: '/admin-settings/organization',
      iconBgColor: 'bg-green-100 dark:bg-green-900/30',
      iconTextColor: 'text-green-600 dark:text-green-400',
      requiresRole: ['super_admin', 's_admin', 'c_admin']
    },
    {
      title: 'System Configuration',
      description: 'Configure system-wide settings and preferences',
      icon: <CogIcon className="h-6 w-6" />,
      href: '/admin-settings/system',
      iconBgColor: 'bg-purple-100 dark:bg-purple-900/30',
      iconTextColor: 'text-purple-600 dark:text-purple-400',
      requiresRole: ['super_admin']
    },
    {
      title: 'Security & Permissions',
      description: 'Manage security settings and user permissions',
      icon: <ShieldCheckIcon className="h-6 w-6" />,
      href: '/admin-settings/security',
      iconBgColor: 'bg-red-100 dark:bg-red-900/30',
      iconTextColor: 'text-red-600 dark:text-red-400',
      requiresRole: ['super_admin', 's_admin', 'c_admin']
    },
    {
      title: 'Email Templates',
      description: 'Customize email templates and notifications',
      icon: <EnvelopeIcon className="h-6 w-6" />,
      href: '/settings/email-templates',
      iconBgColor: 'bg-yellow-100 dark:bg-yellow-900/30',
      iconTextColor: 'text-yellow-600 dark:text-yellow-400',
      requiresRole: ['super_admin', 's_admin', 'c_admin']
    },
    {
      title: 'Reports & Analytics',
      description: 'Configure reporting settings and analytics',
      icon: <DocumentTextIcon className="h-6 w-6" />,
      href: '/admin-settings/reports',
      iconBgColor: 'bg-indigo-100 dark:bg-indigo-900/30',
      iconTextColor: 'text-indigo-600 dark:text-indigo-400',
      requiresRole: ['super_admin', 's_admin', 'c_admin']
    }
  ];

  // Filter settings based on user role
  const filteredSettings = adminSettings.filter(setting => {
    if (!setting.requiresRole) return true;
    return setting.requiresRole.includes(user?.role || '');
  });

  const getRoleDisplay = (role?: string): string => {
    switch (role) {
      case 's_admin':
        return 'Supplier Admin';
      case 'c_admin':
        return 'Company Admin';
      case 'super_admin':
        return 'Super Admin';
      default:
        return 'User';
    }
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Header */}
      <div className="mb-8">
        <div className="text-sm text-gray-500 mb-2">
          Tools / Admin Settings
        </div>
        <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Admin Settings</h1>
        <p className="text-gray-600 dark:text-gray-400 mt-2">
          Manage system settings, users, and organizational preferences
        </p>
      </div>

      {/* User Info Card */}
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-md p-6 mb-8">
        <div className="flex items-center space-x-4">
          <div className="h-12 w-12 rounded-full bg-[#2a6e78] flex items-center justify-center text-white font-medium text-lg">
            {user?.first_name?.charAt(0) || user?.email?.charAt(0).toUpperCase() || 'U'}
          </div>
          <div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">
              {user?.full_name || `${user?.first_name} ${user?.last_name}` || 'User'}
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              {getRoleDisplay(user?.role)} • {user?.email}
            </p>
          </div>
        </div>
      </div>

      {/* Admin Settings Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {filteredSettings.map((setting, index) => (
          <Link
            key={index}
            href={setting.href}
            className="group bg-white dark:bg-gray-800 rounded-lg shadow-md hover:shadow-lg transition-all duration-200 p-6 border border-gray-200 dark:border-gray-700 hover:border-[#2a6e78] dark:hover:border-[#2a6e78]"
          >
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${setting.iconBgColor} mb-4`}>
                  <span className={setting.iconTextColor}>
                    {setting.icon}
                  </span>
                </div>
                <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-2 group-hover:text-[#2a6e78] transition-colors">
                  {setting.title}
                </h3>
                <p className="text-gray-600 dark:text-gray-400 text-sm leading-relaxed">
                  {setting.description}
                </p>
              </div>
              <ChevronRightIcon className="h-5 w-5 text-gray-400 group-hover:text-[#2a6e78] transition-colors ml-2 flex-shrink-0" />
            </div>
          </Link>
        ))}
      </div>

      {/* Procurement Quick Actions */}
      <div className="mt-12 bg-gray-50 dark:bg-gray-800 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mb-4">Procurement Quick Actions</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
          <Link
            href="/admin-settings/budget-approval-limits"
            className="flex items-center space-x-3 p-3 bg-white dark:bg-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <CurrencyDollarIcon className="h-5 w-5 text-[#2a6e78]" />
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Set Budget Limits</span>
          </Link>
          <Link
            href="/admin-settings/procurement-groups"
            className="flex items-center space-x-3 p-3 bg-white dark:bg-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <UserGroupIcon className="h-5 w-5 text-[#2a6e78]" />
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Manage Groups</span>
          </Link>
          <Link
            href="/admin-settings/budget-codes"
            className="flex items-center space-x-3 p-3 bg-white dark:bg-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <ClipboardDocumentListIcon className="h-5 w-5 text-[#2a6e78]" />
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Budget Codes</span>
          </Link>
          <Link
            href="/admin-settings/manage-users"
            className="flex items-center space-x-3 p-3 bg-white dark:bg-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-600 transition-colors"
          >
            <UsersIcon className="h-5 w-5 text-[#2a6e78]" />
            <span className="text-sm font-medium text-gray-900 dark:text-gray-100">Add New User</span>
          </Link>
        </div>
      </div>

      {/* Help Section */}
      <div className="mt-8 bg-blue-50 dark:bg-blue-900/20 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-blue-900 dark:text-blue-100 mb-2">Need Help?</h3>
        <p className="text-blue-700 dark:text-blue-200 text-sm mb-4">
          If you need assistance with admin settings or have questions about user management, 
          please refer to our documentation or contact support.
        </p>
        <div className="flex space-x-4">
          <Link
            href="/help-centre"
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm font-medium"
          >
            View Documentation
          </Link>
          <Link
            href="/help-centre"
            className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-200 text-sm font-medium"
          >
            Contact Support
          </Link>
        </div>
      </div>
    </div>
  );
}
