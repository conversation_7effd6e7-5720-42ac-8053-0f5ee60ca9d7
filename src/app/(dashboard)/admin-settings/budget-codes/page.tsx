'use client';

import React, { useState } from 'react';
import { Home, FileText, Edit, Plus } from 'lucide-react';
import Link from 'next/link';

type BudgetCode = {
  id: string;
  code: string;
  description: string;
};

export default function BudgetCodesPage() {
  const [budgetCodes, setBudgetCodes] = useState<BudgetCode[]>([
    {
      id: '1',
      code: '211002',
      description: 'Contract staff salaries (including Casuals, Temporary)'
    },
    {
      id: '2',
      code: '221008',
      description: 'Computer supplies and IT services'
    },
    {
      id: '3',
      code: '221009',
      description: 'Welfare and Entertainment'
    }
  ]);

  const [showAddModal, setShowAddModal] = useState(false);
  const [newCode, setNewCode] = useState({
    code: '',
    description: '',
    dateModified: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })
  });

  const handleAddCode = () => {
    if (newCode.code && newCode.description) {
      setBudgetCodes([...budgetCodes, {
        id: Date.now().toString(),
        code: newCode.code,
        description: newCode.description
      }]);
      setNewCode({
        code: '',
        description: '',
        dateModified: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })
      });
      setShowAddModal(false);
    }
  };

  return (
    <div className="h-full overflow-auto">
      {/* Page Header */}
      <div className="p-6 pb-4">
        <div className="flex items-center justify-between">
          <div>
            <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Home className="h-4 w-4" />
              <span>Budget codes</span>
            </nav>
            <h1 className="text-2xl font-semibold text-gray-900">Budget codes</h1>
          </div>
          <button className="bg-gray-100 text-[#18546c] px-4 py-2 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md">
            <span>💾</span>
            <span className="ml-2">Save & publish</span>
          </button>
        </div>
      </div>

      {/* Content */}
      <div className="px-96 pb-6">
        <div className="bg-white rounded-lg border border-gray-200 shadow-sm">
          {/* Card Header */}
          <div className="px-6 py-4 border-b border-gray-200">
            <div className="flex items-center space-x-2">
              <FileText className="h-5 w-5 text-gray-600" />
              <h2 className="text-lg font-medium text-gray-900">Budget codes</h2>
            </div>
          </div>

          {/* Budget Codes List */}
          <div className="p-6">
            {/* Column Headers */}
            <div className="grid grid-cols-12 gap-4 pb-3 mb-4 border-b border-gray-200">
              <div className="col-span-3">
                <span className="text-sm font-medium text-gray-700">Budget code</span>
              </div>
              <div className="col-span-7">
                <span className="text-sm font-medium text-gray-700">Description</span>
              </div>
              <div className="col-span-2"></div>
            </div>

            {/* Budget Code Items */}
            <div className="space-y-3">
              {budgetCodes.map((code) => (
                <div key={code.id} className="grid grid-cols-12 gap-4 items-center py-3 hover:bg-gray-50 rounded-md px-2 -mx-2">
                  <div className="col-span-3">
                    <span className="text-sm text-gray-600 font-mono">{code.code}</span>
                  </div>
                  <div className="col-span-7">
                    <span className="text-sm text-gray-700">{code.description}</span>
                  </div>
                  <div className="col-span-2 flex justify-end">
                    <button className="text-gray-400 hover:text-gray-600 p-1 flex items-center space-x-1">
                      <Edit className="h-4 w-4" />
                      <span>Edit</span>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Footer */}
          <div className="px-6 py-4 border-t border-gray-200 flex items-center justify-between bg-gray-100">
            <button
              onClick={() => setShowAddModal(true)}
              className="flex items-center bg-white space-x-2 text-[#18546c] hover:text-[#164158] text-sm font-medium px-6 py-2 rounded-md"
            >
              <Plus className="h-4 w-4" />
              <span>Add new code</span>
            </button>
            <button className="bg-[#18546c] hover:bg-[#164158] text-white px-6 py-2 rounded-md text-sm font-medium">
              Save
            </button>
          </div>
        </div>
      </div>

      {/* Add Code Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-md">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Add budget code</h3>
              <button
                onClick={() => {
                  setShowAddModal(false);
                  setNewCode({
                    code: '',
                    description: '',
                    dateModified: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })
                  });
                }}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="sr-only">Close</span>
                ✕
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-6 space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Budget code</label>
                <input
                  type="text"
                  value={newCode.code}
                  onChange={(e) => setNewCode({...newCode, code: e.target.value})}
                  className="w-36 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="221011"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Description</label>
                <input
                  type="text"
                  value={newCode.description}
                  onChange={(e) => setNewCode({...newCode, description: e.target.value})}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="Printing, Stationery, Photocopying and Binding"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">Date modified</label>
                <input
                  type="text"
                  value={newCode.dateModified}
                  readOnly
                  className="w-36 px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                />
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 bg-gray-200">
              <button
                onClick={() => {
                  setShowAddModal(false);
                  setNewCode({
                    code: '',
                    description: '',
                    dateModified: new Date().toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' })
                  });
                }}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                onClick={handleAddCode}
                className="px-4 py-2 text-sm font-medium text-white bg-[#18546c] border border-transparent rounded-md hover:bg-[#164158] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#18546c]"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
