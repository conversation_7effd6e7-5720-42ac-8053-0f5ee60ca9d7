'use client';

import React, { useState } from 'react';
import { Home, Search, Filter, ChevronDown, Edit2, Plus } from 'lucide-react';
import { useAuth } from '@/context/AuthContext';

export default function ProcurementGroupsPage() {
  const { user } = useAuth();
  const [searchTerm, setSearchTerm] = useState('');
  const [sortBy, setSortBy] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(17);
  const [showAddModal, setShowAddModal] = useState(false);
  const [formData, setFormData] = useState({
    groupId: '0008',
    projectName: 'Information Technology',
    invoicesBilledTo: 'UNRA Head Office',
    status: 'Deactivated',
    casesContained: 0,
    dateModified: 'Sep 20, 2024',
    createdBy: user?.full_name || 'Current User'
  });

  // Quotation approvals state
  const [quotationApprovals, setQuotationApprovals] = useState({
    isEnabled: false,
    minimumQuotes: 3,
    caseReference: ''
  });

  // Sample data matching your screenshot
  const [procurementGroups, setProcurementGroups] = useState([
    { no: 1, groupId: '0001', name: 'Administration', casesContained: 66, invoicesBilledTo: 'UNRA Head Office', dateModified: 'Feb 12, 2024', status: 'Active' },
    { no: 2, groupId: '0002', name: 'Mechanical', casesContained: 47, invoicesBilledTo: 'UNRA Gulu Office', dateModified: 'Feb 10, 2024', status: 'Active' },
    { no: 3, groupId: '0003', name: 'Civil Works', casesContained: 31, invoicesBilledTo: 'UNRA Head Office', dateModified: 'Mar 14, 2024', status: 'Active' },
    { no: 4, groupId: '0004', name: 'Legal & Audit', casesContained: 29, invoicesBilledTo: 'UNRA Head Office', dateModified: 'Apr 08, 2024', status: 'Active' },
    { no: 5, groupId: '0005', name: 'Design Office', casesContained: 18, invoicesBilledTo: 'UNRA Head Office', dateModified: 'Apr 09, 2024', status: 'Active' },
    { no: 6, groupId: '0006', name: 'Production', casesContained: 12, invoicesBilledTo: 'UNRA Head Office', dateModified: 'Apr 09, 2024', status: 'Deactivated' },
    { no: 7, groupId: '0007', name: 'Ferry Services', casesContained: 23, invoicesBilledTo: 'UNRA Jinja Office', dateModified: 'Apr 15, 2024', status: 'Active' },
  ]);

  const handleAddGroup = () => {
    if (formData.groupId && formData.projectName) {
      const newGroup = {
        no: procurementGroups.length + 1,
        groupId: formData.groupId,
        name: formData.projectName,
        casesContained: formData.casesContained,
        invoicesBilledTo: formData.invoicesBilledTo,
        dateModified: formData.dateModified,
        status: formData.status
      };

      setProcurementGroups([...procurementGroups, newGroup]);
      setFormData({
        groupId: '0008',
        projectName: 'Information Technology',
        invoicesBilledTo: 'UNRA Head Office',
        status: 'Deactivated',
        casesContained: 0,
        dateModified: 'Sep 20, 2024',
        createdBy: user?.full_name || 'Current User'
      });
      setShowAddModal(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      groupId: '0008',
      projectName: 'Information Technology',
      invoicesBilledTo: 'UNRA Head Office',
      status: 'Deactivated',
      casesContained: 0,
      dateModified: 'Sep 20, 2024',
      createdBy: user?.full_name || 'Current User'
    });
    setShowAddModal(false);
  };

  return (
    <div className="h-full overflow-auto">
      {/* Page Header */}
      <div className="w-full">
        <div className="flex items-center justify-between px-6 py-4">
          <div>
            <nav className="flex items-center space-x-2 text-sm text-gray-500 mb-2">
              <Home className="h-4 w-4" />
              <span>Procurement groups</span>
            </nav>
            <h1 className="text-xl font-medium text-gray-900">Procurement groups</h1>
          </div>
          <div className="flex items-center space-x-3">
            <button className="bg-gray-100 text-[#18546c] px-4 py-2 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md">
              <span className="ml-2">Save & publish</span>
            </button>
            <button className="bg-gray-100 text-[#18546c] px-4 py-2 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md">
              Save
            </button>
            <button
              onClick={() => setShowAddModal(true)}
              className="bg-gray-100 text-[#18546c] px-4 py-2 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md"
            >
              <Plus className="h-4 w-4" />
              <span>Add group</span>
            </button>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="px-16 py-6 space-y-6">
        {/* Search and Filter Bar */}
        <div className="flex items-center justify-between">
          <div className="relative flex-1 max-w-md">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <input
              type="text"
              placeholder="Search..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg text-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            />
          </div>
          <div className="flex items-center space-x-3">
            <button className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 text-sm">
              <Filter className="h-4 w-4" />
              <span>Filter</span>
            </button>
            <button className="flex items-center space-x-2 text-gray-600 hover:text-gray-800 text-sm">
              <span>Sort by</span>
              <ChevronDown className="h-4 w-4" />
            </button>
          </div>
        </div>

        {/* Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">No.</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Group ID <ChevronDown className="inline h-3 w-3 ml-1" />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Project/Department/Office name <ChevronDown className="inline h-3 w-3 ml-1" />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Cases contained</th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Invoices billed to <ChevronDown className="inline h-3 w-3 ml-1" />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Date modified <ChevronDown className="inline h-3 w-3 ml-1" />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status <ChevronDown className="inline h-3 w-3 ml-1" />
                  </th>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {procurementGroups.map((group, index) => (
                  <tr key={group.no} className={`hover:bg-gray-100 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{group.no}.</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{group.groupId}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{group.name}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{group.casesContained}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">{group.invoicesBilledTo}</td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-500">{group.dateModified}</td>
                    <td className="px-6 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        group.status === 'Active'
                          ? 'bg-green-100 text-green-800'
                          : 'bg-red-100 text-red-800'
                      }`}>
                        {group.status}
                      </span>
                    </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm">
                      <button className="text-gray-400 hover:text-gray-600">
                        <Edit2 className="h-4 w-4" />
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Pagination */}
        <div className="flex items-center justify-between">
          <div className="text-sm text-gray-500">
            Showing 1 to 7 of 7
          </div>
          <div className="flex items-center space-x-2">
            <button className="p-2 text-gray-400 hover:text-gray-600">
              <span>‹</span>
            </button>
            <button className="px-3 py-1 bg-blue-600 text-white rounded text-sm">
              1
            </button>
            <button className="p-2 text-gray-400 hover:text-gray-600">
              <span>›</span>
            </button>
          </div>
          <div className="flex items-center space-x-2">
            <span className="text-sm text-gray-500">Show</span>
            <select
              value={rowsPerPage}
              onChange={(e) => setRowsPerPage(parseInt(e.target.value))}
              className="border border-gray-300 rounded px-2 py-1 text-sm"
            >
              <option value={17}>17 rows</option>
              <option value={25}>25 rows</option>
              <option value={50}>50 rows</option>
            </select>
          </div>
        </div>
      </div>

      {/* Quotation Approvals Section */}
      <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mt-6">
        <div className="flex items-center space-x-2 mb-6">
          <div className="w-5 h-5 rounded-full border-2 border-gray-300 flex items-center justify-center">
            <div className="w-2 h-2 bg-gray-400 rounded-full"></div>
          </div>
          <h2 className="text-lg font-medium text-gray-900">Quotation approvals</h2>
        </div>

        <div className={`space-y-6 ${!quotationApprovals.isEnabled ? 'opacity-50 pointer-events-none' : ''}`}>
          {/* Minimum number of quotes */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Minimum number of quotes required for approval
            </label>
            <select
              value={quotationApprovals.minimumQuotes}
              onChange={(e) => setQuotationApprovals({
                ...quotationApprovals,
                minimumQuotes: parseInt(e.target.value)
              })}
              className="w-48 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent bg-white"
              disabled={!quotationApprovals.isEnabled}
            >
              <option value={1}>1</option>
              <option value={2}>2</option>
              <option value={3}>3</option>
              <option value={4}>4</option>
              <option value={5}>5</option>
            </select>
          </div>

          {/* Apply to case reference */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Apply to case reference:
            </label>
            <input
              type="text"
              value={quotationApprovals.caseReference}
              onChange={(e) => setQuotationApprovals({
                ...quotationApprovals,
                caseReference: e.target.value
              })}
              className="w-full max-w-md px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              placeholder="Enter case reference number"
              disabled={!quotationApprovals.isEnabled}
            />
          </div>

          {/* Action buttons */}
          <div className="flex items-center space-x-3 pt-4">
            <button
              onClick={() => setQuotationApprovals({
                ...quotationApprovals,
                caseReference: ''
              })}
              className="px-6 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              disabled={!quotationApprovals.isEnabled}
            >
              Override
            </button>
            <button
              onClick={() => {
                // Handle apply once logic here
                console.log('Apply once clicked', quotationApprovals);
              }}
              className="px-6 py-2 text-sm font-medium text-white bg-[#18546c] border border-transparent rounded-md hover:bg-[#164158] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#18546c]"
              disabled={!quotationApprovals.isEnabled}
            >
              Apply once
            </button>
          </div>
        </div>

        {/* Enable/Disable toggle (for testing - you can remove this) */}
        <div className="mt-6 pt-4 border-t border-gray-200">
          <label className="flex items-center space-x-2">
            <input
              type="checkbox"
              checked={quotationApprovals.isEnabled}
              onChange={(e) => setQuotationApprovals({
                ...quotationApprovals,
                isEnabled: e.target.checked
              })}
              className="rounded border-gray-300 text-[#18546c] focus:ring-[#18546c]"
            />
            <span className="text-sm text-gray-600">Enable quotation approvals (for testing)</span>
          </label>
        </div>
      </div>

      {/* Add Group Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg shadow-xl w-full max-w-2xl">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Add procurement group</h3>
              <button
                onClick={handleCancel}
                className="text-gray-400 hover:text-gray-600"
              >
                <span className="sr-only">Close</span>
                ✕
              </button>
            </div>

            {/* Modal Body */}
            <div className="p-6 space-y-4">
              {/* Group ID and Project Name Row */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Group ID
                  </label>
                  <input
                    type="text"
                    value={formData.groupId}
                    onChange={(e) => setFormData({ ...formData, groupId: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="0008"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Project/Department/Office name
                  </label>
                  <input
                    type="text"
                    value={formData.projectName}
                    onChange={(e) => setFormData({ ...formData, projectName: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="Information Technology"
                  />
                </div>
              </div>

              {/* Invoices billed to */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Invoices billed to
                </label>
                <input
                  type="text"
                  value={formData.invoicesBilledTo}
                  onChange={(e) => setFormData({ ...formData, invoicesBilledTo: e.target.value })}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  placeholder="UNRA Head Office"
                />
              </div>

              {/* Status and Cases contained row */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Status
                  </label>
                  <select
                    value={formData.status}
                    onChange={(e) => setFormData({ ...formData, status: e.target.value })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                  >
                    <option value="Active">Active</option>
                    <option value="Deactivated">Deactivated</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Cases contained
                  </label>
                  <input
                    type="number"
                    value={formData.casesContained}
                    onChange={(e) => setFormData({ ...formData, casesContained: parseInt(e.target.value) || 0 })}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    placeholder="00"
                  />
                </div>
              </div>

              {/* Date modified and Created by row */}
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Date modified
                  </label>
                  <input
                    type="text"
                    value={formData.dateModified}
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Created by
                  </label>
                  <input
                    type="text"
                    value={formData.createdBy}
                    readOnly
                    className="w-full px-3 py-2 border border-gray-300 rounded-md bg-gray-50 text-gray-500"
                  />
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200">
              <button
                onClick={handleCancel}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
              >
                Cancel
              </button>
              <button
                onClick={handleAddGroup}
                className="px-4 py-2 text-sm font-medium text-white bg-[#18546c] border border-transparent rounded-md hover:bg-[#164158] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#18546c]"
              >
                Add group
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
