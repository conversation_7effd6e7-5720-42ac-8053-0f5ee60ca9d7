'use client';

import React, { useState } from 'react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Table, TableHeader, TableRow, TableHead, TableBody, TableCell } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { FiSearch, FiPlus, FiEdit, FiTrash2, FiEye } from 'react-icons/fi';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import TemplateEditor from '@/components/EmailTemplates/TemplateEditor';
import InvitationTemplate from '@/components/EmailTemplates/InvitationTemplate';

// Mock data for email templates
const mockTemplates = [
  {
    id: '1',
    name: 'Supplier Invitation',
    subject: 'Invitation to join Ascension Supplier Network',
    lastModified: 'Jul 1, 2025',
    status: 'Active',
    type: 'Invitation',
  },
  {
    id: '2',
    name: 'Account Verification',
    subject: 'Verify your Ascension account',
    lastModified: 'Jun 28, 2025',
    status: 'Active',
    type: 'Verification',
  },
  {
    id: '3',
    name: 'Password Reset',
    subject: 'Reset your Ascension password',
    lastModified: 'Jun 25, 2025',
    status: 'Active',
    type: 'Account',
  },
  {
    id: '4',
    name: 'Requisition Approval',
    subject: 'Requisition awaiting your approval',
    lastModified: 'Jun 20, 2025',
    status: 'Active',
    type: 'Notification',
  },
  {
    id: '5',
    name: 'Welcome Email',
    subject: 'Welcome to Ascension',
    lastModified: 'Jun 15, 2025',
    status: 'Draft',
    type: 'Onboarding',
  },
];

export default function EmailTemplatesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [isEditorOpen, setIsEditorOpen] = useState(false);
  const [isPreviewOpen, setIsPreviewOpen] = useState(false);
  const [currentTemplate, setCurrentTemplate] = useState<any>(null);
  
  // Filter templates based on search term
  const filteredTemplates = mockTemplates.filter(template => {
    return (
      template.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
      template.type.toLowerCase().includes(searchTerm.toLowerCase())
    );
  });
  
  const handleEditTemplate = (template: any) => {
    setCurrentTemplate(template);
    setIsEditorOpen(true);
  };
  
  const handlePreviewTemplate = (template: any) => {
    setCurrentTemplate(template);
    setIsPreviewOpen(true);
  };
  
  const handleSaveTemplate = (updatedTemplate: any) => {
    // Here you would typically save the template to your backend
    console.log('Saving template:', updatedTemplate);
    setIsEditorOpen(false);
    // For now, we'll just show a success message
    alert('Template saved successfully!');
  };
  
  return (
    <div className="space-y-6 p-6">
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold">Email Templates</h1>
          <p className="text-gray-500 dark:text-gray-400">Manage and customize email templates</p>
        </div>
        <Button
          onClick={() => {}} // Add create template functionality
          className="bg-[#18546c] hover:bg-[#1a6985] flex items-center gap-2"
        >
          <FiPlus className="h-4 w-4" />
          Create Template
        </Button>
      </div>
      
      <Card className="bg-white dark:bg-slate-800 shadow-sm">
        <CardContent className="p-6">
          <div className="flex flex-col md:flex-row gap-4 mb-6">
            <div className="relative flex-1">
              <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
              <Input
                placeholder="Search templates..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>
          </div>
          
          <div className="rounded-md border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Name</TableHead>
                  <TableHead>Subject</TableHead>
                  <TableHead>Type</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Last Modified</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTemplates.length > 0 ? (
                  filteredTemplates.map((template) => (
                    <TableRow key={template.id}>
                      <TableCell className="font-medium">{template.name}</TableCell>
                      <TableCell>{template.subject}</TableCell>
                      <TableCell>{template.type}</TableCell>
                      <TableCell>
                        <Badge className={template.status === 'Active' ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300' : 'bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-300'}>
                          {template.status}
                        </Badge>
                      </TableCell>
                      <TableCell>{template.lastModified}</TableCell>
                      <TableCell>
                        <div className="flex gap-2">
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handlePreviewTemplate(template)}
                            className="h-8 w-8 p-0"
                          >
                            <FiEye className="h-4 w-4" />
                            <span className="sr-only">Preview</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleEditTemplate(template)}
                            className="h-8 w-8 p-0"
                          >
                            <FiEdit className="h-4 w-4" />
                            <span className="sr-only">Edit</span>
                          </Button>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => {}} // Add delete functionality
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-600"
                          >
                            <FiTrash2 className="h-4 w-4" />
                            <span className="sr-only">Delete</span>
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))
                ) : (
                  <TableRow>
                    <TableCell colSpan={6} className="text-center py-6 text-gray-500">
                      No templates found matching your criteria
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          </div>
        </CardContent>
      </Card>

      {/* Template Editor Dialog */}
      <Dialog open={isEditorOpen} onOpenChange={setIsEditorOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Edit Template</DialogTitle>
          </DialogHeader>
          {currentTemplate && (
            <TemplateEditor
              initialTemplate={{
                name: currentTemplate.name,
                subject: currentTemplate.subject,
                recipientName: '[Recipient Name]',
                companyName: '[Company Name]',
                invitationLink: 'https://ascension.com/invite/sample-token',
                expiryDays: '30 days',
                senderName: 'Ascension Team',
                senderTitle: 'Procurement Manager'
              }}
              onSave={handleSaveTemplate}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Template Preview Dialog */}
      <Dialog open={isPreviewOpen} onOpenChange={setIsPreviewOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>Preview Template</DialogTitle>
          </DialogHeader>
          <div className="border rounded-md p-4 bg-white overflow-auto max-h-[70vh]">
            {currentTemplate && currentTemplate.type === 'Invitation' && (
              <InvitationTemplate
                recipientName="[Recipient Name]"
                companyName="[Company Name]"
                invitationLink="https://ascension.com/invite/sample-token"
                expiryDate="30 days"
                senderName="Ascension Team"
                senderTitle="Procurement Manager"
              />
            )}
          </div>
          <div className="flex justify-end mt-4">
            <Button onClick={() => setIsPreviewOpen(false)} variant="outline">
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}
