'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { 
  FiInbox, 
  FiStar, 
  FiFileText, 
  FiTrash2, 
  FiSearch, 
  FiChevronDown, 
  FiPaperclip, 
  FiRefreshCw,
  FiMoreVertical,
  FiMail,
  FiDownload,
  FiPrinter,
  FiPlus,
  FiFilter,
  FiX,
  FiChevronRight,
  FiChevronLeft,
  FiMessageSquare,
  FiMenu,
  FiEye
} from 'react-icons/fi';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/InquiryCard';
import { LoadingSpinner } from '@/components/ui/loading-spinner';
import { useAuth } from '@/context/AuthContext';
import { useMessages } from '@/context/MessageContext';
import { NewMessageDialog } from '@/components/messages/NewMessageDialog';

interface Message {
  id: string;
  caseReference: string;
  from: string;
  subject: string;
  preview: string;
  receivedAt: string;
  read: boolean;
  starred: boolean;
  hasAttachment: boolean;
  priority?: 'high' | 'normal' | 'low';
  labels?: string[];
}

type MessageStatus = 'all' | 'unread' | 'starred' | 'archived';

const mockMessages: Message[] = [
  {
    id: '1',
    caseReference: 'UNICEF/0002/24/062',
    from: 'Gloria Adimwe',
    subject: "Hi Denis, There's currently no budget for this order. Recommend you cancel the order if not very urgent.",
    preview: "Hi Denis, There's currently no budget for this order. Recommend you cancel the order if not very urgent.",
    receivedAt: '2023-06-28T11:52:00',
    read: false,
    starred: false,
    hasAttachment: false,
    priority: 'high',
  },
  {
    id: '2',
    caseReference: 'UNICEF/0001/24/009',
    from: 'Nagwe Auto Spares Ltd',
    subject: 'Dear sir, We will not be able to deliver as previously advised due to unavailability of field vehicles. Expect the delivery on Monday.',
    preview: 'Dear sir, We will not be able to deliver as previously advised due to unavailability of field vehicles. Expect the delivery on Monday.',
    receivedAt: '2023-06-10T14:15:00',
    read: true,
    starred: false,
    hasAttachment: false,
  },
  {
    id: '3',
    caseReference: 'UNICEF/0004/24/175',
    from: 'Chelsea Jjuuko',
    subject: 'Hi Denis, Requesting you to expedite approval of this the subject inquiry. We are aiming to start maintenance of the generators.',
    preview: 'Hi Denis, Requesting you to expedite approval of this the subject inquiry. We are aiming to start maintenance of the generators.',
    receivedAt: '2023-04-28T09:45:00',
    read: true,
    starred: false,
    hasAttachment: true,
  },
  {
    id: '4',
    caseReference: 'UNICEF/0004/24/165',
    from: 'Brian Gimei',
    subject: 'Hi Denis, There are no other suitable suppliers who can provide us quotations. Suggest you override the minimum number of quotations.',
    preview: 'Hi Denis, There are no other suitable suppliers who can provide us quotations. Suggest you override the minimum number of quotations.',
    receivedAt: '2023-04-13T10:30:00',
    read: true,
    starred: false,
    hasAttachment: false,
  },
  {
    id: '5',
    caseReference: 'UNICEF/0002/24/173',
    from: 'Ascension',
    subject: 'Hi Denis, You have received a new invoice from Nagwe Auto Spares Ltd. Purchase order details below:',
    preview: 'Hi Denis, You have received a new invoice from Nagwe Auto Spares Ltd. Purchase order details below:',
    receivedAt: '2023-04-10T15:20:00',
    read: true,
    starred: false,
    hasAttachment: false,
  },
  {
    id: '6',
    caseReference: 'UNICEF/0004/24/161',
    from: 'Ascension',
    subject: 'Hi Denis, You have received a new invoice from Okinawa Stationers Ltd. Purchase order details below:',
    preview: 'Hi Denis, You have received a new invoice from Okinawa Stationers Ltd. Purchase order details below:',
    receivedAt: '2023-04-10T14:45:00',
    read: true,
    starred: false,
    hasAttachment: false,
  },
  {
    id: '7',
    caseReference: 'UNICEF/0002/24/062',
    from: 'Ascension',
    subject: 'Hi Denis, You have received a new invoice from Okinawa Stationers Ltd. Purchase order details below:',
    preview: 'Hi Denis, You have received a new invoice from Okinawa Stationers Ltd. Purchase order details below:',
    receivedAt: '2023-04-09T09:15:00',
    read: true,
    starred: false,
    hasAttachment: false,
  },
];

export default function MessagesPage() {
  const router = useRouter();
  const { user } = useAuth();
  const { 
    messages, 
    currentFolder, 
    selectedMessages, 
    folderCounts, 
    loading, 
    error,
    currentPage,
    totalPages,
    totalMessages,
    fetchMessages,
    selectMessage,
    selectAllMessages,
    clearSelection,
    deleteMessage,
    deleteSelectedMessages,
    moveMessage,
    markAsRead,
    starMessage
  } = useMessages();
  
  const [searchQuery, setSearchQuery] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [showNewMessageDialog, setShowNewMessageDialog] = useState(false);

  useEffect(() => {
    if (user) {
      fetchMessages();
    } else {
      router.push('/login');
    }
  }, [user, router, fetchMessages]);

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    router.push('/login');
    return null;
  }

  const filteredMessages = messages.filter(message => {
    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      return (
        message.from.toLowerCase().includes(query) ||
        message.subject.toLowerCase().includes(query) ||
        message.preview.toLowerCase().includes(query)
      );
    }
    
    return true;
  });

  const toggleSelectAll = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.checked) {
      selectAllMessages();
    } else {
      clearSelection();
    }
  };

  const handleMessageClick = (messageId: string) => {
    markAsRead(messageId);
  };

  const handleStarMessage = (messageId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    starMessage(messageId);
  };

  // Format date to relative time (e.g., '2 hours ago')
  const formatRelativeTime = (dateString: string) => {
    const date = new Date(dateString);
    const now = new Date();
    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);
    
    // If same day, show time
    if (date.toDateString() === now.toDateString()) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    }
    
    // If yesterday, show 'Yesterday'
    const yesterday = new Date(now);
    yesterday.setDate(yesterday.getDate() - 1);
    if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    }
    
    // Otherwise show date in short format
    return date.toLocaleDateString([], { day: 'numeric', month: 'short' });
  };
  
  // Format date to local date string
  const formatDate = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleDateString([], { 
      year: 'numeric', 
      month: 'short', 
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  return (
    <div className="flex flex-col h-full bg-gray-50 dark:bg-gray-900 transition-colors duration-200">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 px-6 py-4 shadow-sm">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <div className="flex items-center">
              <FiMessageSquare className="h-6 w-6 text-blue-600 dark:text-blue-400 mr-3" />
              <h1 className="text-xl font-semibold text-gray-900 dark:text-white">Messages</h1>
              <span className="ml-3 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100">
                {messages.length} total
              </span>
            </div>
          </div>
          
          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              className="hidden sm:flex items-center space-x-1.5 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <FiDownload className="h-4 w-4" />
              <span>Export</span>
            </Button>
            <Button
              variant="outline"
              size="sm"
              className="hidden sm:flex items-center space-x-1.5 border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700"
            >
              <FiPrinter className="h-4 w-4" />
              <span>Print</span>
            </Button>
            <Button 
              className="bg-gray-100 text-[#18546c] hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 shadow hover:shadow-md text-sm flex items-center"
              onClick={() => setShowNewMessageDialog(true)}
            >
              <span className="mr-1.5 text-base">+</span>
              <span>New Message</span>
            </Button>
          </div>
        </div>
        
        {/* Folder Tabs */}
        <div className="mt-4 flex flex-wrap gap-2">
          {[
            { id: 'inbox', label: 'Inbox', count: folderCounts.unread },
            { id: 'sent', label: 'Sent', count: folderCounts.sent },
            { id: 'drafts', label: 'Drafts', count: folderCounts.drafts },
            { id: 'trash', label: 'Trash', count: folderCounts.trash }
          ].map(tab => (
            <button
              key={tab.id}
              onClick={() => fetchMessages(tab.id)}
              className={`px-3 py-1.5 text-sm font-medium rounded-md transition-colors ${
                currentFolder === tab.id
                  ? 'bg-blue-50 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700/50'
              }`}
            >
              {tab.label}
              {tab.count !== undefined && tab.count > 0 && (
                <span className="ml-1.5 inline-flex items-center justify-center h-4 w-4 rounded-full bg-blue-600 text-[10px] font-medium text-white">
                  {tab.count}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>
      
      {/* Mobile tabs */}
      <div className="mt-4 md:hidden overflow-x-auto">
        <div className="flex space-x-2 pb-1">
          {['inbox', 'sent', 'drafts', 'trash'].map((folder) => (
            <button
              key={folder}
              onClick={() => fetchMessages(folder)}
              className={`px-3 py-1.5 text-sm font-medium rounded-md whitespace-nowrap ${
                currentFolder === folder
                  ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                  : 'text-gray-600 dark:text-gray-400 hover:bg-gray-100 dark:hover:bg-gray-700/50'
              }`}
            >
              {folder.charAt(0).toUpperCase() + folder.slice(1)}
              {folder === 'inbox' && folderCounts.unread > 0 && (
                <span className="ml-1.5 inline-flex items-center justify-center h-4 w-4 rounded-full bg-blue-600 text-[10px] font-medium text-white">
                  {folderCounts.unread}
                </span>
              )}
            </button>
          ))}
        </div>
      </div>
      
      {/* Toolbar */}
    <div className="bg-white border-b border-gray-200 px-4 sm:px-6 py-3">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3">
        <div className="flex items-center space-x-2">
          <input
            type="checkbox"
            className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            checked={selectedMessages.length > 0 && 
                    selectedMessages.length === filteredMessages.length}
            onChange={toggleSelectAll}
          />
          
          <div className="relative flex-1 sm:w-64">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiSearch className="h-4 w-4 text-gray-400" />
            </div>
            <input
              type="text"
              className="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md text-sm placeholder-gray-500 focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500"
              placeholder="Search messages..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
            />
          </div>

          <Button 
            variant="ghost" 
            size="sm"
            className={`hidden sm:flex items-center ${
              showFilters ? 'bg-blue-50 text-blue-700' : ''
            }`}
            onClick={() => setShowFilters(!showFilters)}
          >
            <FiFilter className="h-4 w-4 mr-1.5" />
            <span>Filters</span>
          </Button>
        </div>

        <div className="flex items-center justify-between sm:justify-end space-x-2">
          <Button variant="ghost" size="icon" className="sm:hidden">
            <FiFilter className="h-4 w-4" />
          </Button>
          <Button variant="ghost" size="icon">
            <FiRefreshCw className="h-4 w-4" />
          </Button>
          <div className="relative">
            <Button variant="ghost" size="icon">
              <FiMoreVertical className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Filter Panel */}
      {showFilters && (
        <div className="mt-3 p-4 bg-gray-50 rounded-md border border-gray-200">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">Priority</h4>
              <div className="space-y-2">
                {['high', 'normal', 'low'].map(priority => (
                  <label key={priority} className="flex items-center">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700 capitalize">{priority}</span>
                  </label>
                ))}
              </div>
            </div>
            <div>
              <h4 className="text-xs font-medium text-gray-500 uppercase tracking-wider mb-2">Labels</h4>
              <div className="space-y-2">
                {['work', 'personal', 'important'].map(label => (
                  <label key={label} className="flex items-center">
                    <input
                      type="checkbox"
                      className="h-4 w-4 text-blue-600 border-gray-300 rounded"
                    />
                    <span className="ml-2 text-sm text-gray-700 capitalize">{label}</span>
                  </label>
                ))}
              </div>
            </div>
            <div className="flex items-end">
              <button className="px-3 py-1.5 bg-white border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50">
                Clear all filters
              </button>
            </div>
          </div>
        </div>
      )}
    </div>

    {/* Message List */}
    <div className="flex-1 overflow-y-auto bg-white dark:bg-gray-900">
      {/* Empty state */}
      {filteredMessages.length === 0 && !loading && (
        <div className="flex flex-col items-center justify-center h-full py-16 px-4 text-center">
          <div className="mx-auto flex h-12 w-12 items-center justify-center rounded-full bg-blue-100 dark:bg-blue-900/30">
            <FiInbox className="h-6 w-6 text-blue-600 dark:text-blue-400" />
          </div>
          <h3 className="mt-4 text-lg font-medium text-gray-900 dark:text-white">No messages found</h3>
          <p className="mt-1 text-sm text-gray-500 dark:text-gray-400 max-w-md">
            {searchQuery || currentFolder !== 'inbox'
              ? 'No messages match your current filters. Try adjusting your search or filter criteria.'
              : 'Your inbox is empty. Get started by sending a new message.'}
          </p>
          <div className="mt-6">
            <Button
              onClick={() => {
                setSearchQuery('');
                fetchMessages('inbox');
                setShowFilters(false);
              }}
              variant={searchQuery || currentFolder !== 'inbox' ? 'outline' : 'default'}
              className="mr-3"
            >
              <FiInbox className="mr-2 h-4 w-4" />
              View all messages
            </Button>
            <Button
              onClick={() => setShowNewMessageDialog(true)}
            >
              <FiPlus className="mr-2 h-4 w-4" />
              New message
            </Button>
          </div>
        </div>
      )}
      {filteredMessages.length === 0 ? (
        <div className="flex flex-col items-center justify-center h-64 p-6 text-center">
          <FiInbox className="h-12 w-12 text-gray-300 mb-3" />
          <h3 className="text-sm font-medium text-gray-900 mb-1">No messages found</h3>
          <p className="text-sm text-gray-500 max-w-md">
            {searchQuery || showFilters 
              ? 'Try adjusting your search or filter criteria' 
              : 'You have no messages in this folder'}
          </p>
          {searchQuery || showFilters ? (
            <Button 
              variant="outline" 
              className="mt-3"
              onClick={() => {
                setSearchQuery('');
                setShowFilters(false);
                fetchMessages('inbox');
              }}
            >
              Clear all filters
            </Button>
          ) : (
            <Button 
              className="mt-3"
              onClick={() => setShowNewMessageDialog(true)}
            >
              <FiPlus className="mr-2 h-4 w-4" />
              New Message
            </Button>
          )}
        </div>
      ) : (
        <div className="divide-y divide-gray-200 dark:divide-gray-700">
          <div className="hidden sm:block">
            <div className="grid grid-cols-12 gap-4 px-4 py-3 bg-gray-50 dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700 text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
              <div className="col-span-1 flex items-center">
                <input
                  type="checkbox"
                  className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                  checked={selectedMessages.length === filteredMessages.length && filteredMessages.length > 0}
                  onChange={toggleSelectAll}
                />
              </div>
              <div className="col-span-2">Case #</div>
              <div className="col-span-4">From</div>
              <div className="col-span-4">Message</div>
              <div className="col-span-1 text-right">
                Received
              </div>
              <div className="col-span-1"></div>
            </div>
          </div>
          {filteredMessages.map(message => (
            <div 
              key={message.id} 
              className={`border-0 rounded-none cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800/50 ${
                !message.isRead ? 'bg-blue-50 dark:bg-blue-900/30' : 'bg-white dark:bg-gray-900'
              } transition-colors duration-150`}
              onClick={() => handleMessageClick(message.id)}
            >
              <div className="grid grid-cols-12 gap-4 p-3 sm:p-4">
                <div className="col-span-1 flex items-center">
                  <input
                    type="checkbox"
                    className="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500 dark:border-gray-600 dark:bg-gray-700 dark:ring-offset-gray-800"
                    checked={selectedMessages.includes(message.id)}
                    onChange={() => selectMessage(message.id)}
                    onClick={(e) => e.stopPropagation()}
                  />
                </div>
                <div className="col-span-2">
                  <span className="font-mono text-sm text-blue-600 dark:text-blue-400 font-medium">
                    {message.caseReference}
                  </span>
                </div>
                <div className="col-span-4">
                  <div className="flex items-center">
                    <div className={`text-sm truncate ${
                      !message.isRead 
                        ? 'font-semibold text-gray-900 dark:text-white' 
                        : 'text-gray-700 dark:text-gray-300'
                    }`}>
                      {message.from}
                    </div>
                  </div>
                </div>
                <div className="col-span-3">
                  <div className={`text-sm truncate ${
                    !message.isRead 
                      ? 'font-medium text-gray-900 dark:text-white' 
                      : 'text-gray-600 dark:text-gray-400'
                  }`}>
                    {message.subject}
                  </div>
                  <div className="text-xs text-gray-500 dark:text-gray-400 truncate mt-0.5">
                    {message.preview}
                  </div>
                </div>
                <div className="col-span-1 text-right">
                  <div className="text-xs font-medium text-gray-500 dark:text-gray-400 whitespace-nowrap">
                    {formatRelativeTime(message.receivedAt)}
                  </div>
                  <div className="text-[11px] text-gray-400 dark:text-gray-500" title={formatDate(message.receivedAt)}>
                    {new Date(message.receivedAt).toLocaleDateString()}
                  </div>
                </div>
                <div className="col-span-1 flex justify-end">
                  <div className="flex space-x-1">
                    <button 
                      className="p-1.5 text-gray-400 hover:text-blue-500 dark:hover:text-blue-400 rounded-full hover:bg-blue-50 dark:hover:bg-blue-900/30 transition-colors"
                      onClick={(e) => {
                        e.stopPropagation();
                        // View action
                      }}
                      title="View message"
                    >
                      <FiEye className="h-4 w-4" />
                    </button>
                    <button 
                      className="p-1.5 text-gray-400 hover:text-red-500 dark:hover:text-red-400 rounded-full hover:bg-red-50 dark:hover:bg-red-900/30 transition-colors"
                      onClick={(e) => {
                        e.stopPropagation();
                        // Delete action
                      }}
                      title="Delete message"
                    >
                      <FiTrash2 className="h-4 w-4" />
                    </button>
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>

    {/* Pagination */}
    <div className="bg-white dark:bg-gray-900 border-t border-gray-200 dark:border-gray-800 px-4 py-3 sm:px-6">
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center space-x-2 mb-3 sm:mb-0">
          <p className="text-sm text-gray-700 dark:text-gray-300">
            Showing <span className="font-medium">1</span> to{' '}
            <span className="font-medium">{filteredMessages.length}</span> of{' '}
            <span className="font-medium">{messages.length}</span> results
          </p>
          <span className="hidden md:inline text-gray-400">|</span>
          <div className="hidden md:flex items-center text-sm text-gray-500 dark:text-gray-400">
            <span>Rows per page:</span>
            <select 
              className="ml-2 bg-transparent border-0 text-sm font-medium text-gray-700 dark:text-gray-300 focus:outline-none focus:ring-0 p-0"
            >
              <option>10</option>
              <option>25</option>
              <option>50</option>
              <option>100</option>
            </select>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <Button 
            variant="outline" 
            size="sm" 
            className="gap-1.5 h-8 px-2.5 text-xs border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
            disabled
          >
            <FiChevronLeft className="h-3.5 w-3.5" />
            <span className="hidden sm:inline">Previous</span>
          </Button>
          <div className="flex items-center space-x-1">
            {[1, 2, 3, 4, 5].map((page) => (
              <button
                key={page}
                className={`inline-flex items-center justify-center h-8 w-8 rounded-md text-sm font-medium ${
                  page === 1
                    ? 'bg-blue-100 dark:bg-blue-900/50 text-blue-700 dark:text-blue-300'
                    : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800'
                }`}
              >
                {page}
              </button>
            ))}
            <span className="px-2 text-gray-400">...</span>
            <button className="h-8 w-8 rounded-md text-sm font-medium text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-gray-800">
              10
            </button>
          </div>
          <Button 
            variant="outline" 
            size="sm" 
            className="gap-1.5 h-8 px-2.5 text-xs border-gray-300 dark:border-gray-600 text-gray-700 dark:text-gray-300 hover:bg-gray-50 dark:hover:bg-gray-800"
          >
            <span className="hidden sm:inline">Next</span>
            <FiChevronRight className="h-3.5 w-3.5" />
          </Button>
        </div>
              </div>
      </div>
      
      {/* New Message Dialog */}
      <NewMessageDialog
        isOpen={showNewMessageDialog}
        onClose={() => setShowNewMessageDialog(false)}
      />
    </div>
  );
}
