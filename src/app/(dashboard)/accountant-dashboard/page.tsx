'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import AccountantDashboard from '@/components/dashboard/AccountantDashboard';
import { useGlobalPreloader } from '@/hooks/useGlobalPreloader';

export default function AccountantDashboardPage() {
  const { user, isAuthenticated, logout } = useAuth();
  const router = useRouter();
  const { showPreloader, hidePreloader } = useGlobalPreloader();
  const [accessError, setAccessError] = useState<string | null>(null);

  React.useEffect(() => {
    if (isAuthenticated === false) {
      router.push('/login');
    } else if (isAuthenticated === true && user) {
      // Hide preloader once authenticated
      hidePreloader();
      // Check if user has the correct role
      if (!user.role) {
        setAccessError('Your user account is missing a role assignment.');
      } else if (user.role !== 'c_accountant') {
        setAccessError(`Your current role (${user.role}) does not have permission to access the accountant dashboard.`);
      } else {
        // Role is correct, clear any error
        setAccessError(null);
      }
    } else {
      // Show beautiful preloader while checking authentication
      showPreloader('Checking authentication...', 'elegant');
    }
  }, [isAuthenticated, router, user, showPreloader, hidePreloader]);

  if (!isAuthenticated) {
    // The beautiful preloader is shown via the global preloader system
    return null;
  }

  if (accessError) {
    return (
      <div className="container mx-auto px-4 py-8">
        <div className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-6">
          <div className="flex items-center mb-4">
            <svg className="h-8 w-8 text-red-500 mr-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            <h2 className="text-lg font-semibold text-red-800 dark:text-red-200">Access Denied</h2>
          </div>
          
          <p className="mb-4 text-red-700 dark:text-red-300">{accessError}</p>
          
          <p className="mb-6 text-red-600 dark:text-red-400">Please contact your administrator if you believe you should have access to this page.</p>
          
          <div className="flex space-x-4">
            <button 
              onClick={() => router.push('/dashboard')} 
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Go to Dashboard
            </button>
            <button 
              onClick={() => logout()} 
              className="px-4 py-2 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-100 rounded hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If we get here, user has the correct role and is not null
  return user ? <AccountantDashboard user={user} /> : null;
}
