'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import Link from 'next/link';
import {
  ArrowLeftIcon,
  PencilIcon,
  EyeIcon,
  PaperClipIcon,
  StarIcon,
  MagnifyingGlassIcon,
  FunnelIcon,
  BarsArrowUpIcon,
  CalendarIcon
} from '@heroicons/react/20/solid';

// Mock requisition detail data - matching the image exactly
const getRequisitionDetail = (projectId: string, caseNo: string) => {
  // This would typically come from an API
  if ((projectId === '0001' && caseNo === '001') || (projectId === '0002' && caseNo === '173')) {
    return {
      id: 'REQ-001-001',
      caseNo: '173',
      referenceNumber: 'UNRA/SUPLS/24-25/173',
      subject: 'Spare parts vehicle UAG 257R',
      priority: 'Urgent',
      status: 'Requisition',
      procurementOfficer: '<PERSON>',
      budgetCode: '228002',
      contactPerson: '<PERSON>, 0777074589',
      deliveryInstructions: 'Toyota Arua, 15 Arua Road',
      dateRequired: 'Sep 18, 2024',
      dateCreated: 'Sep 10, 2024',
      goodsReceivedDate: 'MMM DD, YYYY',
      department: 'Mechanical',
      items: [
        {
          lineItem: '01',
          description: 'Diesel Filter FF5850',
          partNumber: 'FF5850',
          unit: 'pcs',
          quantity: 30,
          attachment: 'View',
          itemComments: 'Preferably from Toyota',
          action: 'Edit'
        },
        {
          lineItem: '02',
          description: 'Oil Filter LF5350',
          partNumber: 'LF5350',
          unit: 'pairs',
          quantity: 40,
          attachment: 'View',
          itemComments: 'Preferably from Toyota',
          action: 'Edit'
        },
        {
          lineItem: '03',
          description: 'Air Filter AF2350',
          partNumber: 'PJ1050',
          unit: 'pcs',
          quantity: 20,
          attachment: 'View',
          itemComments: 'Fuel efficient model',
          action: 'Edit'
        },
        {
          lineItem: '04',
          description: 'Spark Plug BP1250',
          partNumber: 'AB6350',
          unit: 'pcs',
          quantity: 40,
          attachment: 'View',
          itemComments: 'Chasis No. X340K,HK047RD,JRD',
          action: 'Edit'
        },
        {
          lineItem: '05',
          description: 'Fuel Filter FT7150',
          partNumber: 'ZK2350',
          unit: 'pcs',
          quantity: 20,
          attachment: 'No attachment',
          itemComments: 'Preferably from Toyota',
          action: 'Edit'
        },
        {
          lineItem: '06',
          description: 'Brake Pad BF4350',
          partNumber: 'MD7850',
          unit: 'pairs',
          quantity: 6,
          attachment: 'View',
          itemComments: '',
          action: 'Edit'
        },
        {
          lineItem: '07',
          description: 'Wiper Blade WB3560',
          partNumber: 'QP6050',
          unit: 'pcs',
          quantity: 6,
          attachment: 'No attachment',
          itemComments: 'Chasis No. X340K,HK047RD,JRD',
          action: 'Edit'
        }
      ],
      tabs: [
        { name: 'Requisition', active: true, href: '#' },
        { name: 'Inquiry Sent', active: false, href: '#' },
        { name: 'Approved', active: false, href: '#' },
        { name: 'Purchase Order', active: false, href: '#' },
        { name: 'Goods Received', active: false, href: '#' },
        { name: 'Invoice', active: false, href: '#' },
        { name: 'Closed', active: false, href: '#' }
      ]
    };
  }

  // Default/fallback data
  return {
    id: `REQ-${projectId}-${caseNo}`,
    caseNo: caseNo,
    referenceNumber: `UNRA/PROJ${projectId}/24-25/${caseNo}`,
    subject: 'Sample Requisition',
    priority: 'Medium',
    status: 'Draft',
    procurementOfficer: 'John Doe',
    budgetCode: '000000',
    contactPerson: 'Contact Person, 0777000000',
    deliveryInstructions: 'Main Office',
    dateRequired: 'TBD',
    dateCreated: 'TBD',
    goodsReceivedDate: 'MMM DD, YYYY',
    department: 'General',
    items: [],
    tabs: [
      { name: 'Requisition', active: true, href: '#' },
      { name: 'Inquiry Sent', active: false, href: '#' },
      { name: 'Approved', active: false, href: '#' },
      { name: 'Purchase Order', active: false, href: '#' },
      { name: 'Goods Received', active: false, href: '#' },
      { name: 'Invoice', active: false, href: '#' },
      { name: 'Closed', active: false, href: '#' }
    ]
  };
};

// Get project name
const getProjectName = (projectId: string) => {
  const projectNames: Record<string, string> = {
    '0001': 'Administration',
    '0002': 'Mechanical',
    '0003': 'Civil Works',
    '0004': 'Legal & Audit',
    '0005': 'Design'
  };
  return projectNames[projectId] || 'Unknown Project';
};

// Priority color mapping
const getPriorityColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'urgent':
      return 'bg-orange-100 text-orange-800';
    case 'high':
      return 'bg-red-100 text-red-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'low':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Status color mapping
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'draft':
      return 'bg-gray-100 text-gray-800';
    case 'submitted':
      return 'bg-blue-100 text-blue-800';
    case 'in progress':
      return 'bg-yellow-100 text-yellow-800';
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default function RequisitionDetailPage() {
  const params = useParams();
  const projectId = params.id as string;
  const caseNo = params.caseNo as string;
  const projectName = getProjectName(projectId);
  const requisition = getRequisitionDetail(projectId, caseNo);
  const [activeTab, setActiveTab] = useState('Requisition');

  return (
    <div className="flex h-screen bg-gray-50">
      {/* This page will be rendered within the dashboard layout which already has the sidebar */}
      <div className="flex-1 flex flex-col overflow-hidden">
        {/* Top navigation bar */}
        <div className="bg-white border-b border-gray-200 px-6 py-3">
          <div className="flex items-center justify-between">
            <div className="flex items-center text-sm text-gray-600">
              <span>0002 Mechanical</span>
              <span className="mx-2 text-gray-400">•</span>
              <span className="text-gray-900 font-medium">UNRA/SUPLS/24-25/173</span>
            </div>
            <div className="flex items-center space-x-2">
              <Button variant="outline" className="text-red-600 border-red-300 hover:bg-red-50 text-sm px-3 py-1">
                Delete Case
              </Button>
              <Button variant="outline" className="border-gray-300 text-sm px-3 py-1">
                Save
              </Button>
              <Button className="bg-blue-600 hover:bg-blue-700 text-sm px-3 py-1">
                Save and Submit
              </Button>
            </div>
          </div>
        </div>

        {/* Main content area */}
        <div className="flex-1 overflow-auto">
          <div className="p-6">
            {/* Title with star */}
            <div className="flex items-center mb-2">
              <h1 className="text-2xl font-bold text-gray-900 mr-2">
                {requisition.referenceNumber}
              </h1>
              <StarIcon className="w-5 h-5 text-yellow-400 fill-current" />
            </div>

            {/* Subject */}
            <p className="text-gray-600 mb-6">{requisition.subject}</p>

            {/* Workflow tabs */}
            <div className="border-b border-gray-200 mb-6">
              <nav className="-mb-px flex space-x-8">
                {requisition.tabs.map((tab) => (
                  <button
                    key={tab.name}
                    onClick={() => setActiveTab(tab.name)}
                    className={`py-2 px-1 border-b-2 font-medium text-sm whitespace-nowrap ${
                      activeTab === tab.name
                        ? 'border-blue-500 text-blue-600'
                        : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                    }`}
                  >
                    {tab.name}
                  </button>
                ))}
              </nav>
            </div>

            {/* Form fields in exact layout from image */}
            <div className="grid grid-cols-6 gap-3 mb-4">
              {/* First row */}
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Case no.</label>
                <input
                  type="text"
                  value={requisition.caseNo}
                  className="w-full px-2 py-1.5 border border-gray-300 rounded text-sm bg-gray-50"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Reference number</label>
                <input
                  type="text"
                  value={requisition.referenceNumber}
                  className="w-full px-2 py-1.5 border border-gray-300 rounded text-sm bg-gray-50"
                  readOnly
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Dept/Office/Project</label>
                <input
                  type="text"
                  value={requisition.department}
                  className="w-full px-2 py-1.5 border border-gray-300 rounded text-sm"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Priority</label>
                <div className="flex items-center pt-1">
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                    <span className="w-2 h-2 bg-orange-400 rounded-full mr-1"></span>
                    {requisition.priority}
                  </span>
                </div>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Delivery Instructions</label>
                <input
                  type="text"
                  value={requisition.deliveryInstructions}
                  className="w-full px-2 py-1.5 border border-gray-300 rounded text-sm"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Date required</label>
                <div className="relative">
                  <input
                    type="text"
                    value={requisition.dateRequired}
                    className="w-full px-2 py-1.5 border border-gray-300 rounded text-sm pr-7"
                  />
                  <CalendarIcon className="w-4 h-4 text-gray-400 absolute right-2 top-1/2 transform -translate-y-1/2" />
                </div>
              </div>

              {/* Second row */}
              <div className="col-span-2">
                <label className="block text-xs font-medium text-gray-700 mb-1">Subject</label>
                <input
                  type="text"
                  value={requisition.subject}
                  className="w-full px-2 py-1.5 border border-gray-300 rounded text-sm"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Procurement Officer</label>
                <select className="w-full px-2 py-1.5 border border-gray-300 rounded text-sm">
                  <option>{requisition.procurementOfficer}</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Budget code</label>
                <select className="w-full px-2 py-1.5 border border-gray-300 rounded text-sm">
                  <option>{requisition.budgetCode}</option>
                </select>
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Contact person</label>
                <input
                  type="text"
                  value={requisition.contactPerson}
                  className="w-full px-2 py-1.5 border border-gray-300 rounded text-sm"
                />
              </div>
              <div>
                <label className="block text-xs font-medium text-gray-700 mb-1">Goods received date</label>
                <div className="relative">
                  <input
                    type="text"
                    value={requisition.goodsReceivedDate}
                    className="w-full px-2 py-1.5 border border-gray-300 rounded text-sm bg-gray-50 pr-7"
                    readOnly
                  />
                  <CalendarIcon className="w-4 h-4 text-gray-400 absolute right-2 top-1/2 transform -translate-y-1/2" />
                </div>
              </div>

              {/* Status row */}
              <div className="col-span-6">
                <label className="block text-xs font-medium text-gray-700 mb-1">Status</label>
                <span className="inline-flex px-2 py-1 text-xs font-medium rounded bg-blue-100 text-blue-800">
                  {requisition.status}
                </span>
              </div>
            </div>

            {/* Search and actions bar */}
            <div className="flex items-center justify-between mb-4">
              <div className="flex items-center space-x-4">
                <div className="relative">
                  <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 absolute left-3 top-1/2 transform -translate-y-1/2" />
                  <input
                    type="text"
                    placeholder="Search Line Item, Part number..."
                    className="pl-10 pr-4 py-2 border border-gray-300 rounded-md text-sm w-80"
                  />
                </div>
              </div>
              <div className="flex items-center space-x-2">
                <Button variant="outline" className="text-sm px-3 py-1">
                  <FunnelIcon className="w-4 h-4 mr-1" />
                  Filter
                </Button>
                <Button variant="outline" className="text-sm px-3 py-1">
                  <BarsArrowUpIcon className="w-4 h-4 mr-1" />
                  Sort by
                </Button>
                <Button className="bg-teal-600 hover:bg-teal-700 text-sm px-3 py-1">
                  Add Item
                </Button>
              </div>
            </div>

        {/* Items Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="min-w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Line Item</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Description</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Part Number</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Unit</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Quantity</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Attachment</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Item Comments</th>
                  <th className="px-3 py-2 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Action</th>
                </tr>
              </thead>
              <tbody className="bg-white">
                {requisition.items.map((item, index) => (
                  <tr key={index} className={`border-b border-gray-100 hover:bg-gray-100 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                    <td className="px-3 py-2 text-sm font-medium text-gray-900">
                      {item.lineItem}
                    </td>
                    <td className="px-3 py-2 text-sm text-gray-900">
                      {item.description}
                    </td>
                    <td className="px-3 py-2 text-sm text-gray-900">
                      {item.partNumber}
                    </td>
                    <td className="px-3 py-2 text-sm text-gray-900">
                      {item.unit}
                    </td>
                    <td className="px-3 py-2 text-sm text-gray-900">
                      {item.quantity}
                    </td>
                    <td className="px-3 py-2 text-sm">
                      {item.attachment === 'View' ? (
                        <button className="text-blue-600 hover:text-blue-800 flex items-center text-xs">
                          <EyeIcon className="w-3 h-3 mr-1" />
                          View
                        </button>
                      ) : (
                        <span className="text-gray-400 text-xs">{item.attachment}</span>
                      )}
                    </td>
                    <td className="px-3 py-2 text-sm text-gray-900">
                      {item.itemComments || '-'}
                    </td>
                    <td className="px-3 py-2 text-sm">
                      <button className="text-blue-600 hover:text-blue-800 text-xs flex items-center">
                        <PencilIcon className="w-3 h-3 mr-1" />
                        Edit
                      </button>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
          </div>
        </div>
      </div>
    </div>
  );
}
