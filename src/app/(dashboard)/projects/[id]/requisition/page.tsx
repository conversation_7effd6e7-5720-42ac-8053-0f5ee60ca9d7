'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import { But<PERSON> } from '@/components/ui/button';
import Link from 'next/link';
import { ArrowLeftIcon, PlusIcon, PencilIcon, EyeIcon, MagnifyingGlassIcon, FunnelIcon, ArrowsUpDownIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/20/solid';

// Mock requisition data based on project ID
const getRequisitionData = (projectId: string) => {
  const requisitionMap: Record<string, any[]> = {
    '0001': [
      {
        id: 'REQ-001-001',
        caseNo: '001',
        referenceNumber: 'UNRA/ADMIN/24-25/001',
        subject: 'Office Supplies and Equipment',
        priority: 'Medium',
        status: 'Submitted',
        procurementOfficer: '<PERSON>',
        budgetCode: '110001',
        contactPerson: '<PERSON>, 0777123456',
        deliveryInstructions: 'Main Office, Block A',
        dateRequired: '2024-02-15',
        dateCreated: '2024-01-20',
        totalItems: 15,
        estimatedValue: '$5,200'
      },
      {
        id: 'REQ-001-002',
        caseNo: '002',
        referenceNumber: 'UNRA/ADMIN/24-25/002',
        subject: 'IT Equipment Procurement',
        priority: 'High',
        status: 'Approved',
        procurementOfficer: 'John Smith',
        budgetCode: '110002',
        contactPerson: 'Bob Williams, 0777234567',
        deliveryInstructions: 'IT Department, 2nd Floor',
        dateRequired: '2024-03-01',
        dateCreated: '2024-02-05',
        totalItems: 8,
        estimatedValue: '$12,500'
      }
    ],
    '0002': [
      {
        id: 'REQ-002-001',
        caseNo: '173',
        referenceNumber: 'UNRA/SUPLS/24-25/173',
        subject: 'Spare parts vehicle UAG 2576',
        priority: 'Urgent',
        status: 'Submitted',
        procurementOfficer: 'Brian Gimei',
        budgetCode: '229002',
        contactPerson: 'Anne Kakung, 0777074589',
        deliveryInstructions: 'Toyota Arua, 15 Arua Road',
        dateRequired: '2024-09-18',
        dateCreated: '2024-09-10',
        totalItems: 7,
        estimatedValue: '$3,450'
      },
      {
        id: 'REQ-002-002',
        caseNo: '174',
        referenceNumber: 'UNRA/MECH/24-25/174',
        subject: 'Heavy Machinery Maintenance',
        priority: 'High',
        status: 'In Progress',
        procurementOfficer: 'Emily Brown',
        budgetCode: '229003',
        contactPerson: 'David Wilson, 0777345678',
        deliveryInstructions: 'Workshop Bay 3',
        dateRequired: '2024-10-05',
        dateCreated: '2024-09-15',
        totalItems: 12,
        estimatedValue: '$8,750'
      }
    ],
    '0003': [
      {
        id: 'REQ-003-001',
        caseNo: '175',
        referenceNumber: 'UNRA/CIVIL/24-25/175',
        subject: 'Construction Materials',
        priority: 'Medium',
        status: 'Draft',
        procurementOfficer: 'Michael Johnson',
        budgetCode: '330001',
        contactPerson: 'Sarah Adams, 0777456789',
        deliveryInstructions: 'Site Office, Block C',
        dateRequired: '2024-11-01',
        dateCreated: '2024-09-20',
        totalItems: 25,
        estimatedValue: '$45,000'
      }
    ],
    '0004': [
      {
        id: 'REQ-004-001',
        caseNo: '176',
        referenceNumber: 'UNRA/LEGAL/24-25/176',
        subject: 'Legal Documentation Services',
        priority: 'Low',
        status: 'Approved',
        procurementOfficer: 'Jennifer Lee',
        budgetCode: '440001',
        contactPerson: 'Robert Clark, **********',
        deliveryInstructions: 'Legal Department',
        dateRequired: '2024-10-15',
        dateCreated: '2024-09-01',
        totalItems: 3,
        estimatedValue: '$2,800'
      }
    ],
    '0005': [
      {
        id: 'REQ-005-001',
        caseNo: '177',
        referenceNumber: 'UNRA/DESIGN/24-25/177',
        subject: 'Design Software Licenses',
        priority: 'Medium',
        status: 'Submitted',
        procurementOfficer: 'Daniel Martinez',
        budgetCode: '550001',
        contactPerson: 'Lisa Anderson, **********',
        deliveryInstructions: 'Design Studio',
        dateRequired: '2024-12-01',
        dateCreated: '2024-09-25',
        totalItems: 5,
        estimatedValue: '$15,600'
      }
    ]
  };

  return requisitionMap[projectId] || [];
};

// Get project name
const getProjectName = (projectId: string) => {
  const projectNames: Record<string, string> = {
    '0001': 'Administration',
    '0002': 'Mechanical',
    '0003': 'Civil Works',
    '0004': 'Legal & Audit',
    '0005': 'Design'
  };
  return projectNames[projectId] || 'Unknown Project';
};

// Priority color mapping
const getPriorityColor = (priority: string) => {
  switch (priority.toLowerCase()) {
    case 'urgent':
      return 'bg-red-100 text-red-800';
    case 'high':
      return 'bg-orange-100 text-orange-800';
    case 'medium':
      return 'bg-yellow-100 text-yellow-800';
    case 'low':
      return 'bg-green-100 text-green-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

// Status color mapping
const getStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case 'draft':
      return 'bg-gray-100 text-gray-800';
    case 'submitted':
      return 'bg-blue-100 text-blue-800';
    case 'in progress':
      return 'bg-yellow-100 text-yellow-800';
    case 'approved':
      return 'bg-green-100 text-green-800';
    case 'rejected':
      return 'bg-red-100 text-red-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
};

export default function ProjectRequisitionPage() {
  const params = useParams();
  const projectId = params.id as string;
  const projectName = getProjectName(projectId);
  const requisitions = getRequisitionData(projectId);
  const [searchTerm, setSearchTerm] = useState('');
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Filter requisitions based on search term (includes year)
  const filteredRequisitions = requisitions.filter(req => {
    const term = searchTerm.toLowerCase();
    const year = (req.dateCreated || '').slice(0, 4);
    return (
      req.subject.toLowerCase().includes(term) ||
      req.referenceNumber.toLowerCase().includes(term) ||
      req.caseNo.includes(searchTerm) ||
      year.includes(term) ||
      projectId.includes(searchTerm)
    );
  });

  const total = filteredRequisitions.length;
  const totalPages = Math.max(1, Math.ceil(total / rowsPerPage));
  const safePage = Math.min(currentPage, totalPages);
  const startIndex = total === 0 ? 0 : (safePage - 1) * rowsPerPage;
  const endIndex = Math.min(startIndex + rowsPerPage, total);
  const pageItems = filteredRequisitions.slice(startIndex, endIndex);

  return (
    <div className="p-16">
      {/* Breadcrumb */}
      <div className="mb-4">
        <div className="text-sm text-gray-500">
          🏠 {projectId} {projectName} / Requisitions
        </div>
      </div>

      {/* Header Bar */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h1 className="text-xl font-semibold">Requisitions</h1>
        </div>
        <Button className="bg-[#18546c] hover:bg-[#1a6985] text-white">
          <PlusIcon className="w-4 h-4 mr-2" />
          Create New
        </Button>
      </div>

      {/* Toolbar */}
      <div className="flex items-center justify-between bg-white border rounded-md px-3 py-2 mb-3">
        <div className="flex items-center gap-3 flex-1">
          <div className="flex items-center bg-gray-50 border rounded-md px-3 py-2 w-full max-w-md">
            <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 mr-2" />
            <input
              value={searchTerm}
              onChange={(e) => { setSearchTerm(e.target.value); setCurrentPage(1); }}
              placeholder="Search ID, Year, Subject, Reference..."
              className="bg-transparent outline-none w-full text-sm"
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="text-sm">
            <FunnelIcon className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" className="text-sm">
            <ArrowsUpDownIcon className="w-4 h-4 mr-2" />
            Sort by
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead>
              <tr className="bg-gray-50 text-gray-600">
                <th className="px-4 py-3 text-left font-medium">Project ID</th>
                <th className="px-4 py-3 text-left font-medium">Year</th>
                <th className="px-4 py-3 text-left font-medium">Case No.</th>
                <th className="px-4 py-3 text-left font-medium">Reference</th>
                <th className="px-4 py-3 text-left font-medium">Subject</th>
                <th className="px-4 py-3 text-left font-medium">Status</th>
                <th className="px-4 py-3 text-left font-medium">Date created</th>
                <th className="px-4 py-3 text-left font-medium">Budget code</th>
                <th className="px-4 py-3 text-left font-medium">Budget category</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {pageItems.length === 0 ? (
                <tr>
                  <td colSpan={9} className="px-4 py-10 text-center text-gray-500">No requisitions found.</td>
                </tr>
              ) : (
                pageItems.map((r) => {
                  const year = (r.dateCreated || '').slice(0, 4) || '2024';
                  const budgetCategory = r.budgetCategory || '—';
                  return (
                    <tr key={r.id} className="hover:bg-gray-50">
                      <td className="px-4 py-3">{projectId}</td>
                      <td className="px-4 py-3">{year}</td>
                      <td className="px-4 py-3">{r.caseNo}</td>
                      <td className="px-4 py-3 text-[#18546c]">{r.referenceNumber}</td>
                      <td className="px-4 py-3">{r.subject}</td>
                      <td className="px-4 py-3">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(r.status)}`}>{r.status}</span>
                      </td>
                      <td className="px-4 py-3">{new Date(r.dateCreated).toDateString().slice(4)}</td>
                      <td className="px-4 py-3">{r.budgetCode}</td>
                      <td className="px-4 py-3">{budgetCategory}</td>
                    </tr>
                  );
                })
              )}
            </tbody>
          </table>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between px-3 py-2 border-t bg-white text-sm">
          <div className="text-gray-500">Showing {total === 0 ? 0 : startIndex + 1} to {endIndex} of {total}</div>
          <div className="flex items-center gap-2">
            <select
              value={rowsPerPage}
              onChange={(e) => { setRowsPerPage(Number(e.target.value)); setCurrentPage(1); }}
              className="text-gray-500 bg-transparent border-0 text-sm focus:ring-0"
            >
              <option value={5}>5 rows</option>
              <option value={10}>10 rows</option>
              <option value={15}>15 rows</option>
              <option value={20}>20 rows</option>
            </select>
            <div className="flex items-center gap-1">
              <Button variant="outline" size="icon" disabled={safePage === 1} onClick={() => setCurrentPage(p => Math.max(1, p - 1))}>
                <ChevronLeftIcon className="w-4 h-4" />
              </Button>
              <span className="px-2">{safePage}</span>
              <Button variant="outline" size="icon" disabled={safePage === totalPages} onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}>
                <ChevronRightIcon className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
