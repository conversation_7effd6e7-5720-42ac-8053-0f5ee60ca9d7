'use client';

import React, { useState } from 'react';
import { useParams } from 'next/navigation';
import { Button } from '@/components/ui/button';
import { PlusIcon, MagnifyingGlassIcon, FunnelIcon, ArrowsUpDownIcon, ChevronLeftIcon, ChevronRightIcon } from '@heroicons/react/20/solid';

// Mock purchase orders data matching the screenshot
const mockPurchaseOrders = [
  { id: 1, projectId: '0002', year: 2024, caseNo: '180', reference: 'UNICEF/0002/24/180', subject: 'Spare parts vehicle UAG 257R', status: 'PO Issued', dateCreated: 'Sep 12, 2024', budgetCode: '228002', budgetCategory: 'Maintenance vehicles' },
  { id: 2, projectId: '0002', year: 2024, caseNo: '179', reference: 'UNICEF/0002/24/179', subject: 'Lawn mower', status: 'PO Confirmed', dateCreated: 'Sep 12, 2024', budgetCode: '228003', budgetCategory: 'Maintenance machinery' },
  { id: 3, projectId: '0002', year: 2024, caseNo: '178', reference: 'UNICEF/0002/24/178', subject: 'Water pump spares', status: 'PO Cancelled', dateCreated: 'Sep 11, 2024', budgetCode: '228004', budgetCategory: 'Construction equipment' },
  { id: 4, projectId: '0002', year: 2024, caseNo: '177', reference: 'UNICEF/0002/24/177', subject: 'Carburetor assembly', status: 'PO Issued', dateCreated: 'Sep 13, 2024', budgetCode: '228005', budgetCategory: 'Maintenance machinery' },
  { id: 5, projectId: '0002', year: 2024, caseNo: '176', reference: 'UNICEF/0002/24/176', subject: 'Spark plugs set', status: 'PO Confirmed', dateCreated: 'Sep 10, 2024', budgetCode: '228006', budgetCategory: 'Maintenance machinery' },
  { id: 6, projectId: '0002', year: 2024, caseNo: '175', reference: 'UNICEF/0002/24/175', subject: 'Muffler exhaust pipe', status: 'PO Confirmed', dateCreated: 'Sep 9, 2024', budgetCode: '228007', budgetCategory: 'Maintenance machinery' },
  { id: 7, projectId: '0002', year: 2024, caseNo: '174', reference: 'UNICEF/0002/24/174', subject: 'Piston rings kit', status: 'PO Issued', dateCreated: 'Sep 8, 2024', budgetCode: '228008', budgetCategory: 'Maintenance vehicles' },
  { id: 8, projectId: '0002', year: 2024, caseNo: '173', reference: 'UNICEF/0002/24/173', subject: 'Fuel filter replacement', status: 'PO Declined', dateCreated: 'Sep 7, 2024', budgetCode: '228009', budgetCategory: 'Maintenance vehicles' },
  { id: 9, projectId: '0002', year: 2024, caseNo: '172', reference: 'UNICEF/0002/24/172', subject: 'Air filter element', status: 'PO Issued', dateCreated: 'Sep 6, 2024', budgetCode: '228010', budgetCategory: 'Maintenance vehicles' },
  { id: 10, projectId: '0002', year: 2024, caseNo: '171', reference: 'UNICEF/0002/24/171', subject: 'Ignition coil module', status: 'PO Issued', dateCreated: 'Sep 5, 2024', budgetCode: '228011', budgetCategory: 'Maintenance vehicles' },
  { id: 11, projectId: '0002', year: 2024, caseNo: '170', reference: 'UNICEF/0002/24/170', subject: 'Oil change kit', status: 'PO Issued', dateCreated: 'Sep 4, 2024', budgetCode: '228012', budgetCategory: 'Maintenance machinery' },
  { id: 12, projectId: '0002', year: 2024, caseNo: '169', reference: 'UNICEF/0002/24/169', subject: 'Brake pads set', status: 'Invoice', dateCreated: 'Sep 3, 2024', budgetCode: '228013', budgetCategory: 'Maintenance vehicles' },
  { id: 13, projectId: '0002', year: 2024, caseNo: '168', reference: 'UNICEF/0002/24/168', subject: 'Transmission fluid', status: 'Invoice', dateCreated: 'Sep 2, 2024', budgetCode: '228014', budgetCategory: 'Maintenance vehicles' },
  { id: 14, projectId: '0002', year: 2024, caseNo: '167', reference: 'UNICEF/0002/24/167', subject: 'Wheel alignment kit', status: 'Invoice', dateCreated: 'Sep 1, 2024', budgetCode: '228015', budgetCategory: 'Maintenance vehicles' },
  { id: 15, projectId: '0002', year: 2024, caseNo: '166', reference: 'UNICEF/0002/24/166', subject: 'Radiator coolant', status: 'Closed', dateCreated: 'Aug 31, 2024', budgetCode: '228016', budgetCategory: 'Maintenance vehicles' },
  { id: 16, projectId: '0002', year: 2024, caseNo: '165', reference: 'UNICEF/0002/24/165', subject: 'Wheel alignment kit', status: 'Closed', dateCreated: 'Aug 30, 2024', budgetCode: '228017', budgetCategory: 'Maintenance vehicles' },
  { id: 17, projectId: '0002', year: 2024, caseNo: '164', reference: 'UNICEF/0002/24/164', subject: 'Spare parts vehicle UAG 257R', status: 'Closed', dateCreated: 'Aug 29, 2024', budgetCode: '228018', budgetCategory: 'Maintenance vehicles' },
];

// Get project name
const getProjectName = (projectId: string) => {
  const projectNames: Record<string, string> = {
    '0001': 'Administration',
    '0002': 'Mechanical',
    '0003': 'Civil Works',
    '0004': 'Legal & Audit',
    '0005': 'Design'
  };
  return projectNames[projectId] || 'Unknown Project';
};

export default function ProjectPOPage() {
  const params = useParams();
  const projectId = params.id as string;
  const projectName = getProjectName(projectId);
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState(1);
  const [rowsPerPage, setRowsPerPage] = useState(10);

  // Filter purchase orders based on search term
  const filteredPOs = mockPurchaseOrders.filter(po => {
    const term = searchTerm.toLowerCase();
    return (
      po.projectId.toLowerCase().includes(term) ||
      po.year.toString().includes(term) ||
      po.caseNo.toLowerCase().includes(term) ||
      po.reference.toLowerCase().includes(term) ||
      po.subject.toLowerCase().includes(term) ||
      po.status.toLowerCase().includes(term) ||
      po.budgetCode.toLowerCase().includes(term) ||
      po.budgetCategory.toLowerCase().includes(term)
    );
  });

  const total = filteredPOs.length;
  const totalPages = Math.max(1, Math.ceil(total / rowsPerPage));
  const safePage = Math.min(currentPage, totalPages);
  const startIndex = total === 0 ? 0 : (safePage - 1) * rowsPerPage;
  const endIndex = Math.min(startIndex + rowsPerPage, total);
  const pageItems = filteredPOs.slice(startIndex, endIndex);

  return (
    <div className="p-16">
      {/* Breadcrumb */}
      <div className="mb-4">
        <div className="text-sm text-gray-500">
          🏠 {projectId} {projectName} / Purchase Orders
        </div>
      </div>

      {/* Header Bar */}
      <div className="flex items-center justify-between mb-4">
        <div>
          <h1 className="text-xl font-semibold">Purchase Orders</h1>
        </div>
        <Button className="bg-[#18546c] hover:bg-[#1a6985] text-white">
          <PlusIcon className="w-4 h-4 mr-2" />
          Create New
        </Button>
      </div>

      {/* Toolbar */}
      <div className="flex items-center justify-between bg-white border rounded-md px-3 py-2 mb-3">
        <div className="flex items-center gap-3 flex-1">
          <div className="flex items-center bg-gray-50 border rounded-md px-3 py-2 w-full max-w-md">
            <MagnifyingGlassIcon className="w-4 h-4 text-gray-400 mr-2" />
            <input
              value={searchTerm}
              onChange={(e) => { setSearchTerm(e.target.value); setCurrentPage(1); }}
              placeholder="Search ID, Year, Subject, Reference..."
              className="bg-transparent outline-none w-full text-sm"
            />
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Button variant="outline" className="text-sm">
            <FunnelIcon className="w-4 h-4 mr-2" />
            Filter
          </Button>
          <Button variant="outline" className="text-sm">
            <ArrowsUpDownIcon className="w-4 h-4 mr-2" />
            Sort by
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="bg-white rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full text-sm">
            <thead>
              <tr className="bg-gray-50 text-gray-600">
                <th className="px-4 py-3 text-left font-medium">Project ID</th>
                <th className="px-4 py-3 text-left font-medium">Year</th>
                <th className="px-4 py-3 text-left font-medium">Case No.</th>
                <th className="px-4 py-3 text-left font-medium">Reference</th>
                <th className="px-4 py-3 text-left font-medium">Subject</th>
                <th className="px-4 py-3 text-left font-medium">Status</th>
                <th className="px-4 py-3 text-left font-medium">Date created</th>
                <th className="px-4 py-3 text-left font-medium">Budget code</th>
                <th className="px-4 py-3 text-left font-medium">Budget category</th>
              </tr>
            </thead>
            <tbody className="divide-y divide-gray-100">
              {pageItems.length === 0 ? (
                <tr>
                  <td colSpan={9} className="px-4 py-10 text-center text-gray-500">No purchase orders found.</td>
                </tr>
              ) : (
                pageItems.map((po) => (
                  <tr key={po.id} className="hover:bg-gray-50">
                    <td className="px-4 py-3">{po.projectId}</td>
                    <td className="px-4 py-3">{po.year}</td>
                    <td className="px-4 py-3">{po.caseNo}</td>
                    <td className="px-4 py-3 text-[#18546c]">{po.reference}</td>
                    <td className="px-4 py-3">{po.subject}</td>
                    <td className="px-4 py-3">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                        po.status === 'Closed' ? 'bg-gray-100 text-gray-800' :
                        po.status === 'Invoice' ? 'bg-green-100 text-green-800' :
                        po.status === 'PO Confirmed' ? 'bg-blue-100 text-blue-800' :
                        po.status === 'PO Issued' ? 'bg-yellow-100 text-yellow-800' :
                        po.status === 'PO Cancelled' ? 'bg-red-100 text-red-800' :
                        po.status === 'PO Declined' ? 'bg-red-100 text-red-800' :
                        'bg-gray-100 text-gray-800'
                      }`}>
                        {po.status}
                      </span>
                    </td>
                    <td className="px-4 py-3">{po.dateCreated}</td>
                    <td className="px-4 py-3">{po.budgetCode}</td>
                    <td className="px-4 py-3">{po.budgetCategory}</td>
                  </tr>
                ))
              )}
            </tbody>
          </table>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between px-3 py-2 border-t bg-white text-sm">
          <div className="text-gray-500">Showing {total === 0 ? 0 : startIndex + 1} to {endIndex} of {total}</div>
          <div className="flex items-center gap-2">
            <select
              value={rowsPerPage}
              onChange={(e) => { setRowsPerPage(Number(e.target.value)); setCurrentPage(1); }}
              className="text-gray-500 bg-transparent border-0 text-sm focus:ring-0"
            >
              <option value={5}>5 rows</option>
              <option value={10}>10 rows</option>
              <option value={15}>15 rows</option>
              <option value={20}>20 rows</option>
            </select>
            <div className="flex items-center gap-1">
              <Button variant="outline" size="icon" disabled={safePage === 1} onClick={() => setCurrentPage(p => Math.max(1, p - 1))}>
                <ChevronLeftIcon className="w-4 h-4" />
              </Button>
              <span className="px-2">{safePage}</span>
              <Button variant="outline" size="icon" disabled={safePage === totalPages} onClick={() => setCurrentPage(p => Math.min(totalPages, p + 1))}>
                <ChevronRightIcon className="w-4 h-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
