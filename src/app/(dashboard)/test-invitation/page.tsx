'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { toast } from 'sonner';
import { ArrowLeftIcon, EnvelopeIcon, LinkIcon, CheckCircleIcon } from '@heroicons/react/24/solid';
import Link from 'next/link';

interface InvitationResult {
  invitationId: string;
  token: string;
  email: string;
  setPasswordUrl: string;
  expiryDate: string;
  emailSent: boolean;
  emailContent?: {
    to: string;
    subject: string;
    html: string;
    text: string;
    mailtoLink: string;
  };
}

export default function TestInvitationPage() {
  const [formData, setFormData] = useState({
    email: '',
    organizationName: 'ABC Supplies Ltd',
    invitedByName: '<PERSON> (Supplier Admin)',
    role: 'supplier_user'
  });
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<InvitationResult | null>(null);
  const [error, setError] = useState<string | null>(null);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!formData.email) {
      toast.error('Email is required');
      return;
    }

    setLoading(true);
    setError(null);
    setResult(null);

    try {
      const response = await fetch('/api/test-invitation', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      const data = await response.json();

      if (data.success) {
        setResult(data.data);
        if (data.data.emailSent) {
          toast.success('Invitation email sent successfully!');
        } else {
          toast.success('Invitation created! Email content provided for manual sending.');
        }
      } else {
        setError(data.error || 'Failed to create invitation');
        toast.error(data.error || 'Failed to create invitation');
      }
    } catch (err: any) {
      setError(err.message || 'An error occurred');
      toast.error(err.message || 'An error occurred');
    } finally {
      setLoading(false);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard.writeText(text);
    toast.success('Copied to clipboard!');
  };

  const openMailto = (mailtoLink: string) => {
    window.open(mailtoLink, '_blank');
  };

  return (
    <div className="container mx-auto px-4 py-8">
      {/* Breadcrumb */}
      <div className="mb-8">
        <Link href="/dashboard" className="inline-flex items-center text-[#2a6e78] hover:text-[#1d4f56] transition-colors">
          <ArrowLeftIcon className="w-4 h-4 mr-2" />
          Back to Dashboard
        </Link>
        <div className="text-sm text-gray-500 mt-1">
          Dashboard / Test Invitation
        </div>
      </div>

      {/* Header */}
      <div className="bg-white rounded-lg shadow-md p-6 mb-8">
        <h1 className="text-2xl font-bold text-gray-800 mb-2">Test Supplier Invitation</h1>
        <p className="text-gray-600">
          Create a real supplier invitation scenario with a working &quot;Set Password&quot; link.
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Form */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-6">Create Test Invitation</h2>
          
          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="email">Recipient Email *</Label>
              <Input
                id="email"
                name="email"
                type="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="<EMAIL>"
                required
              />
            </div>

            <div>
              <Label htmlFor="organizationName">Organization Name</Label>
              <Input
                id="organizationName"
                name="organizationName"
                type="text"
                value={formData.organizationName}
                onChange={handleInputChange}
                placeholder="ABC Supplies Ltd"
              />
            </div>

            <div>
              <Label htmlFor="invitedByName">Invited By</Label>
              <Input
                id="invitedByName"
                name="invitedByName"
                type="text"
                value={formData.invitedByName}
                onChange={handleInputChange}
                placeholder="John Smith (Supplier Admin)"
              />
            </div>

            <div>
              <Label htmlFor="role">Role</Label>
              <Input
                id="role"
                name="role"
                type="text"
                value={formData.role}
                onChange={handleInputChange}
                placeholder="supplier_user"
                readOnly
                className="bg-gray-50"
              />
            </div>

            <Button
              type="submit"
              disabled={loading}
              className="w-full bg-[#2a6e78] hover:bg-[#1d4f56]"
            >
              {loading ? (
                <>
                  <span className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></span>
                  Creating Invitation...
                </>
              ) : (
                <>
                  <EnvelopeIcon className="w-4 h-4 mr-2" />
                  Send Test Invitation
                </>
              )}
            </Button>
          </form>

          {error && (
            <div className="mt-4 p-4 bg-red-50 border border-red-200 rounded-lg">
              <p className="text-red-700 text-sm">{error}</p>
            </div>
          )}
        </div>

        {/* Results */}
        <div className="bg-white rounded-lg shadow-md p-6">
          <h2 className="text-xl font-semibold text-gray-800 mb-6">Invitation Results</h2>
          
          {!result ? (
            <div className="text-center py-8">
              <EnvelopeIcon className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500">No invitation created yet</p>
              <p className="text-sm text-gray-400 mt-2">Fill out the form and click &quot;Send Test Invitation&quot;</p>
            </div>
          ) : (
            <div className="space-y-4">
              {/* Success Status */}
              <div className="flex items-center p-3 bg-green-50 border border-green-200 rounded-lg">
                <CheckCircleIcon className="w-5 h-5 text-green-600 mr-2" />
                <span className="text-green-800 font-medium">
                  {result.emailSent ? 'Email sent successfully!' : 'Invitation created successfully!'}
                </span>
              </div>

              {/* Invitation Details */}
              <div className="space-y-3">
                <div>
                  <Label className="text-sm font-medium text-gray-600">Invitation ID</Label>
                  <div className="flex items-center space-x-2">
                    <code className="flex-1 p-2 bg-gray-100 rounded text-sm font-mono">
                      {result.invitationId}
                    </code>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(result.invitationId)}
                    >
                      Copy
                    </Button>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Token</Label>
                  <div className="flex items-center space-x-2">
                    <code className="flex-1 p-2 bg-gray-100 rounded text-sm font-mono">
                      {result.token}
                    </code>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(result.token)}
                    >
                      Copy
                    </Button>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Set Password URL</Label>
                  <div className="flex items-center space-x-2">
                    <code className="flex-1 p-2 bg-gray-100 rounded text-sm font-mono break-all">
                      {result.setPasswordUrl}
                    </code>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => copyToClipboard(result.setPasswordUrl)}
                    >
                      Copy
                    </Button>
                  </div>
                </div>

                <div>
                  <Label className="text-sm font-medium text-gray-600">Expires</Label>
                  <p className="p-2 bg-gray-100 rounded text-sm">
                    {new Date(result.expiryDate).toLocaleString()}
                  </p>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex space-x-2 pt-4">
                <Button
                  onClick={() => window.open(result.setPasswordUrl, '_blank')}
                  className="flex-1 bg-[#2a6e78] hover:bg-[#1d4f56]"
                >
                  <LinkIcon className="w-4 h-4 mr-2" />
                  Test Set Password Page
                </Button>
                
                {result.emailContent && (
                  <Button
                    variant="outline"
                    onClick={() => openMailto(result.emailContent!.mailtoLink)}
                    className="flex-1"
                  >
                    <EnvelopeIcon className="w-4 h-4 mr-2" />
                    Open Email Client
                  </Button>
                )}
              </div>

              {/* Email Content (if not sent) */}
              {!result.emailSent && result.emailContent && (
                <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <h3 className="font-medium text-yellow-800 mb-2">Email Content (Manual Sending Required)</h3>
                  <div className="space-y-2 text-sm">
                    <div>
                      <strong>To:</strong> {result.emailContent.to}
                    </div>
                    <div>
                      <strong>Subject:</strong> {result.emailContent.subject}
                    </div>
                    <div>
                      <strong>Body:</strong>
                      <pre className="mt-1 p-2 bg-white border rounded text-xs overflow-x-auto">
                        {result.emailContent.text}
                      </pre>
                    </div>
                  </div>
                </div>
              )}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
