'use client';

import React, { Suspense, useState } from 'react';
import { useSearch<PERSON>ara<PERSON>, useRouter } from 'next/navigation';
import Link from 'next/link';
import { MagnifyingGlassIcon, FunnelIcon, ChevronDownIcon, ChevronUpIcon, DocumentTextIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';

// Mock data for the table - matching the image exactly
const mockRequisitions = [
  {
    id: '0002',
    year: '2024',
    caseNo: '180',
    reference: 'UNICEF/0002/24/180',
    subject: 'Spare parts vehicle UAG 257R',
    status: 'Requisition',
    dateCreated: 'Oct 14, 2024',
    budgetCode: '228002',
    budgetCategory: 'Maintenance vehicles'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '179',
    reference: 'UNICEF/0002/24/179',
    subject: 'Lawn mower',
    status: 'Requisition',
    dateCreated: 'Oct 12, 2024',
    budgetCode: '228003',
    budgetCategory: 'Maintenance machinery'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '178',
    reference: 'UNICEF/0002/24/178',
    subject: 'Water pump spares',
    status: 'Requisition',
    dateCreated: 'Oct 11, 2024',
    budgetCode: '228004',
    budgetCategory: 'Construction equipment'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '177',
    reference: 'UNICEF/0002/24/177',
    subject: 'Carburetor assembly',
    status: 'Requisition',
    dateCreated: 'Oct 13, 2024',
    budgetCode: '228005',
    budgetCategory: 'Maintenance machinery'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '176',
    reference: 'UNICEF/0002/24/176',
    subject: 'Spark plugs set',
    status: 'Submitted',
    dateCreated: 'Oct 10, 2024',
    budgetCode: '228006',
    budgetCategory: 'Maintenance machinery'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '175',
    reference: 'UNICEF/0002/24/175',
    subject: 'Muffler exhaust pipe',
    status: 'Submitted',
    dateCreated: 'Oct 9, 2024',
    budgetCode: '228007',
    budgetCategory: 'Maintenance machinery'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '174',
    reference: 'UNICEF/0002/24/174',
    subject: 'Piston rings kit',
    status: 'Submitted',
    dateCreated: 'Oct 8, 2024',
    budgetCode: '228008',
    budgetCategory: 'Maintenance vehicles'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '173',
    reference: 'UNICEF/0002/24/173',
    subject: 'Fuel filter replacement',
    status: 'Submitted',
    dateCreated: 'Oct 7, 2024',
    budgetCode: '228009',
    budgetCategory: 'Maintenance vehicles'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '172',
    reference: 'UNICEF/0002/24/172',
    subject: 'Air filter element',
    status: 'Submitted',
    dateCreated: 'Oct 6, 2024',
    budgetCode: '228010',
    budgetCategory: 'Maintenance vehicles'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '171',
    reference: 'UNICEF/0002/24/171',
    subject: 'Ignition coil module',
    status: 'Submitted',
    dateCreated: 'Oct 5, 2024',
    budgetCode: '228011',
    budgetCategory: 'Maintenance vehicles'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '170',
    reference: 'UNICEF/0002/24/170',
    subject: 'Oil change kit',
    status: 'Submitted',
    dateCreated: 'Oct 4, 2024',
    budgetCode: '228012',
    budgetCategory: 'Maintenance machinery'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '169',
    reference: 'UNICEF/0002/24/169',
    subject: 'Brake pads set',
    status: 'Submitted',
    dateCreated: 'Oct 3, 2024',
    budgetCode: '228013',
    budgetCategory: 'Maintenance vehicles'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '168',
    reference: 'UNICEF/0002/24/168',
    subject: 'Transmission fluid',
    status: 'Submitted',
    dateCreated: 'Oct 2, 2024',
    budgetCode: '228014',
    budgetCategory: 'Maintenance vehicles'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '167',
    reference: 'UNICEF/0002/24/167',
    subject: 'Wheel alignment kit',
    status: 'Submitted',
    dateCreated: 'Oct 1, 2024',
    budgetCode: '228015',
    budgetCategory: 'Maintenance vehicles'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '166',
    reference: 'UNICEF/0002/24/166',
    subject: 'Radiator coolant',
    status: 'Submitted',
    dateCreated: 'Sep 31, 2024',
    budgetCode: '228016',
    budgetCategory: 'Maintenance vehicles'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '165',
    reference: 'UNICEF/0002/24/165',
    subject: 'Wheel alignment kit',
    status: 'Submitted',
    dateCreated: 'Sep 30, 2024',
    budgetCode: '228017',
    budgetCategory: 'Maintenance vehicles'
  },
  {
    id: '0002',
    year: '2024',
    caseNo: '164',
    reference: 'UNICEF/0002/24/164',
    subject: 'Spare parts vehicle UAG 257R',
    status: 'Submitted',
    dateCreated: 'Sep 29, 2024',
    budgetCode: '228018',
    budgetCategory: 'Maintenance vehicles'
  }

];

const statusVariantMap = {
  'Pending': 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-400',
  'In Review': 'bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-400',
  'Approved': 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-400',
  'Rejected': 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-400',
};

// Create a separate component that uses useSearchParams
function PendingRequisitionsContent() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const statusFilter = searchParams.get('status');
  
  const [searchTerm, setSearchTerm] = useState('');
  const [showFilters, setShowFilters] = useState(false);
  const [sortConfig, setSortConfig] = useState<{ key: string; direction: 'asc' | 'desc' } | null>(null);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  const [activeFilter, setActiveFilter] = useState<string | null>(statusFilter || 'All');
  
  const handleSort = (key: string) => {
    let direction: 'asc' | 'desc' = 'asc';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'asc') {
      direction = 'desc';
    }
    setSortConfig({ key, direction });
  };
  
  const sortedRequisitions = [...mockRequisitions].sort((a, b) => {
    if (!sortConfig) return 0;
    
    const aValue = a[sortConfig.key as keyof typeof a];
    const bValue = b[sortConfig.key as keyof typeof b];
    
    if (aValue < bValue) {
      return sortConfig.direction === 'asc' ? -1 : 1;
    }
    if (aValue > bValue) {
      return sortConfig.direction === 'asc' ? 1 : -1;
    }
    return 0;
  });
  
  const filteredRequisitions = sortedRequisitions.filter(requisition => {
    const searchLower = searchTerm.toLowerCase();
    const matchesSearch = 
      requisition.id.toLowerCase().includes(searchLower) ||
      requisition.subject.toLowerCase().includes(searchLower) ||
      requisition.reference.toLowerCase().includes(searchLower);
      
    const matchesFilter = 
      activeFilter === 'All' || 
      requisition.status === activeFilter;
      
    return matchesSearch && matchesFilter;
  });
  
  return (
    <div className="p-6 bg-gray-50 dark:bg-slate-900 min-h-screen">
      {/* Breadcrumb */}
      <div className="flex items-center text-sm text-gray-500 dark:text-gray-400 mb-4">
        <svg className="h-4 w-4 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
          <path d="M3 12h18M3 6h18M3 18h18" />
        </svg>
        <span className="mr-2">0002 Mechanical</span>
        <span className="mx-2">&gt;</span>
        <span className="text-gray-900 dark:text-gray-100 font-medium">Requisitions</span>
      </div>

      {/* Page Header */}
      <div className="flex justify-between items-start mb-6">
        <div>
          <h1 className="text-2xl font-semibold text-gray-900 dark:text-gray-100">
            Requisitions
          </h1>
        </div>
        <Button className="bg-blue-500 text-white hover:bg-blue-600 transition-colors duration-200 shadow hover:shadow-md text-sm rounded-md">
          <span className="mr-1">+</span> Create New
        </Button>
      </div>

      {/* Search Bar */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-3 mb-4">
        <div className="flex items-center justify-between">
          <div className="relative flex-1">
            <MagnifyingGlassIcon className="h-4 w-4 absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400" />
            <Input
              type="text"
              placeholder="Search ID, Year, Subject, Reference..."
              className="pl-10 w-full border border-gray-200 h-9 text-sm"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div className="flex items-center space-x-2 ml-4">
            <Button variant="outline" className="flex items-center gap-1 h-9 text-sm border-gray-200">
              <FunnelIcon className="h-4 w-4" />
              <span>Filter</span>
            </Button>
            <Button variant="outline" className="flex items-center gap-1 h-9 text-sm border-gray-200">
              <span>Sort by</span>
              <ChevronDownIcon className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* Data Table */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 dark:bg-slate-700 border-b border-gray-200">
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('id')}
                  >
                    Project ID
                    <ChevronDownIcon className="h-3 w-3 ml-1" />
                  </button>
                </TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('year')}
                  >
                    Year
                    <ChevronDownIcon className="h-3 w-3 ml-1" />
                  </button>
                </TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">Case No.</TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">Reference</TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('subject')}
                  >
                    Subject
                    <ChevronDownIcon className="h-3 w-3 ml-1" />
                  </button>
                </TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">
                  <button 
                    className="flex items-center gap-1"
                    onClick={() => handleSort('status')}
                  >
                    Status
                    <ChevronDownIcon className="h-3 w-3 ml-1" />
                  </button>
                </TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">Date created</TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">Budget code</TableHead>
                <TableHead className="font-medium text-xs text-gray-600 dark:text-gray-300 py-2 px-3">Budget category</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRequisitions.length > 0 ? (
                filteredRequisitions.map((requisition, index) => (
                  <TableRow 
                    key={requisition.id} 
                    className={`border-b border-gray-100 hover:bg-gray-50 dark:hover:bg-slate-700/50 cursor-pointer ${index % 2 === 0 ? 'bg-white dark:bg-slate-800' : 'bg-gray-50 dark:bg-slate-700/20'}`}
                    onClick={() => router.push(`/requisitions/${requisition.id}`)}
                  >
                    <TableCell className="py-2 px-3 text-sm">
                      {requisition.id}
                    </TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.year}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.caseNo}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.reference}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.subject}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">
                      {requisition.status}
                    </TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.dateCreated}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.budgetCode}</TableCell>
                    <TableCell className="py-2 px-3 text-sm">{requisition.budgetCategory}</TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={9} className="text-center py-8 text-gray-500 dark:text-gray-400">
                    <div className="flex flex-col items-center justify-center space-y-2">
                      <DocumentTextIcon className="h-12 w-12 text-gray-300 dark:text-gray-600" />
                      <p>No pending requisitions found</p>
                      <p className="text-sm">Try adjusting your search or filter criteria</p>
                    </div>
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <div className="px-6 py-4 border-t border-gray-200 dark:border-slate-700 flex flex-col sm:flex-row items-center justify-between gap-4">
          <div className="text-sm text-gray-500 dark:text-gray-400">
            Showing 1 to 17 of 17 entries
          </div>

          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <button
                className="px-3 py-1 border border-gray-200 dark:border-slate-600 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={true}
              >
                &lt;
              </button>
              <span className="px-3 py-1 bg-blue-500 text-white rounded-md text-sm">1</span>
              <button
                className="px-3 py-1 border border-gray-200 dark:border-slate-600 rounded-md text-sm disabled:opacity-50 disabled:cursor-not-allowed"
                disabled={true}
              >
                &gt;
              </button>
            </div>

            <div className="text-sm text-gray-500 dark:text-gray-400">
              <span className="font-medium">17 rows</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}


export default function PendingRequisitionsPage() {
  return (
    <Suspense fallback={
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-500"></div>
      </div>
    }>
      <PendingRequisitionsContent />
    </Suspense>
  );
}
