'use client';

import React from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/context/AuthContext';
import SupplierUserDashboard from '@/components/dashboard/SupplierUserDashboard';
import { useGlobalPreloader } from '@/hooks/useGlobalPreloader';

export default function SupplierUserDashboardPage() {
  const { user, isAuthenticated, logout } = useAuth();
  const router = useRouter();
  const { showPreloader, hidePreloader } = useGlobalPreloader();
  const [accessError, setAccessError] = React.useState<string | null>(null);

  React.useEffect(() => {
    if (isAuthenticated === false) {
      router.push('/login');
      return;
    }

    if (isAuthenticated === true && user) {
      // Hide preloader once authenticated
      hidePreloader();
      // Check if user has the correct role for supplier user dashboard
      if (!['supplier_user', 's_user', 'supplier'].includes(user.role)) {
        setAccessError(`Access denied. This page is only available to supplier users. Your current role is: ${user.role}`);
        return;
      }
      // Role is correct, clear any error
      setAccessError(null);
    } else {
      // Show beautiful preloader while checking authentication
      showPreloader('Checking authentication...', 'elegant');
    }
  }, [isAuthenticated, user, router, showPreloader, hidePreloader]);

  // Show loading while checking authentication
  if (!isAuthenticated) {
    // The beautiful preloader is shown via the global preloader system
    return null;
  }

  // Show access error if user doesn't have the right role
  if (accessError) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="max-w-md w-full bg-white dark:bg-gray-800 shadow-lg rounded-lg p-8 text-center">
          <div className="mb-6">
            <div className="mx-auto flex items-center justify-center h-16 w-16 rounded-full bg-red-100 dark:bg-red-900/30">
              <svg className="h-8 w-8 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
              </svg>
            </div>
          </div>
          
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Access Denied
          </h2>
          
          <p className="mb-4">{accessError}</p>
          
          <p className="mb-6">Please contact your administrator if you believe you should have access to this page.</p>
          
          <div className="flex space-x-4">
            <button 
              onClick={() => router.push('/dashboard')} 
              className="px-4 py-2 bg-gray-200 dark:bg-gray-700 text-gray-800 dark:text-white rounded hover:bg-gray-300 dark:hover:bg-gray-600 transition-colors"
            >
              Go to Dashboard
            </button>
            <button 
              onClick={() => logout()} 
              className="px-4 py-2 bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-100 rounded hover:bg-red-200 dark:hover:bg-red-800 transition-colors"
            >
              Sign Out
            </button>
          </div>
        </div>
      </div>
    );
  }

  // If we get here, user has the correct role and is not null
  return user ? (
    <SupplierUserDashboard user={user} />
  ) : null;
}
