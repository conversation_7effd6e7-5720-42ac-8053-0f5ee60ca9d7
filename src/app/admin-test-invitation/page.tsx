'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useModal } from '@/hooks/useModal';

export default function TestInvitationPage() {
  const [email, setEmail] = useState('<EMAIL>');
  const { showSuccess, showError, showInfo, ModalComponent } = useModal();
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);
  const [authStatus, setAuthStatus] = useState<any>(null);

  // Check auth status on component mount
  React.useEffect(() => {
    const token = localStorage.getItem('access_token');
    const userData = localStorage.getItem('user');

    setAuthStatus({
      hasToken: !!token,
      tokenLength: token?.length || 0,
      hasUserData: !!userData,
      userData: userData ? JSON.parse(userData) : null
    });
  }, []);

  const testInvitation = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      // Get token from localStorage
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No access token found. Please log in first.');
      }

      // Get user data for organization_id
      const userData = JSON.parse(localStorage.getItem('user') || '{}');
      const orgId = userData?.organization_id || userData?.organisation_id;

      console.log('Testing invitation with:', {
        email,
        orgId,
        token: token.substring(0, 10) + '...',
        tokenLength: token.length,
        userData: userData
      });

      // First, let's test if the token works by calling /users/me
      console.log('Testing token with /users/me...');
      const userTestResponse = await fetch('/api/proxy/auth/users/me', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        }
      });

      if (!userTestResponse.ok) {
        const userError = await userTestResponse.text();
        throw new Error(`Token validation failed: ${userTestResponse.status} - ${userError}`);
      }

      const currentUser = await userTestResponse.json();
      console.log('✅ Token is valid, current user:', currentUser);

      // Check user role and permissions
      if (!currentUser.role) {
        console.warn('⚠️ User has no role assigned');
      } else {
        console.log('User role:', currentUser.role);

        // Check if user role allows sending invitations
        const allowedRoles = ['c_admin', 'admin', 'super_admin'];
        if (!allowedRoles.includes(currentUser.role)) {
          console.warn('⚠️ User role may not have permission to send invitations:', currentUser.role);
        }
      }

      // Update orgId from fresh user data if needed
      const freshOrgId = currentUser.organization_id || currentUser.organisation_id;
      if (freshOrgId && freshOrgId !== orgId) {
        console.log('Using fresh organization ID from API:', freshOrgId);
      }

      const finalOrgId = freshOrgId || orgId;
      console.log('Sending invitation with organization_id:', finalOrgId);

      if (!finalOrgId) {
        throw new Error('No organization_id found. This is required for sending invitations.');
      }

      const response = await fetch('/api/proxy/invitations/bulk', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify({
          emails: [email],
          role: 'supplier',
          message: 'Test invitation from test page',
          organization_id: finalOrgId
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        setResult(data);
        console.log('✅ Invitation test successful:', data);
      } else {
        throw new Error(`API Error: ${response.status} - ${JSON.stringify(data)}`);
      }
    } catch (err: any) {
      console.error('❌ Invitation test failed:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="container mx-auto py-8 px-4 max-w-2xl">
      <Card>
        <CardHeader>
          <CardTitle>Test Invitation API</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Auth Status */}
          {authStatus && (
            <div className={`p-3 rounded-md border ${authStatus.hasToken ? 'bg-green-50 border-green-200' : 'bg-red-50 border-red-200'}`}>
              <h4 className="font-medium text-sm">Authentication Status:</h4>
              <div className="text-xs mt-1 space-y-1">
                <p>Token: {authStatus.hasToken ? '✅ Present' : '❌ Missing'} ({authStatus.tokenLength} chars)</p>
                <p>User Data: {authStatus.hasUserData ? '✅ Present' : '❌ Missing'}</p>
                {authStatus.userData && (
                  <p>Org ID: {authStatus.userData.organization_id || authStatus.userData.organisation_id || '❌ Missing'}</p>
                )}
              </div>
            </div>
          )}

          <div>
            <label className="block text-sm font-medium mb-2">Test Email:</label>
            <Input
              type="email"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              placeholder="<EMAIL>"
            />
          </div>

          <Button 
            onClick={testInvitation} 
            disabled={loading || !email}
            className="w-full"
          >
            {loading ? 'Sending Test Invitation...' : 'Send Test Invitation'}
          </Button>

          {error && (
            <div className="p-4 bg-red-50 border border-red-200 rounded-md">
              <h3 className="font-medium text-red-800">Error:</h3>
              <p className="text-red-700 text-sm mt-1">{error}</p>
            </div>
          )}

          {result && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-md">
              <h3 className="font-medium text-green-800">Success!</h3>
              <div className="text-green-700 text-sm mt-2">
                <p><strong>Successful:</strong> {result.successful}</p>
                <p><strong>Failed:</strong> {result.failed}</p>
                <p><strong>Total:</strong> {result.total}</p>
                {result.results && result.results.length > 0 && (
                  <div className="mt-2">
                    <strong>Results:</strong>
                    <pre className="mt-1 text-xs bg-white p-2 rounded border overflow-auto">
                      {JSON.stringify(result.results, null, 2)}
                    </pre>
                  </div>
                )}
              </div>
            </div>
          )}

          <div className="space-y-2">
            <Button
              onClick={async () => {
                try {
                  const response = await fetch('/api/test-connectivity');
                  const data = await response.json();
                  console.log('Connectivity test:', data);
                  showInfo('API Connectivity Test', `Status: ${data.status}\nAPI Reachable: ${data.api_reachable}`);
                } catch (e) {
                  console.error('Connectivity test failed:', e);
                  alert('Connectivity test failed');
                }
              }}
              variant="outline"
              size="sm"
            >
              Test API Connectivity
            </Button>

            <Button
              onClick={async () => {
                try {
                  const token = localStorage.getItem('access_token');
                  const userData = JSON.parse(localStorage.getItem('user') || '{}');
                  const orgId = userData?.organization_id || userData?.organisation_id;

                  // Test auth/invite endpoint directly
                  const response = await fetch('https://dev.ascensionservices.net/api/v1/auth/invite', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json',
                      'Authorization': `Bearer ${token}`
                    },
                    body: JSON.stringify({
                      email: '<EMAIL>',
                      role: 'supplier',
                      organization_id: orgId,
                      message: 'Direct test of auth/invite endpoint'
                    })
                  });

                  console.log('Auth invite test:', response.status, response.statusText);
                  const data = await response.text();
                  console.log('Auth invite response:', data);
                  alert(`Auth Invite Test: ${response.status} ${response.statusText}\nCheck console for details`);
                } catch (e) {
                  console.error('Auth invite test failed:', e);
                  alert('Auth invite test failed');
                }
              }}
              variant="outline"
              size="sm"
            >
              Test Auth/Invite Endpoint
            </Button>

            <Button
              onClick={async () => {
                try {
                  // Test SMTP configuration
                  const response = await fetch('/api/test-email-smtp', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ action: 'verify' })
                  });

                  const data = await response.json();
                  console.log('SMTP test:', data);
                  if (data.success) {
                    showSuccess('SMTP Test Successful', `${data.message}\n\nCheck console for configuration details.`);
                  } else {
                    showError('SMTP Test Failed', `${data.message}\n\nCheck console for error details.`);
                  }
                } catch (e) {
                  console.error('SMTP test failed:', e);
                  alert('SMTP test failed');
                }
              }}
              variant="outline"
              size="sm"
              className="bg-blue-600 hover:bg-blue-700 text-white"
            >
              Test SMTP Connection
            </Button>

            <Button
              onClick={async () => {
                const email = prompt('Enter email address to send test email:');
                if (!email) return;

                try {
                  const response = await fetch('/api/test-email-smtp', {
                    method: 'POST',
                    headers: {
                      'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ action: 'send', email })
                  });

                  const data = await response.json();
                  console.log('Test email result:', data);
                  if (data.success) {
                    showSuccess('Test Email Sent!', `${data.message}\n\nCheck your inbox for the test email.`);
                  } else {
                    showError('Test Email Failed', data.message);
                  }
                } catch (e) {
                  console.error('Test email failed:', e);
                  alert('Test email failed');
                }
              }}
              variant="outline"
              size="sm"
              className="bg-green-600 hover:bg-green-700 text-white"
            >
              📧 Send Test Email
            </Button>
          </div>

          <div className="text-sm text-gray-600">
            <p><strong>Note:</strong> This page tests the invitation API directly.</p>
            <p>Make sure you&apos;re logged in first to get a valid access token.</p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
