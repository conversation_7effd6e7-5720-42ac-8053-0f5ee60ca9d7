import { NextRequest, NextResponse } from 'next/server';
import { sendTestEmail, verifyEmailConnection } from '@/lib/emailService';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, action } = body;

    if (action === 'verify') {
      // Test SMTP connection
      console.log('🔍 Testing SMTP connection...');
      const isConnected = await verifyEmailConnection();
      
      return NextResponse.json({
        success: isConnected,
        message: isConnected ? 'SMTP connection verified successfully' : 'SMTP connection failed',
        config: {
          host: process.env.SMTP_HOST,
          port: process.env.SMTP_PORT,
          secure: process.env.SMTP_SECURE,
          user: process.env.SMTP_USER ? `${process.env.SMTP_USER.substring(0, 3)}***` : 'not set',
          from: process.env.SMTP_FROM,
          fromName: process.env.SMTP_FROM_NAME
        }
      });
    }

    if (action === 'send' && email) {
      // Send test email
      console.log(`📧 Sending test email to ${email}...`);
      const result = await sendTestEmail(email);
      
      return NextResponse.json({
        success: result.success,
        message: result.success ? 
          `Test email sent successfully to ${email}` : 
          `Failed to send test email: ${result.error}`,
        messageId: result.messageId,
        error: result.error
      });
    }

    return NextResponse.json(
      { error: 'Invalid action or missing email' },
      { status: 400 }
    );

  } catch (error: any) {
    console.error('Email test API error:', error);
    return NextResponse.json(
      { 
        success: false,
        error: 'Email test failed',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

export async function GET() {
  try {
    // Return SMTP configuration status
    const config = {
      host: process.env.SMTP_HOST || 'not set',
      port: process.env.SMTP_PORT || 'not set',
      secure: process.env.SMTP_SECURE || 'not set',
      user: process.env.SMTP_USER ? `${process.env.SMTP_USER.substring(0, 3)}***` : 'not set',
      from: process.env.SMTP_FROM || 'not set',
      fromName: process.env.SMTP_FROM_NAME || 'not set'
    };

    const isConfigured = !!(
      process.env.SMTP_HOST && 
      process.env.SMTP_USER && 
      process.env.SMTP_PASS
    );

    return NextResponse.json({
      configured: isConfigured,
      config: config,
      message: isConfigured ? 
        'SMTP is configured' : 
        'SMTP configuration incomplete - missing required settings'
    });

  } catch (error: any) {
    return NextResponse.json(
      { error: 'Failed to check SMTP configuration', details: error.message },
      { status: 500 }
    );
  }
}
