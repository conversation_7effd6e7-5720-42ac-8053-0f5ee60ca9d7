import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, invitationUrl, invitedBy, message } = body;

    if (!email || !invitationUrl) {
      return NextResponse.json(
        { error: 'Email and invitation URL are required' },
        { status: 400 }
      );
    }

    // Create email content
    const emailSubject = 'Invitation to Join Ascension Supplier Network';
    const emailBody = `
Hello,

${invitedBy || 'Someone'} has invited you to join the Ascension supplier network.

${message || 'You have been invited to join our supplier network.'}

To accept this invitation and create your account, please click the link below:

${invitationUrl}

This invitation will expire in 7 days.

If you have any questions, please contact us.

Best regards,
Ascension Team

---
This is an automated message. Please do not reply to this email.
    `.trim();

    // Create mailto link
    const mailtoLink = `mailto:${email}?subject=${encodeURIComponent(emailSubject)}&body=${encodeURIComponent(emailBody)}`;

    // Return email content and mailto link
    return NextResponse.json({
      success: true,
      email: email,
      subject: emailSubject,
      body: emailBody,
      mailtoLink: mailtoLink,
      message: 'Email content generated successfully. Use the mailto link or copy the content to send manually.'
    });

  } catch (error: any) {
    console.error('Email generation error:', error);
    return NextResponse.json(
      { error: 'Failed to generate email content', details: error.message },
      { status: 500 }
    );
  }
}
