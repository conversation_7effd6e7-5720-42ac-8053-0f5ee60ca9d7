import { NextResponse } from 'next/server';

export async function GET() {
  try {
    // Check if we can reach the backend API
    const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.ascensionservices.net/api/v1';
    
    // Actually try to connect to the backend API
    console.log('Testing connection to backend API:', apiUrl);
    
    try {
      // Make a simple request to the API
      const response = await fetch(`${apiUrl}/health-check`, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        },
        // Set a reasonable timeout
        signal: AbortSignal.timeout(5000)
      });
      
      if (response.ok) {
        return NextResponse.json(
          { 
            status: 'ok',
            message: 'Successfully connected to backend API',
            apiUrl,
            timestamp: new Date().toISOString()
          },
          { 
            status: 200,
            headers: {
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            },
          }
        );
      } else {
        return NextResponse.json(
          { 
            status: 'warning',
            message: `Backend API returned status ${response.status}`,
            apiUrl,
            timestamp: new Date().toISOString()
          },
          { 
            status: 200,
            headers: {
              'Access-Control-Allow-Origin': '*',
              'Access-Control-Allow-Methods': 'GET, OPTIONS',
              'Access-Control-Allow-Headers': 'Content-Type, Authorization',
            },
          }
        );
      }
    } catch (apiError: any) {
      console.error('Failed to connect to backend API:', apiError);
      
      // Return a partial success - the proxy is working but backend connection failed
      return NextResponse.json(
        { 
          status: 'warning',
          message: 'API proxy is healthy but backend connection failed',
          error: apiError.message,
          apiUrl,
          timestamp: new Date().toISOString()
        },
        { 
          status: 200,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          },
        }
      );
    }
  } catch (error) {
    console.error('Health check error:', error);
    return NextResponse.json(
      { error: 'Health check failed' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'GET, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
