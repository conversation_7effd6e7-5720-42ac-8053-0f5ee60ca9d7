import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Log headers for debugging
    const headers = Object.fromEntries(request.headers.entries());
    console.log('Request headers:', headers);
    
    // Get the request body
    const body = await request.json();
    console.log('Original request body:', body);
    
    // Get authorization header
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      console.error('Missing Authorization header');
      return NextResponse.json(
        { error: 'Missing Authorization header' },
        { 
          status: 401,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          },
        }
      );
    }
    
    // Extract token from the header (removing "Bearer " prefix if present)
    const token = authHeader.startsWith('Bearer ') ? 
      authHeader.substring(7) : authHeader;
    
    console.log('Token length:', token?.length);
    
    // Get all emails from the request
    const emails = Array.isArray(body.emails) ? body.emails : [body.emails].filter(Boolean);

    if (emails.length === 0) {
      return NextResponse.json(
        { error: 'No emails provided' },
        { status: 400 }
      );
    }

    // Get all needed fields from the request body
    const organization_id = body.organization_id;
    const message = body.message || 'You have been invited to join our supplier network.';
    const expires_in_days = body.expires_in_days || 7;

    console.log('Processing invitations for emails:', emails);
    console.log('Organization ID:', organization_id);

    // Test the token by calling /users/me first
    console.log('Testing token with /users/me...');
    try {
      const userTestResponse = await fetch('https://dev.ascensionservices.net/api/v1/auth/users/me', {
        headers: {
          'Accept': 'application/json',
          'Authorization': authHeader
        }
      });

      if (userTestResponse.ok) {
        const userData = await userTestResponse.json();
        console.log('✅ Token is valid, user role:', userData.role);
        console.log('User organization:', userData.organization_id || userData.organisation_id);
      } else {
        console.error('❌ Token validation failed:', userTestResponse.status, userTestResponse.statusText);
      }
    } catch (e) {
      console.error('❌ Error testing token:', e);
    }
    
    // Try the /auth/invite endpoint which might have different permissions
    const authInviteUrl = 'https://dev.ascensionservices.net/api/v1/auth/invite';
    const regularInviteUrl = 'https://dev.ascensionservices.net/api/v1/invitations';
    
    console.log('Trying multiple invitation endpoints...');

    // Send individual invitations and collect results
    const results = [];
    let successful = 0;
    let failed = 0;

    for (const email of emails) {
      try {
        // Create payload for invitation according to API docs
        const invitePayload: any = {
          email: email,
          role: 'supplier',
          message: message
        };

        // Add organization_id if provided (required for auth/invite)
        if (organization_id) {
          invitePayload.organization_id = organization_id;
        }

        console.log(`Sending invitation to ${email}:`, invitePayload);

        let invitationSent = false;
        let lastError = null;

        // Method 1: Try /auth/invite endpoint (might have different permissions)
        console.log(`Method 1: Trying /auth/invite for ${email}...`);
        try {
          const authResponse = await fetch(authInviteUrl, {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Accept': 'application/json',
              'Authorization': authHeader
            },
            body: JSON.stringify(invitePayload),
            signal: AbortSignal.timeout(30000)
          });

          console.log(`Auth invite response for ${email}:`, authResponse.status, authResponse.statusText);

          if (authResponse.ok) {
            const responseData = await authResponse.json();
            console.log(`✅ Auth invite sent successfully to ${email}`);
            results.push({
              email: email,
              success: true,
              data: responseData,
              method: 'auth/invite'
            });
            successful++;
            invitationSent = true;
          } else {
            const errorText = await authResponse.text();
            lastError = `Auth invite failed: ${authResponse.status} ${authResponse.statusText} - ${errorText}`;
            console.log(`Auth invite failed for ${email}:`, lastError);
          }
        } catch (authError: any) {
          lastError = `Auth invite error: ${authError.message}`;
          console.log(`Auth invite error for ${email}:`, authError.message);
        }

        // Method 2: Try regular /invitations endpoint if auth/invite failed
        if (!invitationSent) {
          console.log(`Method 2: Trying /invitations for ${email}...`);
          try {

            const response = await fetch(regularInviteUrl, {
              method: 'POST',
              headers: {
                'Content-Type': 'application/json',
                'Accept': 'application/json',
                'Authorization': authHeader
              },
              body: JSON.stringify(invitePayload),
              signal: AbortSignal.timeout(30000)
            });

            console.log(`Regular invite response for ${email}:`, response.status, response.statusText);

            if (response.ok) {
              const responseData = await response.json();
              console.log(`✅ Regular invite sent successfully to ${email}`);
              results.push({
                email: email,
                success: true,
                data: responseData,
                method: 'regular'
              });
              successful++;
              invitationSent = true;
            } else {
              const errorText = await response.text();
              lastError = `Regular invite failed: ${response.status} ${response.statusText} - ${errorText}`;
              console.log(`Regular invite failed for ${email}:`, lastError);
            }
          } catch (regularError: any) {
            lastError = `Regular invite error: ${regularError.message}`;
            console.log(`Regular invite error for ${email}:`, regularError.message);
          }
        }

        // If both methods failed, record the failure
        if (!invitationSent) {

          console.error(`❌ All invitation methods failed for ${email}`);
          results.push({
            email: email,
            success: false,
            error: 'All invitation methods failed',
            details: lastError || 'Unknown error'
          });
          failed++;
        }

      } catch (error: any) {
        console.error(`❌ Network error sending invitation to ${email}:`, error);
        console.error('Error details:', {
          name: error.name,
          message: error.message,
          cause: error.cause,
          stack: error.stack
        });

        // Check if it's a timeout error
        let errorType = 'Network error';
        if (error.name === 'AbortError') {
          errorType = 'Request timeout';
        } else if (error.message?.includes('fetch')) {
          errorType = 'Connection failed';
        }

        results.push({
          email: email,
          success: false,
          error: errorType,
          details: error.message
        });
        failed++;
      }
    }

    // Create summary response
    const summary = {
      successful: successful,
      failed: failed,
      total: emails.length,
      results: results
    };

    console.log('Invitation summary:', summary);

    // Return the response with proper CORS headers
    return NextResponse.json(summary, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error: any) {
    // Enhanced error logging
    console.error('Proxy error details:', {
      name: error.name,
      message: error.message,
      stack: error.stack,
      cause: error.cause,
      response: error.response ? {
        status: error.response.status,
        statusText: error.response.statusText,
        data: error.response.data
      } : 'No response data'
    });
    
    return NextResponse.json(
      { 
        error: 'Proxy request failed', 
        message: error.message || 'Unknown error',
        details: error.response?.data || {},
        name: error.name || 'Unknown error type'
      },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
