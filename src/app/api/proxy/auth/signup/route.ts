import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();
    console.log('Signup request received:', { 
      hasOrgIn: !!body.org_in, 
      hasAdminIn: !!body.admin_in,
      hasSimpleFormat: !!(body.organization_name || body.email)
    });
    
    if (body.org_in && body.admin_in) {
      console.log('Using existing org_in/admin_in format');
      var signupData = body;
    } else {
      // Transform from simple format to the expected API format
      console.log('Transforming from simple format to org_in/admin_in format');
      const today = new Date().toISOString().split('T')[0];
      
      signupData = {
        org_in: {
          name: body.organization_name,
          type: 'company', // Default to company
          address: body.address || 'Not provided',
          telephone: body.phone_number,
          contact_person: body.contact_name,
          status: 'active',
          country: body.country || 'Uganda',
          incorporation_date: today
        },
        admin_in: {
          email: body.email,
          password: body.password,
          first_name: body.contact_name?.split(' ')[0] || 'User',
          last_name: body.contact_name?.split(' ').slice(1).join(' ') || 'Name',
          telephone_number: body.phone_number,
          role_names: ['c_admin']
        }
      };
    }
    
    console.log('Sending signup data to backend:', JSON.stringify(signupData, null, 2));
    
    // Forward the request to the actual API
    const response = await fetch('https://dev.ascensionservices.net/api/v1/auth/signup', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify(signupData),
    });

    // Get the response data
    const data = await response.json();
    
    // Return the response with proper CORS headers
    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error) {
    console.error('Proxy error:', error);
    return NextResponse.json(
      { error: 'Proxy request failed' },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
