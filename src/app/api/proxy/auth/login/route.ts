import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    // Get the request body
    const body = await request.json();
    console.log('Login request received:', { email: body.email, hasPassword: !!body.password });
    
    // Convert to x-www-form-urlencoded format as required by the API
    const formData = new URLSearchParams();
    formData.append('grant_type', 'password');
    formData.append('username', body.email); // API expects 'username' but we use 'email' in our frontend
    formData.append('password', body.password);
    formData.append('scope', '');
    // Use the actual client credentials for the Ascension API
    formData.append('client_id', 'ascension-portal-client');
    formData.append('client_secret', 'ascension-portal-secret');
    
    const formBody = formData.toString();
    console.log('Sending form data to API:', formBody);
    
    // Forward the request to the actual API
    const response = await fetch('https://dev.ascensionservices.net/api/v1/auth/login', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json',
      },
      body: formBody,
    });

    // Check if the response is ok
    if (!response.ok) {
      const errorText = await response.text();
      console.error('API login error:', { status: response.status, statusText: response.statusText, body: errorText });
      return NextResponse.json(
        { error: `Login failed with status ${response.status}: ${response.statusText}` },
        { 
          status: response.status,
          headers: {
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'POST, OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type, Authorization',
          },
        }
      );
    }

    // Get the response data
    const data = await response.json();
    console.log('Login successful, received token:', { hasToken: !!data.access_token });
    
    // Return the response with proper CORS headers
    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });
  } catch (error: any) {
    console.error('Proxy error:', error);
    return NextResponse.json(
      { 
        error: 'Proxy request failed', 
        message: error.message || 'Unknown error',
        details: error.response?.data || {}
      },
      { 
        status: 500,
        headers: {
          'Access-Control-Allow-Origin': '*',
          'Access-Control-Allow-Methods': 'POST, OPTIONS',
          'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        },
      }
    );
  }
}

export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
