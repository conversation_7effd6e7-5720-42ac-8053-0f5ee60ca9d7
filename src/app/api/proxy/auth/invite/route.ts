import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('🔄 Invite API proxy called');

    const body = await request.json();
    const { email, role_name } = body;

    console.log('📧 Invite request:', { email, role_name });

    if (!email || !role_name) {
      console.log('❌ Missing required fields');
      return NextResponse.json(
        { detail: 'Email and role_name are required' },
        { status: 400 }
      );
    }

    // Get the authorization header from the request
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      console.log('❌ Missing authorization header');
      return NextResponse.json(
        { detail: 'Authorization header is required' },
        { status: 401 }
      );
    }

    console.log('🔑 Auth header present:', authHeader.substring(0, 20) + '...');

    // Get the API base URL
    const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.ascensionservices.net/api/v1';
    
    console.log('Sending invite request to:', `${apiUrl}/auth/invite`);
    console.log('Invite payload:', { email, role_name });

    // Forward the request to the backend API
    const response = await fetch(`${apiUrl}/auth/invite`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': authHeader,
      },
      body: JSON.stringify({
        email,
        role_name
      }),
    });

    const data = await response.json();

    console.log('📡 Backend response status:', response.status);
    console.log('📡 Backend response data:', data);

    if (!response.ok) {
      console.log('❌ Backend returned error:', response.status, data);
      return NextResponse.json(
        { detail: data.detail || data.message || 'Failed to send invitation' },
        { status: response.status }
      );
    }

    console.log('✅ Invitation sent successfully');
    return NextResponse.json(data, {
      status: response.status,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error) {
    console.error('Error in invite proxy:', error);
    return NextResponse.json(
      { detail: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function OPTIONS() {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}
