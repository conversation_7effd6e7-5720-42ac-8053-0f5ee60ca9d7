import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { token, password } = body;
    
    if (!token || !password) {
      return NextResponse.json(
        { detail: 'Token and password are required' },
        { status: 400 }
      );
    }

    // Forward the request to the backend API
    const apiUrl = process.env.NEXT_PUBLIC_API_BASE_URL || 'https://dev.ascensionservices.net/api/v1';
    
    const response = await fetch(`${apiUrl}/auth/reset-password`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      body: JSON.stringify({ token, password }),
    });

    const data = await response.json();

    if (!response.ok) {
      return NextResponse.json(
        { detail: data.detail || 'Failed to reset password' },
        { status: response.status }
      );
    }

    return NextResponse.json(
      { message: data.message || 'Password reset successful', success: true },
      { status: 200 }
    );
  } catch (error) {
    console.error('Error in reset-password route:', error);
    return NextResponse.json(
      { detail: 'Internal server error' },
      { status: 500 }
    );
  }
}
