import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get the authorization header
    const authHeader = request.headers.get('Authorization');
    
    return NextResponse.json({
      message: 'Debug information',
      tokenPresent: !!authHeader,
      tokenDetails: authHeader ? {
        length: authHeader.length,
        firstChars: authHeader.substring(0, 10) + '...',
        lastChars: '...' + authHeader.substring(authHeader.length - 10),
        isBearerToken: authHeader.startsWith('Bearer '),
      } : null
    });
  } catch (error: any) {
    console.error('Debug token error:', error);
    return NextResponse.json({ error: 'Error debugging token', details: error.message }, { status: 500 });
  }
}
