import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    console.log('Testing API connectivity...');

    // Test basic connectivity to the API
    const testUrl = 'https://dev.ascensionservices.net/api/v1/health';

    // Also test the invitation endpoint
    const invitationUrl = 'https://dev.ascensionservices.net/api/v1/invitations';
    
    try {
      const response = await fetch(testUrl, {
        method: 'GET',
        headers: {
          'Accept': 'application/json'
        },
        signal: AbortSignal.timeout(10000) // 10 second timeout
      });
      
      console.log('Health check response:', response.status, response.statusText);

      // Test invitation endpoint accessibility (without auth)
      let invitationStatus = 'unknown';
      try {
        const invResponse = await fetch(invitationUrl, {
          method: 'GET', // Just test if endpoint exists
          signal: AbortSignal.timeout(5000)
        });
        invitationStatus = `${invResponse.status} ${invResponse.statusText}`;
        console.log('Invitation endpoint test:', invitationStatus);
      } catch (invError: any) {
        invitationStatus = `Error: ${invError.message}`;
        console.log('Invitation endpoint error:', invError.message);
      }

      return NextResponse.json({
        status: 'success',
        api_reachable: true,
        health_status: response.status,
        health_text: response.statusText,
        invitation_endpoint: invitationStatus
      });
      
    } catch (healthError: any) {
      console.error('Health check failed:', healthError);
      
      // Try a different endpoint that we know exists
      try {
        const altResponse = await fetch('https://dev.ascensionservices.net/docs', {
          method: 'GET',
          signal: AbortSignal.timeout(10000)
        });
        
        console.log('Docs endpoint response:', altResponse.status);
        
        return NextResponse.json({
          status: 'partial',
          api_reachable: true,
          health_status: 'unknown',
          docs_status: altResponse.status,
          health_error: healthError.message
        });
        
      } catch (docsError: any) {
        console.error('Complete connectivity failure:', docsError);
        
        return NextResponse.json({
          status: 'failed',
          api_reachable: false,
          error: docsError.message,
          health_error: healthError.message
        });
      }
    }
    
  } catch (error: any) {
    console.error('Connectivity test error:', error);
    return NextResponse.json({
      status: 'error',
      error: error.message
    }, { status: 500 });
  }
}
