import { NextRequest, NextResponse } from 'next/server';
import { sendInvitationEmail, verifyEmailConnection } from '@/lib/emailService';

// Simple in-memory storage for invitations (in production, use a database)
const invitations: any[] = [];

export async function POST(request: NextRequest) {
  try {
    console.log('Custom invitation system - processing request...');
    
    // Get authorization header
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json(
        { error: 'Missing Authorization header' },
        { status: 401 }
      );
    }

    // Get the request body
    const body = await request.json();
    console.log('Custom invitation request body:', body);

    // Validate required fields
    const emails = Array.isArray(body.emails) ? body.emails : [body.emails].filter(Boolean);
    if (emails.length === 0) {
      return NextResponse.json(
        { error: 'No emails provided' },
        { status: 400 }
      );
    }

    // Get user info to validate they can send invitations
    const token = authHeader.startsWith('Bearer ') ? authHeader.substring(7) : authHeader;
    
    // Verify token by calling /users/me
    let currentUser;
    try {
      const userResponse = await fetch('https://dev.ascensionservices.net/api/v1/auth/users/me', {
        headers: {
          'Authorization': authHeader,
          'Accept': 'application/json'
        }
      });
      
      if (!userResponse.ok) {
        throw new Error(`User verification failed: ${userResponse.status}`);
      }
      
      currentUser = await userResponse.json();
      console.log('Current user verified:', currentUser.email, 'Role:', currentUser.role);
    } catch (error) {
      console.error('User verification error:', error);
      return NextResponse.json(
        { error: 'Invalid authentication token' },
        { status: 401 }
      );
    }

    // Process each email
    const results = [];
    let successful = 0;
    let failed = 0;

    for (const email of emails) {
      try {
        // Create invitation record
        const invitationId = `custom_inv_${Date.now()}_${Math.random().toString(36).substring(2, 15)}`;
        const invitation = {
          id: invitationId,
          email: email,
          role: body.role || 'supplier',
          message: body.message || 'You have been invited to join our supplier network.',
          organization_id: body.organization_id || currentUser.organisation_id || currentUser.organization_id,
          invited_by: currentUser.email,
          invited_by_name: `${currentUser.first_name} ${currentUser.last_name}`,
          status: 'pending',
          created_at: new Date().toISOString(),
          expires_at: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
          invitation_url: `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001'}/accept-invitation/${invitationId}`
        };

        // Store invitation
        invitations.push(invitation);

        // Send email using SMTP service
        let emailSent = false;
        let emailError = null;

        try {
          console.log(`📧 Sending invitation email to ${email} using SMTP...`);

          const emailResult = await sendInvitationEmail({
            to: email,
            invitedBy: invitation.invited_by_name,
            invitationUrl: invitation.invitation_url,
            message: invitation.message,
            organizationName: body.organization_name || 'Ascension',
            learnMoreUrl: process.env.NEXT_PUBLIC_LEARN_MORE_URL || 'https://ascensionservices.net'
          });

          if (emailResult.success) {
            emailSent = true;
            console.log(`✅ SMTP email sent successfully to ${email}`, emailResult.messageId);
          } else {
            emailError = `SMTP email failed: ${emailResult.error}`;
            console.log(`❌ SMTP email failed for ${email}:`, emailError);
          }
        } catch (error: any) {
          emailError = `SMTP email error: ${error.message}`;
          console.log(`❌ SMTP email error for ${email}:`, emailError);
        }

        // Record success (even if email failed, invitation is created)
        results.push({
          email: email,
          success: true,
          invitation_id: invitation.id,
          invitation_url: invitation.invitation_url,
          email_sent: emailSent,
          method: 'custom'
        });
        successful++;

        console.log(`✅ Custom invitation created for ${email} (ID: ${invitation.id})`);

      } catch (error: any) {
        console.error(`❌ Failed to create invitation for ${email}:`, error);
        results.push({
          email: email,
          success: false,
          error: 'Failed to create invitation',
          details: error.message
        });
        failed++;
      }
    }

    // Return summary
    const summary = {
      successful: successful,
      failed: failed,
      total: emails.length,
      results: results,
      message: successful > 0 ? 
        `${successful} custom invitation(s) created successfully. Recipients can use the invitation URLs to join.` :
        'No invitations were created successfully.'
    };

    console.log('Custom invitation summary:', summary);

    return NextResponse.json(summary, {
      status: 200,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
      },
    });

  } catch (error: any) {
    console.error('Custom invitation system error:', error);
    return NextResponse.json(
      { 
        error: 'Custom invitation system failed',
        details: error.message 
      },
      { status: 500 }
    );
  }
}

// Handle OPTIONS for CORS
export async function OPTIONS(request: NextRequest) {
  return new NextResponse(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'POST, OPTIONS',
      'Access-Control-Allow-Headers': 'Content-Type, Authorization',
    },
  });
}

// GET endpoint to list invitations
export async function GET(request: NextRequest) {
  try {
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      return NextResponse.json({ error: 'Missing Authorization header' }, { status: 401 });
    }

    // Return all invitations (in production, filter by user/organization)
    return NextResponse.json({
      invitations: invitations,
      total: invitations.length
    });

  } catch (error: any) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }
}
