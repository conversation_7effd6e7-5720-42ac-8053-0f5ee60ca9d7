import { NextRequest, NextResponse } from 'next/server';

export async function GET(request: NextRequest) {
  try {
    // Get all headers
    const headers = Object.fromEntries(request.headers.entries());
    
    // Get the token from localStorage if available
    const authHeader = request.headers.get('Authorization');
    
    // Test a simple GET request to the API to verify connectivity
    const testResponse = await fetch('https://dev.ascensionservices.net/api/v1/health', {
      headers: {
        'Accept': 'application/json',
        'Authorization': authHeader || ''
      }
    });
    
    // Get status and response
    const status = testResponse.status;
    let responseBody;
    
    try {
      responseBody = await testResponse.json();
    } catch (e) {
      responseBody = await testResponse.text();
    }
    
    return NextResponse.json({
      authHeader: authHeader || 'No auth header',
      token: authHeader ? authHeader.replace('Bearer ', '') : 'No token',
      tokenLength: authHeader ? authHeader.length : 0,
      headers: headers,
      testApiResponse: {
        status,
        body: responseBody
      }
    });
  } catch (error: any) {
    console.error('Debug token error:', error);
    return NextResponse.json({ error: 'Error debugging token', details: error.message || 'Unknown error' }, { status: 500 });
  }
}
