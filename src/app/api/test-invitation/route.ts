import { NextRequest, NextResponse } from 'next/server';
import { supplierInvitationTemplate } from '@/email-templates/supplier-invitation';

// Create a real invitation scenario with set password link
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      email,
      organizationName = 'ABC Supplies Ltd',
      invitedByName = '<PERSON> (Supplier Admin)',
      role = 'supplier_user'
    } = body;

    if (!email) {
      return NextResponse.json(
        { error: 'Email is required' },
        { status: 400 }
      );
    }

    // Generate a unique invitation ID and token
    const invitationId = `inv_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const token = `tok_${Date.now()}_${Math.random().toString(36).substr(2, 16)}`;

    // Calculate expiry date (7 days from now)
    const expiryDate = new Date();
    expiryDate.setDate(expiryDate.getDate() + 7);

    // Create the set password URL with token
    const baseUrl = process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000';
    const setPasswordUrl = `${baseUrl}/set-password?token=${token}&invitation_id=${invitationId}&email=${encodeURIComponent(email)}`;

    // Generate email content using the template
    const emailTemplate = supplierInvitationTemplate({
      invitationId,
      organizationName,
      role,
      expiryDate: expiryDate.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      }),
      invitationUrl: setPasswordUrl,
      setPasswordUrl: setPasswordUrl,
      invitedByName
    });

    // Try to send the email using the existing email API
    try {
      const emailResponse = await fetch(`${request.nextUrl.origin}/api/email/send_email`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          template_slug: 'supplier-invitation-custom',
          to: [email],
          context: {
            subject: emailTemplate.subject,
            html_content: emailTemplate.html,
            text_content: emailTemplate.text
          }
        })
      });

      if (emailResponse.ok) {
        const emailResult = await emailResponse.json();
        console.log('✅ Email sent successfully via API:', emailResult);

        return NextResponse.json({
          success: true,
          message: 'Invitation email sent successfully!',
          data: {
            invitationId,
            token,
            email,
            setPasswordUrl,
            expiryDate: expiryDate.toISOString(),
            emailSent: true,
            emailResult
          }
        });
      } else {
        throw new Error(`Email API failed with status ${emailResponse.status}`);
      }
    } catch (emailError) {
      console.log('Email API failed, providing email content for manual sending:', emailError);

      // If email sending fails, return the email content for manual sending
      return NextResponse.json({
        success: true,
        message: 'Invitation created successfully. Email content provided for manual sending.',
        data: {
          invitationId,
          token,
          email,
          setPasswordUrl,
          expiryDate: expiryDate.toISOString(),
          emailSent: false,
          emailContent: {
            to: email,
            subject: emailTemplate.subject,
            html: emailTemplate.html,
            text: emailTemplate.text,
            mailtoLink: `mailto:${email}?subject=${encodeURIComponent(emailTemplate.subject)}&body=${encodeURIComponent(emailTemplate.text)}`
          }
        }
      });
    }

  } catch (error: any) {
    console.error('Test invitation error:', error);
    return NextResponse.json(
      {
        error: 'Failed to create test invitation',
        details: error.message
      },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Get all request headers for debugging
    const headers = Object.fromEntries(request.headers.entries());
    console.log('Request headers:', headers);

    // Get the token from the query parameter
    const url = new URL(request.url);
    const testEmail = url.searchParams.get('email') || '<EMAIL>';
    
    // Get authentication token from request cookies or headers
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      console.error('Missing Authorization header');
      return NextResponse.json({ 
        error: 'No auth token found. Please ensure you are logged in.' 
      }, { status: 401 });
    }
    
    console.log('Auth token:', authHeader.substring(0, 15) + '...');
    
    // Extract token from Bearer prefix if present
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : authHeader;
    
    console.log('Token details:', {
      length: token.length,
      firstFew: token.substring(0, 10) + '...'
    });
    
    // Construct the API request
    const apiUrl = 'https://dev.ascensionservices.net/api/v1/invitations/bulk';
    
    // Try different payload variations
    // Variation 1: Without organization_id
    const payload = {
      emails: [testEmail],
      role: 'supplier',
      message: 'Test invitation from API debug route',
      expires_in_days: 7
    };
    
    console.log('Testing API with payload:', JSON.stringify(payload));
    
    // Try making the request with a different approach
    console.log('Sending request to:', apiUrl);
    
    // Create headers with explicit authorization
    const requestHeaders = new Headers();
    requestHeaders.append('Content-Type', 'application/json');
    requestHeaders.append('Accept', 'application/json');
    requestHeaders.append('Authorization', `Bearer ${token}`);
    
    // Log what we're sending
    console.log('Request headers:', Object.fromEntries(requestHeaders.entries()));
    
    // Send the request with more careful error handling
    let response;
    try {
      response = await fetch(apiUrl, {
        method: 'POST',
        headers: requestHeaders,
        body: JSON.stringify(payload)
      });
      
      console.log('Raw response received:', response.status, response.statusText);
    } catch (error) {
      const fetchError = error as Error;
      console.error('Fetch operation failed:', fetchError);
      return NextResponse.json({
        error: 'Network request failed',
        details: fetchError.message,
        debug: {
          url: apiUrl,
          token_length: token.length,
          headers_sent: Object.fromEntries(requestHeaders.entries())
        }
      }, { status: 500 });
    }
    
    // Get response details
    const status = response.status;
    const statusText = response.statusText;
    let responseData;
    let responseText;
    
    try {
      // Try to get text first
      responseText = await response.text();
      console.log('Raw response text:', responseText);
      
      // Then try to parse as JSON if possible
      try {
        if (responseText) {
          responseData = JSON.parse(responseText);
        } else {
          responseData = { note: 'Empty response body' };
        }
      } catch (error) {
        const parseError = error as Error;
        console.log('Response is not valid JSON');
        responseData = { 
          rawText: responseText,
          parseError: parseError.message
        };
      }
    } catch (textError) {
      console.error('Error getting response text:', textError);
      responseData = { error: 'Could not read response body' };
    }
    
    return NextResponse.json({
      message: 'API test completed',
      request: {
        url: apiUrl,
        headers: Object.fromEntries(requestHeaders.entries()),
        payload
      },
      response: {
        status,
        statusText,
        data: responseData
      },
      success: status >= 200 && status < 300
    });
  } catch (error: any) {
    console.error('Error testing invitation API:', error);
    return NextResponse.json({ 
      error: 'Error testing invitation API', 
      details: error.message,
      stack: error.stack
    }, { status: 500 });
  }
}
