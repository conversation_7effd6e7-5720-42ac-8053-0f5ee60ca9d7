import { NextRequest, NextResponse } from 'next/server';
import nodemailer from 'nodemailer';
import dbConnect from '@/lib/dbConnect';
import EmailTemplate from '@/models/EmailTemplate';

// In-memory store for sent emails (for testing/debugging)
let sentEmails: any[] = [];

const createTransporter = () => {
  const host = process.env.SMTP_HOST || 'smtp.gmail.com';
  const port = parseInt(process.env.SMTP_PORT || '587');
  const user = process.env.SMTP_USER || '';
  const pass = process.env.SMTP_PASS || '';

  if (!user || !pass) {
    console.warn('SMTP user or password not set. Email sending will be disabled.');
    return null;
  }

  console.log('SMTP Configuration:', { 
    host, 
    port, 
    secure: false, 
    user: '****',
  });
  
  return nodemailer.createTransport({
    host,
    port,
    secure: false, // Use explicit TLS
    requireTLS: true,
    tls: {
      rejectUnauthorized: false
    },
    auth: { user, pass },
  });
};

const replacePlaceholders = (text: string, context: Record<string, any>): string => {
  if (!text) return '';
  let processedText = text;
  for (const [key, value] of Object.entries(context)) {
    const regex = new RegExp(`{{\s*${key}\s*}}`, 'g');
    processedText = processedText.replace(regex, String(value));
  }
  return processedText;
};

export async function POST(request: NextRequest) {
  try {
    const requestBody = await request.json();

    const { template_slug, to, cc, bcc, context } = requestBody;

    if (!template_slug) {
      return NextResponse.json({ error: { code: 'MISSING_FIELD', message: 'template_slug is required' } }, { status: 400 });
    }
    if (!to || !Array.isArray(to) || to.length === 0) {
      return NextResponse.json({ error: { code: 'MISSING_FIELD', message: '`to` field must be a non-empty array' } }, { status: 400 });
    }
    if (!context || typeof context !== 'object') {
      return NextResponse.json({ error: { code: 'MISSING_FIELD', message: '`context` object is required' } }, { status: 400 });
    }

    await dbConnect();

    // Use a more robust approach to handle MongoDB document types
    const templateDoc = await EmailTemplate.findOne({ slug: template_slug }).lean();

    if (!templateDoc) {
      return NextResponse.json({ error: { code: 'TEMPLATE_NOT_FOUND', message: `Template with slug '${template_slug}' not found` } }, { status: 404 });
    }
    
    // Cast the document to any to safely access properties
    const template = templateDoc as any;
    
    if (!template.subject || !template.body) {
      return NextResponse.json({ error: { code: 'INVALID_TEMPLATE', message: 'Template is missing required fields' } }, { status: 400 });
    }

    const processedSubject = replacePlaceholders(template.subject, context);
    let processedBody = replacePlaceholders(template.body, context);

    const isHtml = template.is_html as boolean | undefined;
    
    if (template.signature) {
      const processedSignature = replacePlaceholders(template.signature, context);
      if (isHtml) {
        processedBody += `<br><br><div style="color: #888; font-size: 12px;">${processedSignature.replace(/\n/g, '<br>')}</div>`;
      } else {
        processedBody += `\n\n--\n${processedSignature}`;
      }
    }

    const transporter = createTransporter();
    if (!transporter) {
      console.error('Email sending is disabled. Check SMTP configuration.');
      return NextResponse.json({ error: { code: 'SMTP_DISABLED', message: 'Email sending is not configured on the server.' } }, { status: 503 });
    }

    const fromEmail = process.env.SMTP_FROM || '<EMAIL>';
    const fromName = process.env.SMTP_FROM_NAME || 'Ascension';

    const mailOptions = {
      from: `"${fromName}" <${fromEmail}>`,
      to: to.join(', '),
      cc: cc ? cc.join(', ') : undefined,
      bcc: bcc ? bcc.join(', ') : undefined,
      subject: processedSubject,
      ...(isHtml ? { html: processedBody } : { text: processedBody }),
    };

    const info = await transporter.sendMail(mailOptions);

    console.log('Email sent successfully:', { messageId: info.messageId, recipients: mailOptions.to });

    const sentEmailRecord = {
      id: `email_${Date.now()}`,
      template_slug,
      to,
      cc,
      bcc,
      subject: processedSubject,
      sent_at: new Date().toISOString(),
      status: 'sent',
      provider_response: info.response,
    };
    sentEmails.push(sentEmailRecord);

    return NextResponse.json({ status: 'success', message: 'Email sent successfully', data: sentEmailRecord });

  } catch (error) {
    console.error('Error sending email:', error);
    const errorMessage = error instanceof Error ? error.message : 'An unknown error occurred';
    return NextResponse.json({ status: 'error', error: { code: 'INTERNAL_SERVER_ERROR', message: errorMessage } }, { status: 500 });
  }
}
