import { NextRequest, NextResponse } from 'next/server';
import dbConnect from '@/lib/dbConnect';
import EmailTemplate from '@/models/EmailTemplate';

/**
 * GET handler for fetching a single email template by its slug.
 * @param _req The incoming Next.js request object (unused).
 * @param params An object containing the dynamic route parameters, including the slug.
 * @returns A NextResponse object with the template data or an error message.
 */
export async function GET(_req: NextRequest, { params }: { params: Promise<{ slug: string }> }) {
  await dbConnect();

  try {
    const { slug } = await params;
    const template = await EmailTemplate.findOne({ slug }).lean();

    if (!template) {
      return NextResponse.json({ status: 'error', message: 'Email template not found' }, { status: 404 });
    }

    return NextResponse.json({ status: 'success', data: template });
  } catch (error: any) {
    console.error('Failed to fetch email template:', error);
    return NextResponse.json(
      { status: 'error', message: 'Internal Server Error', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * PUT handler for updating an existing email template by its slug.
 * @param req The incoming Next.js request object.
 * @param params An object containing the dynamic route parameters, including the slug.
 * @returns A NextResponse object with the updated template data or an error message.
 */
export async function PUT(req: NextRequest, { params }: { params: Promise<{ slug: string }> }) {
  await dbConnect();

  try {
    const { slug } = await params;
    const body = await req.json();

    // Ensure the slug in the body does not conflict with the URL slug
    if (body.slug && body.slug !== slug) {
      const existing = await EmailTemplate.findOne({ slug: body.slug });
      if (existing) {
        return NextResponse.json({ status: 'error', message: 'New slug is already in use.' }, { status: 409 });
      }
    }

    const updatedTemplate = await EmailTemplate.findOneAndUpdate({ slug }, body, {
      new: true,
      runValidators: true,
    }).lean();

    if (!updatedTemplate) {
      return NextResponse.json({ status: 'error', message: 'Email template not found' }, { status: 404 });
    }

    return NextResponse.json({ status: 'success', data: updatedTemplate });
  } catch (error: any) {
    console.error('Failed to update email template:', error);
    return NextResponse.json(
      { status: 'error', message: 'Internal Server Error', details: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE handler for deleting an email template by its slug.
 * @param _req The incoming Next.js request object (unused).
 * @param params An object containing the dynamic route parameters, including the slug.
 * @returns A NextResponse object confirming deletion or an error message.
 */
export async function DELETE(_req: NextRequest, { params }: { params: Promise<{ slug: string }> }) {
  await dbConnect();

  try {
    const { slug } = await params;
    const result = await EmailTemplate.deleteOne({ slug });

    if (result.deletedCount === 0) {
      return NextResponse.json({ status: 'error', message: 'Email template not found' }, { status: 404 });
    }

    return new NextResponse(null, { status: 204 }); // No Content
  } catch (error: any) {
    console.error('Failed to delete email template:', error);
    return NextResponse.json(
      { status: 'error', message: 'Internal Server Error', details: error.message },
      { status: 500 }
    );
  }
}
