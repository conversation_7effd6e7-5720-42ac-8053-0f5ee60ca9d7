// Mock database for demonstration - replace with actual database
export const mockVerificationTokens = new Map<string, {
  email: string;
  expiresAt: Date;
  used: boolean;
}>();

// Helper function to generate verification token (for use in signup)
export function generateVerificationToken(email: string): string {
  const token = Math.random().toString(36).substring(2) + Date.now().toString(36);
  const expiresAt = new Date(Date.now() + 24 * 60 * 60 * 1000); // 24 hours from now

  // Store token in mock database
  mockVerificationTokens.set(token, {
    email,
    expiresAt,
    used: false
  });

  return token;
}

// Helper function to send verification email using SMTP directly
export async function sendVerificationEmail(email: string, token: string): Promise<boolean> {
  try {
    const verificationUrl = `${process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000'}/verify-email?token=${token}&email=${encodeURIComponent(email)}`;

    // Create the email HTML content
    const emailHtml = `
      <!DOCTYPE html>
      <html lang="en">
      <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Email</title>
        <style>
          body { font-family: Arial, sans-serif; line-height: 1.6; color: #333; }
          .container { max-width: 600px; margin: 0 auto; padding: 20px; }
          .header { background-color: #18546c; padding: 20px; text-align: center; }
          .header h1 { color: white; margin: 0; }
          .content { padding: 20px; background-color: #f9f9f9; }
          .button { display: inline-block; background-color: #18546c; color: white; text-decoration: none; padding: 10px 20px; border-radius: 5px; }
          .footer { padding: 20px; text-align: center; font-size: 12px; color: #666; }
        </style>
      </head>
      <body>
        <div class="container">
          <div class="header">
            <h1>Verify Your Email</h1>
          </div>
          <div class="content">
            <p>Hello,</p>
            <p>Thank you for signing up! Please verify your email address by clicking the button below:</p>
            <p style="text-align: center;">
              <a href="${verificationUrl}" class="button">Verify Email</a>
            </p>
            <p>Or copy and paste this URL into your browser:</p>
            <p>${verificationUrl}</p>
            <p>This link will expire in 24 hours.</p>
            <p>If you did not sign up for an account, please ignore this email.</p>
          </div>
          <div class="footer">
            <p>This is an automated message, please do not reply to this email.</p>
          </div>
        </div>
      </body>
      </html>
    `;

    // In a real application, you would send an actual email here
    // For this demo, we'll just log the verification URL
    console.log(`Verification URL for ${email}:`, verificationUrl);
    
    return true;
  } catch (error) {
    console.error('Error sending verification email:', error);
    return false;
  }
}
