import { NextRequest, NextResponse } from 'next/server';
import { mockVerificationTokens } from './utils';

export async function POST(request: NextRequest) {
  try {
    const { token } = await request.json();

    if (!token) {
      return NextResponse.json(
        { error: 'Verification token is required' },
        { status: 400 }
      );
    }

    // Check if token exists in mock database
    const tokenData = mockVerificationTokens.get(token);

    if (!tokenData) {
      return NextResponse.json(
        { error: 'INVALID_TOKEN', message: 'Invalid verification token' },
        { status: 400 }
      );
    }

    // Check if token is expired
    if (new Date() > tokenData.expiresAt) {
      return NextResponse.json(
        { error: 'TOKEN_EXPIRED', message: 'Verification token has expired' },
        { status: 400 }
      );
    }

    // Check if token has already been used
    if (tokenData.used) {
      return NextResponse.json(
        { error: 'TOKEN_USED', message: 'Verification token has already been used' },
        { status: 400 }
      );
    }

    // Mark token as used
    tokenData.used = true;
    mockVerificationTokens.set(token, tokenData);

    // In a real application, you would:
    // 1. Update the user's email_verified status in the database
    // 2. Remove the verification token from the database
    // 3. Log the verification event

    console.log(`Email verified for: ${tokenData.email}`);

    return NextResponse.json(
      { 
        success: true, 
        message: 'Email verified successfully',
        email: tokenData.email 
      },
      { status: 200 }
    );

  } catch (error) {
    console.error('Email verification error:', error);
    return NextResponse.json(
      { error: 'INTERNAL_ERROR', message: 'Internal server error' },
      { status: 500 }
    );
  }
}
