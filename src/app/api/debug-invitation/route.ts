import { NextRequest, NextResponse } from 'next/server';

export async function POST(request: NextRequest) {
  try {
    console.log('==== DEBUG INVITATION API =====');
    // Get authentication token from request header
    const authHeader = request.headers.get('Authorization');
    if (!authHeader) {
      console.error('Missing Authorization header');
      return NextResponse.json({ 
        error: 'No auth token found. Please ensure you are logged in.' 
      }, { status: 401 });
    }
    
    // Extract token from Bearer prefix if present
    const token = authHeader.startsWith('Bearer ') 
      ? authHeader.substring(7) 
      : authHeader;
    
    console.log('Token prefix:', token.substring(0, 10) + '...');
    
    // Get the request body
    const body = await request.json();
    
    // Backend API endpoint
    const apiUrl = 'https://dev.ascensionservices.net/api/v1/invitations/bulk';
    
    // First, let's try to make a very simple call to just validate our token is working
    try {
      const simpleResponse = await fetch('https://dev.ascensionservices.net/api/v1/roles', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        }
      });
      console.log('Role API status:', simpleResponse.status);
      const rolesText = await simpleResponse.text();
      console.log('Roles API response:', rolesText.substring(0, 100) + (rolesText.length > 100 ? '...' : ''));
    } catch (e: any) {
      console.error('Error testing roles API:', e);
    }
    
    // Try 3 different formats to see which one works
    
    // 1. JSON format
    console.log('\n==== ATTEMPT 1: JSON FORMAT ====');
    let jsonResult;
    try {
      // Create a simplified test payload
      const jsonPayload: any = {
        emails: ['<EMAIL>'],
        role: 'supplier',
        message: 'Test invitation',
        expires_in_days: 7
      };

      // Add organization_id if provided
      if (body.organization_id) {
        jsonPayload.organization_id = body.organization_id;
      }
      
      console.log('JSON payload:', JSON.stringify(jsonPayload, null, 2));
      
      const jsonResponse = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: JSON.stringify(jsonPayload)
      });
      
      console.log('JSON format status:', jsonResponse.status);
      const jsonText = await jsonResponse.text();
      console.log('JSON format response:', jsonText);
      jsonResult = {
        status: jsonResponse.status,
        text: jsonText,
        success: jsonResponse.ok
      };
    } catch (e: any) {
      console.error('Error with JSON format:', e);
      jsonResult = {
        error: e.message || 'Unknown error',
        success: false
      };
    }
    
    // 2. Form URL Encoded format
    console.log('\n==== ATTEMPT 2: FORM URL ENCODED FORMAT ====');
    let formResult;
    try {
      // Create form data
      const formData = new URLSearchParams();
      formData.append('emails', '<EMAIL>');
      formData.append('role', 'supplier');
      formData.append('message', 'Test invitation');
      formData.append('expires_in_days', '7');
      
      // Add organization_id if provided
      if (body.organization_id) {
        formData.append('organization_id', body.organization_id);
      }
      
      console.log('Form payload:', formData.toString());
      
      const formResponse = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: formData.toString()
      });
      
      console.log('Form format status:', formResponse.status);
      const formText = await formResponse.text();
      console.log('Form format response:', formText);
      formResult = {
        status: formResponse.status,
        text: formText,
        success: formResponse.ok
      };
    } catch (e: any) {
      console.error('Error with form format:', e);
      formResult = {
        error: e.message || 'Unknown error',
        success: false
      };
    }
    
    // 3. Multipart format
    console.log('\n==== ATTEMPT 3: MULTIPART FORMAT ====');
    let multipartResult;
    try {
      // Create form data
      const multipartFormData = new FormData();
      multipartFormData.append('emails', '<EMAIL>');
      multipartFormData.append('role', 'supplier');
      multipartFormData.append('message', 'Test invitation');
      multipartFormData.append('expires_in_days', '7');
      
      // Add organization_id if provided
      if (body.organization_id) {
        multipartFormData.append('organization_id', body.organization_id);
      }
      
      const multipartResponse = await fetch(apiUrl, {
        method: 'POST',
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${token}`
        },
        body: multipartFormData
      });
      
      console.log('Multipart format status:', multipartResponse.status);
      const multipartText = await multipartResponse.text();
      console.log('Multipart format response:', multipartText);
      multipartResult = {
        status: multipartResponse.status,
        text: multipartText,
        success: multipartResponse.ok
      };
    } catch (e: any) {
      console.error('Error with multipart format:', e);
      multipartResult = {
        error: e.message || 'Unknown error',
        success: false
      };
    }
    
    // Return all results
    return NextResponse.json({
      jsonFormat: jsonResult,
      formUrlEncoded: formResult,
      multipart: multipartResult
    });
    
  } catch (error: any) {
    console.error('Debug API error:', error);
    return NextResponse.json({
      error: 'Error in debug API',
      details: error.message || 'Unknown error'
    }, { status: 500 });
  }
}
