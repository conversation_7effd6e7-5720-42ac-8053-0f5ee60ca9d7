import { AuthResponse, LoginData, SignupData, User, CompleteSetupData, IAuthService } from './auth.service';

// Mock database
const mockOrganizations = [
  {
    id: 'org_001',
    name: '<PERSON>',
    type: 'company',
    address: '123 Ascension Way, CA',
    telephone: '+19876543210',
    contact_person: 'Admin User',
    status: 'active',
    country: 'USA',
    incorporation_date: '2023-01-01',
  }
];

const mockUsers: User[] = [
  {
    id: 'usr_superadmin_001',
    email: '<EMAIL>',
    full_name: 'Super Admin',
    first_name: '<PERSON>',
    last_name: 'Admin',
    role: 'super_admin',
    organization_id: 'org_ascension',
    organisation_id: 'org_ascension',
    status: 'active',
    telephone: '+1234567890',
    password: 'Admin@256!!'
  },
  {
    id: 'usr_1a2b3c4d5e6f7g8h9i0j',
    email: '<EMAIL>',
    full_name: '<PERSON>',
    first_name: '<PERSON>',
    last_name: '<PERSON>re<PERSON>',
    role: 'admin',
    organization_id: 'org_1a2b3c4d5e6f7g8h9i0j',
    organisation_id: 'org_1a2b3c4d5e6f7g8h9i0j',
    status: 'active',
    telephone: '******-555-0104',
    password: 'password'
  }
];

const mockUser: User = mockUsers[0];

// Mock AuthService implementation
export const MockAuthService: IAuthService = {
  async checkEmailExists(email: string): Promise<boolean> {
    return Promise.resolve(mockUsers.some(user => user.email.toLowerCase() === email.toLowerCase()));
  },

  async checkPhoneExists(phone: string): Promise<boolean> {
    return Promise.resolve(mockOrganizations.some(org => org.telephone === phone));
  },

  async checkOrgExists(name: string): Promise<boolean> {
    return Promise.resolve(mockOrganizations.some(org => org.name.toLowerCase() === name.toLowerCase()));
  },
  async signup(data: SignupData): Promise<AuthResponse> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        if (mockOrganizations.some(o => o.name.toLowerCase() === data.org_in.name.toLowerCase())) {
          return reject(new Error('Organization name already exists.'));
        }
        if (mockUsers.some(u => u.email.toLowerCase() === data.admin_in.email.toLowerCase())) {
          return reject(new Error('Email already exists.'));
        }

        const newOrgId = `org_${Date.now()}`;
        const newUser: User = {
          id: `usr_${Date.now()}`,
          email: data.admin_in.email,
          full_name: `${data.admin_in.first_name} ${data.admin_in.last_name}`,
          first_name: data.admin_in.first_name,
          last_name: data.admin_in.last_name,
          role: 'admin',
          org_id: newOrgId,
          status: 'pending_verification',
          telephone: data.org_in.telephone,
          password: data.admin_in.password
        };
        
        const newOrg = {
          id: newOrgId,
          ...data.org_in
        };

        mockUsers.push(newUser);
        mockOrganizations.push(newOrg);

        const token = `mock_token_${Date.now()}`;
        localStorage.setItem('access_token', token);
        localStorage.setItem('user', JSON.stringify(newUser));
        
        resolve({
          access_token: token,
          refresh_token: `refresh_${token}`,
          token_type: 'bearer',
          expires_in: 3600
        });
      }, 800);
    });
  },
  
  async login(credentials: LoginData): Promise<AuthResponse> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        // Check if credentials match expected values
        const user = mockUsers.find(u => u.email.toLowerCase() === credentials.email.toLowerCase());

        if (user && user.password === credentials.password) {
          const token = `mock_token_${Date.now()}`;
          localStorage.setItem('access_token', token);
          localStorage.setItem('user', JSON.stringify(user));
          
          resolve({
            access_token: token,
            refresh_token: `refresh_${token}`,
            token_type: 'bearer',
            expires_in: 3600
          });
        } else {
          reject(new Error('Invalid credentials'));
        }
      }, 500);
    });
  },
  
  async completeSetup(data: CompleteSetupData): Promise<AuthResponse> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const token = `mock_token_${Date.now()}`;
        localStorage.setItem('access_token', token);
        localStorage.setItem('user', JSON.stringify(mockUser));
        
        resolve({
          access_token: token,
          refresh_token: `refresh_${token}`,
          token_type: 'bearer',
          expires_in: 3600
        });
      }, 500);
    });
  },
  
  async getCurrentUser(): Promise<User> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const token = localStorage.getItem('access_token');
        if (!token) {
          reject(new Error('No authentication token found'));
          return;
        }
        
        const storedUser = localStorage.getItem('user');
        if (storedUser) {
          resolve(JSON.parse(storedUser));
        } else {
          resolve(mockUser);
        }
      }, 300);
    });
  },
  
  logout(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
  },
  
  getAuthHeader(): { Authorization: string } | {} {
    const token = localStorage.getItem('access_token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  },
  
  isAuthenticated(): boolean {
    return !!localStorage.getItem('access_token');
  }
};

export default MockAuthService;
