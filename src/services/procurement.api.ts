import axios, { AxiosResponse } from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

const getAuthHeaders = () => {
  const token = localStorage.getItem('access_token');
  return {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json',
  };
};

export interface BudgetLimit {
  id: number;
  currency: string;
  amount_threshold: number;
  approver_level_required: number;
  min_quotations_required: number;
  is_general_rule: boolean;
  organisation_id: number;
  created_at: string;
  updated_at: string;
  last_published_at?: string;
}

export interface BudgetLimitCreate {
  currency: string;
  amount_threshold: number;
  approver_level_required: number;
  min_quotations_required: number;
  is_general_rule?: boolean;
}

export interface BudgetLimitUpdate {
  currency?: string;
  amount_threshold?: number;
  approver_level_required?: number;
  min_quotations_required?: number;
  is_general_rule?: boolean;
}

export interface ProcurementGroup {
  id: number;
  group_id_code: string;
  name: string;
  invoices_billed_to?: string;
  status: 'Active' | 'Deactivated';
  organisation_id: number;
  created_by_id?: number;
  created_at: string;
  updated_at: string;
  last_published_at?: string;
}

export interface ProcurementGroupCreate {
  group_id_code: string;
  name: string;
  invoices_billed_to?: string;
  status?: 'Active' | 'Deactivated';
}

export interface ProcurementGroupUpdate {
  group_id_code?: string;
  name?: string;
  invoices_billed_to?: string;
  status?: 'Active' | 'Deactivated';
}

export interface BudgetCode {
  id: number;
  code: string;
  description: string;
  department?: string;
  is_active: boolean;
  organisation_id: number;
  created_at: string;
  updated_at: string;
}

export interface BudgetCodeCreate {
  code: string;
  description: string;
  department?: string;
  is_active?: boolean;
}

export interface BudgetCodeUpdate {
  code?: string;
  description?: string;
  department?: string;
  is_active?: boolean;
}

export interface BackendUser {
  id: number;
  email: string;
  first_name: string;
  last_name: string;
  full_name?: string;
  is_superuser: boolean;
  organisation_id: number;
  role?: string;
  role_names?: string[];
  permissions?: string[];
}

export const procurementApi = {
  budgetLimits: {
    async list(): Promise<BudgetLimit[]> {
      const response = await axios.get(`${API_BASE_URL}/procurement/budget-limits`, {
        headers: getAuthHeaders(),
      });
      return response.data;
    },

    async create(data: BudgetLimitCreate): Promise<BudgetLimit> {
      const response = await axios.post(`${API_BASE_URL}/procurement/budget-limits`, data, {
        headers: getAuthHeaders(),
      });
      return response.data;
    },

    async update(id: number, data: BudgetLimitUpdate): Promise<BudgetLimit> {
      const response = await axios.put(`${API_BASE_URL}/procurement/budget-limits/${id}`, data, {
        headers: getAuthHeaders(),
      });
      return response.data;
    },

    async delete(id: number): Promise<void> {
      await axios.delete(`${API_BASE_URL}/procurement/budget-limits/${id}`, {
        headers: getAuthHeaders(),
      });
    },
  },

  procurementGroups: {
    async list(): Promise<ProcurementGroup[]> {
      const response = await axios.get(`${API_BASE_URL}/procurement/groups`, {
        headers: getAuthHeaders(),
      });
      return response.data;
    },

    async create(data: ProcurementGroupCreate): Promise<ProcurementGroup> {
      const response = await axios.post(`${API_BASE_URL}/procurement/groups`, data, {
        headers: getAuthHeaders(),
      });
      return response.data;
    },

    async update(id: number, data: ProcurementGroupUpdate): Promise<ProcurementGroup> {
      const response = await axios.put(`${API_BASE_URL}/procurement/groups/${id}`, data, {
        headers: getAuthHeaders(),
      });
      return response.data;
    },

    async updateStatus(id: number, status: string): Promise<ProcurementGroup> {
      const response = await axios.patch(`${API_BASE_URL}/procurement/groups/${id}/status`, 
        { status }, 
        { headers: getAuthHeaders() }
      );
      return response.data;
    },

    async delete(id: number): Promise<void> {
      await axios.delete(`${API_BASE_URL}/procurement/groups/${id}`, {
        headers: getAuthHeaders(),
      });
    },

    async publish(id: number): Promise<ProcurementGroup> {
      const response = await axios.post(`${API_BASE_URL}/procurement/groups/${id}/publish`, {}, {
        headers: getAuthHeaders(),
      });
      return response.data;
    },
  },

  budgetCodes: {
    async list(): Promise<BudgetCode[]> {
      const response = await axios.get(`${API_BASE_URL}/procurement/budget-codes`, {
        headers: getAuthHeaders(),
      });
      return response.data;
    },

    async create(data: BudgetCodeCreate): Promise<BudgetCode> {
      const response = await axios.post(`${API_BASE_URL}/procurement/budget-codes`, data, {
        headers: getAuthHeaders(),
      });
      return response.data;
    },

    async update(id: number, data: BudgetCodeUpdate): Promise<BudgetCode> {
      const response = await axios.put(`${API_BASE_URL}/procurement/budget-codes/${id}`, data, {
        headers: getAuthHeaders(),
      });
      return response.data;
    },

    async delete(id: number): Promise<void> {
      await axios.delete(`${API_BASE_URL}/procurement/budget-codes/${id}`, {
        headers: getAuthHeaders(),
      });
    },
  },
}; 
