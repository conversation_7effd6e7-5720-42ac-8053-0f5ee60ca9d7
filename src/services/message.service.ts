import api from './api';
import mockMessageService from './mock-message.service';

// Flag to toggle between real API calls and mock service
const USE_MOCK_SERVICE = true;

export interface Message {
  id: string;
  caseReference: string;
  from: string;
  to: string[];
  subject: string;
  body: string;
  preview: string;
  receivedAt: string;
  sentAt?: string;
  isRead: boolean;
  isStarred: boolean;
  hasAttachment: boolean;
  folder: 'inbox' | 'sent' | 'drafts' | 'trash';
  attachments?: MessageAttachment[];
  replyTo?: string;
  threadId?: string;
}

export interface MessageAttachment {
  id: string;
  filename: string;
  size: number;
  mimeType: string;
  url: string;
}

export interface MessageDraft {
  to: string[];
  caseReference: string;
  subject: string;
  body: string;
  attachments?: File[];
}

export interface Recipient {
  id: string;
  username: string;
  email: string;
  supplierCode?: string;
  type: 'user' | 'supplier';
}

export interface FolderCounts {
  inbox: number;
  sent: number;
  drafts: number;
  trash: number;
  unread: number;
  starred: number;
}

class MessageService {
  // Get messages by folder
  async getMessagesByFolder(folder: string, page: number = 1, limit: number = 20): Promise<{
    messages: Message[];
    total: number;
    page: number;
    limit: number;
  }> {
    if (USE_MOCK_SERVICE) {
      return mockMessageService.getMessagesByFolder(folder, page, limit);
    }

    try {
      const response = await api.get<{ 
        data: { 
          messages: Message[]; 
          total: number; 
          page: number; 
          limit: number; 
        } 
      }>(`/v1/notification/messages/folders/${folder}?page=${page}&limit=${limit}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching messages by folder:', error);
      throw error;
    }
  }

  // Get folder counts
  async getFolderCounts(): Promise<FolderCounts> {
    if (USE_MOCK_SERVICE) {
      return mockMessageService.getFolderCounts();
    }

    try {
      const response = await api.get<{ data: FolderCounts }>('/v1/notification/messages/folder-counts');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching folder counts:', error);
      throw error;
    }
  }

  // Get message detail
  async getMessageDetail(messageId: string): Promise<Message> {
    try {
      const response = await api.get<{ data: Message }>(`/v1/notification/messages/${messageId}`);
      return response.data.data;
    } catch (error) {
      console.error('Error fetching message detail:', error);
      throw error;
    }
  }

  // Create new message (draft)
  async createMessage(messageData: MessageDraft): Promise<Message> {
    try {
      const response = await api.post<{ data: Message }>('/v1/notification/messages/', messageData);
      return response.data.data;
    } catch (error) {
      console.error('Error creating message:', error);
      throw error;
    }
  }

  // Update message
  async updateMessage(messageId: string, messageData: Partial<MessageDraft>): Promise<Message> {
    try {
      const response = await api.put<{ data: Message }>(`/v1/notification/messages/${messageId}`, messageData);
      return response.data.data;
    } catch (error) {
      console.error('Error updating message:', error);
      throw error;
    }
  }

  // Send message
  async sendMessage(messageId: string): Promise<void> {
    try {
      await api.post(`/v1/notification/messages/${messageId}/send`);
    } catch (error) {
      console.error('Error sending message:', error);
      throw error;
    }
  }

  // Reply to message
  async replyToMessage(messageId: string, replyData: {
    body: string;
    attachments?: File[];
  }): Promise<Message> {
    try {
      const response = await api.post<{ data: Message }>(`/v1/notification/messages/${messageId}/reply`, replyData);
      return response.data.data;
    } catch (error) {
      console.error('Error replying to message:', error);
      throw error;
    }
  }

  // Delete message
  async deleteMessage(messageId: string): Promise<void> {
    try {
      await api.delete(`/v1/notification/messages/${messageId}`);
    } catch (error) {
      console.error('Error deleting message:', error);
      throw error;
    }
  }

  // Move message to folder
  async moveMessage(messageId: string, folder: string): Promise<void> {
    try {
      await api.put(`/v1/notification/messages/${messageId}/move`, { folder });
    } catch (error) {
      console.error('Error moving message:', error);
      throw error;
    }
  }

  // Search recipients
  async searchRecipients(query: string): Promise<Recipient[]> {
    try {
      const response = await api.get<{ data: Recipient[] }>(`/v1/notification/messages/search/recipients?q=${encodeURIComponent(query)}`);
      return response.data.data;
    } catch (error) {
      console.error('Error searching recipients:', error);
      throw error;
    }
  }

  // Upload attachment
  async uploadAttachment(file: File): Promise<MessageAttachment> {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await api.post<{ data: MessageAttachment }>('/v1/notification/messages/upload-attachment', formData, {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });
      return response.data.data;
    } catch (error) {
      console.error('Error uploading attachment:', error);
      throw error;
    }
  }

  // Bulk delete messages
  async bulkDeleteMessages(messageIds: string[]): Promise<void> {
    try {
      await api.post('/v1/notification/messages/bulk-delete', { messageIds });
    } catch (error) {
      console.error('Error bulk deleting messages:', error);
      throw error;
    }
  }

  // Bulk move messages
  async bulkMoveMessages(messageIds: string[], folder: string): Promise<void> {
    try {
      await api.post('/v1/notification/messages/bulk-move', { messageIds, folder });
    } catch (error) {
      console.error('Error bulk moving messages:', error);
      throw error;
    }
  }

  // Get message templates
  async getMessageTemplates(): Promise<Array<{
    id: string;
    name: string;
    subject: string;
    body: string;
  }>> {
    try {
      const response = await api.get<{ data: Array<{
        id: string;
        name: string;
        subject: string;
        body: string;
      }> }>('/v1/notification/messages/templates');
      return response.data.data;
    } catch (error) {
      console.error('Error fetching message templates:', error);
      throw error;
    }
  }
}

export const messageService = new MessageService();
export default messageService;
