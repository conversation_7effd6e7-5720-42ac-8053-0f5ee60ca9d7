import axios from 'axios';

// Use local proxy endpoints to avoid CORS issues in development
const API_URL = '/api/proxy';

interface RequestPasswordResetResponse {
  message: string;
  success: boolean;
}

interface ResetPasswordResponse {
  message: string;
  success: boolean;
}

export interface IPasswordService {
  requestPasswordReset(email: string): Promise<RequestPasswordResetResponse>;
  resetPassword(token: string, newPassword: string): Promise<ResetPasswordResponse>;
  verifyResetToken(token: string): Promise<boolean>;
}

// Password Service implementation
export const PasswordService: IPasswordService = {
  async requestPasswordReset(email: string): Promise<RequestPasswordResetResponse> {
    try {
      // Since there's no specific password reset endpoint in the API docs,
      // we'll use a generic endpoint that should be implemented on the backend
      const response = await axios.post(`${API_URL}/auth/forgot-password`, 
        { email },
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      return {
        message: response.data.message || 'Password reset email sent successfully.',
        success: true
      };
    } catch (error: any) {
      console.error('Password reset request failed:', error);
      throw new Error(error.response?.data?.error?.message || error.message || 'Failed to send password reset email.');
    }
  },
  
  async resetPassword(token: string, newPassword: string): Promise<ResetPasswordResponse> {
    try {
      // We'll use the complete-setup endpoint from the API docs since it's the closest match
      // for resetting a password with a token
      const response = await axios.post(`${API_URL}/auth/complete-setup`, 
        { 
          token,
          password: newPassword
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      return {
        message: response.data.message || 'Password reset successful.',
        success: true
      };
    } catch (error: any) {
      console.error('Password reset failed:', error);
      throw new Error(error.response?.data?.error?.message || error.message || 'Failed to reset password.');
    }
  },
  
  async verifyResetToken(token: string): Promise<boolean> {
    try {
      // Verify if the token is valid before showing the reset password form
      const response = await axios.post(`${API_URL}/auth/verify-token`, 
        { token },
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json'
          }
        }
      );
      
      return response.data.valid || false;
    } catch (error) {
      console.error('Token verification failed:', error);
      return false;
    }
  }
};

export default PasswordService;
