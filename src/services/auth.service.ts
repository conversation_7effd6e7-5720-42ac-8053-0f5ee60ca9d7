import axios from 'axios';
import MockAuthService from './mock-auth.service';

// Flag to toggle between real API calls and mock service
const USE_MOCK_SERVICE = false; // Using real backend service

// Use local proxy endpoints to avoid CORS issues in development
const API_URL = '/api/proxy';

// Exporting interfaces for use in mock service
export interface AuthResponse {
  access_token: string;
  refresh_token?: string;
  token_type: string;
  expires_in?: number;
  scope?: string;
}

export interface User {
  id: string;
  email: string;
  full_name: string;
  first_name?: string;
  last_name?: string;
  role: string;
  role_names?: string[]; // Add support for backend role_names array
  organization_id?: string; // US spelling
  organisation_id?: string; // UK spelling - API returns this
  org_id?: string; // Used in mock data
  status?: string; // User status
  telephone?: string; // User phone
  password?: string; // For mock auth service only
}

export interface LoginData {
  email: string;
  password: string;
}

export interface SignupData {
  org_in: {
    name: string;
    type: 'company' | 'supplier';
    address: string;
    telephone: string;
    contact_person: string;
    status: string;
    country: string;
    incorporation_date: string;
  };
  admin_in: {
    email: string;
    password: string;
    first_name: string;
    last_name: string;
    telephone_number: string;
    role_names: string[];
  };
}

export interface CompleteSetupData {
  token: string;
  password: string;
  profile?: Record<string, any>;
}

export interface IAuthService {
  signup(data: SignupData): Promise<AuthResponse>;
  login(credentials: LoginData): Promise<AuthResponse>;
  completeSetup(data: CompleteSetupData): Promise<AuthResponse>;
  getCurrentUser(): Promise<User>;
  logout(): void;
  getAuthHeader(): { Authorization: string } | {};
  isAuthenticated(): boolean;
  checkEmailExists(email: string): Promise<boolean>;
  checkPhoneExists(phone: string): Promise<boolean>;
  checkOrgExists(name: string): Promise<boolean>;
}

// AuthService implementation
export const RealAuthService: IAuthService = {
  async signup(data: SignupData): Promise<AuthResponse> {
    try {
      const response = await axios.post(`${API_URL}/auth/signup`, data, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      const { access_token, refresh_token, token_type } = response.data;
      if (access_token) {
        localStorage.setItem('access_token', access_token);
        if (refresh_token) {
          localStorage.setItem('refresh_token', refresh_token);
        }
        
        // Try to get user data after signup
        try {
          const userData = await this.getCurrentUser();
          localStorage.setItem('user', JSON.stringify(userData));
        } catch (error) {
          console.warn('Could not fetch user data after signup:', error);
        }
      }
      
      return {
        access_token,
        refresh_token,
        token_type: token_type || 'bearer'
      };
    } catch (error) {
      console.error('Signup failed:', error);
      throw error;
    }
  },
  
  async login(credentials: LoginData): Promise<AuthResponse> {
    try {
      console.log('Login attempt with:', { email: credentials.email });
      
      // For the local API proxy, we'll send JSON since our proxy will convert it
      const response = await axios.post(`${API_URL}/auth/login`, {
        email: credentials.email,
        password: credentials.password
      }, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      // Check if we got a proper response
      if (!response.data || !response.data.access_token) {
        console.error('Login response missing token:', response.data);
        throw new Error('Invalid login response: Missing access token');
      }
      
      const { access_token, refresh_token, token_type, expires_in, scope } = response.data;
      
      if (access_token) {
        localStorage.setItem('access_token', access_token);
        if (refresh_token) {
          localStorage.setItem('refresh_token', refresh_token);
        }
        
        // Try to get user data
        try {
          const userData = await AuthService.getCurrentUser();
          
          // Prevent login if user has no role
          if (!userData.role && (!userData.role_names || userData.role_names.length === 0)) {
            console.error('Login rejected: User has no assigned role');
            AuthService.logout(); // Clear tokens
            throw new Error('Access denied: No role assigned to this account. Please contact your administrator.');
          }
          
          localStorage.setItem('user', JSON.stringify(userData));
        } catch (e) {
          console.error('Failed during login process', e);
          throw e;
        }
      }
      
      return {
        access_token,
        refresh_token,
        token_type: token_type || 'bearer'
      };
    } catch (error) {
      console.error('Login failed:', error);
      throw error;
    }
  },
  
  async completeSetup(data: CompleteSetupData): Promise<AuthResponse> {
    try {
      const response = await axios.post(`${API_URL}/auth/complete-setup`, data, {
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        }
      });
      
      const { access_token, refresh_token, token_type } = response.data;
      
      if (access_token) {
        localStorage.setItem('access_token', access_token);
        if (refresh_token) {
          localStorage.setItem('refresh_token', refresh_token);
        }
        
        // Try to get user data after setup completion
        try {
          const userData = await this.getCurrentUser();
          localStorage.setItem('user', JSON.stringify(userData));
        } catch (error) {
          console.warn('Could not fetch user data after setup completion:', error);
        }
      }
      
      return {
        access_token,
        refresh_token,
        token_type: token_type || 'bearer'
      };
    } catch (error) {
      console.error('Setup completion failed:', error);
      throw error;
    }
  },
  
  getCurrentUser: async (): Promise<User> => {
    try {
      // Get token from localStorage
      const token = localStorage.getItem('access_token');
      
      if (!token) {
        throw new Error('No authentication token found');
      }
      
      console.log('🔍 Fetching user data from API endpoint...');
      
      const response = await axios.get(`${API_URL}/auth/users/me`, {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Accept': 'application/json'
        }
      });
      
      const userData = response.data;
      
      // Enhanced debugging for role detection
      console.group('📊 User Authentication Data');
      console.log('Raw API response:', response.status, response.statusText);
      console.log('Response headers:', response.headers);
      console.log('User data structure:', Object.keys(userData));
      console.log('User data from API:', userData);
      
      // Check role sources - prioritize role_names over role field
      if (userData.role_names && Array.isArray(userData.role_names) && userData.role_names.length > 0) {
        console.log('✅ Using role from role_names array:', userData.role_names[0]);
        userData.role = userData.role_names[0]; // Use the first role from the role_names array
      } else if (userData.role) {
        console.log('ℹ️ Using role field from response:', userData.role);
        // Keep using the existing role field
      } else {
        console.warn('⛔ No role found in API response, using default role: c_admin');
        userData.role = 'c_admin'; // Default to company admin if role is still missing
      }
      
      // Extract first_name and last_name if available
      if (userData.first_name) {
        // If API directly provides first_name and last_name, use them
        console.log('✅ Using first_name and last_name from API response');
      } else if (userData.full_name) {
        // If only full_name is available, try to split it
        const nameParts = userData.full_name.split(' ');
        if (nameParts.length > 1) {
          userData.first_name = nameParts[0];
          userData.last_name = nameParts.slice(1).join(' ');
        } else {
          userData.first_name = userData.full_name;
          userData.last_name = '';
        }
        console.log('ℹ️ Split full_name into first_name and last_name:', userData.first_name, userData.last_name);
      }
      
      // Normalize organization_id - ensure both spellings are available
      if (userData.organisation_id && !userData.organization_id) {
        userData.organization_id = userData.organisation_id;
        console.log('✅ Normalized organisation_id to organization_id:', userData.organization_id);
      } else if (userData.organization_id && !userData.organisation_id) {
        userData.organisation_id = userData.organization_id;
        console.log('✅ Normalized organization_id to organisation_id:', userData.organisation_id);
      }

      console.log('Final user data with role and normalized org ID:', userData);
      console.groupEnd();

      return userData;
    } catch (error: any) {
      console.error('❌ Failed to get current user:', error);
      
      // Enhanced error reporting
      if (error.response) {
        // The request was made and the server responded with a status code outside of 2xx
        console.error('Server responded with error:', error.response.status, error.response.statusText);
        console.error('Error response data:', error.response.data);
      } else if (error.request) {
        // The request was made but no response was received
        console.error('No response received from server');
      } else {
        // Something happened in setting up the request that triggered an Error
        console.error('Error setting up request:', error.message);
      }
      
      throw error;
    }
  },
  
  logout(): void {
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
  },
  
  getAuthHeader(): { Authorization: string } | {} {
    const token = localStorage.getItem('access_token');
    return token ? { Authorization: `Bearer ${token}` } : {};
  },
  
  isAuthenticated(): boolean {
    const token = localStorage.getItem('token');
    return !!token;
  },

  async checkEmailExists(email: string): Promise<boolean> {
    // This is a placeholder. In a real app, you would make an API call.
    console.warn('checkEmailExists is not implemented in RealAuthService');
    return false; 
  },

  async checkPhoneExists(phone: string): Promise<boolean> {
    // This is a placeholder. In a real app, you would make an API call.
    console.warn('checkPhoneExists is not implemented in RealAuthService');
    return false;
  },

  async checkOrgExists(name: string): Promise<boolean> {
    // This is a placeholder. In a real app, you would make an API call.
    console.warn('checkOrgExists is not implemented in RealAuthService');
    return false;
  }
};

// Determine which implementation to export
export const AuthService: IAuthService = USE_MOCK_SERVICE ? MockAuthService : RealAuthService;
export default AuthService;
