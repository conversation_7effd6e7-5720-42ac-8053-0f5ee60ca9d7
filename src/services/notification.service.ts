import api from './api';
import mockNotificationService from './mock-notification.service';

// Flag to toggle between real API calls and mock service
const USE_MOCK_SERVICE = true;

export interface Notification {
  id: string;
  caseReference: string;
  subject: string;
  message: string;
  type: 'status_change' | 'new_quote' | 'case_assignment' | 'new_invoice' | 'new_credit_note' | 'new_debit_note';
  isRead: boolean;
  createdAt: string;
  procurementCaseId?: string;
  userId?: string;
}

export interface NotificationCounts {
  unread: number;
  total: number;
}

export interface CaseInvolvement {
  id: string;
  procurementCaseId: string;
  userId: string;
  role: 'collaborator' | 'forwarded' | 'inquiry_recipient';
  createdAt: string;
}

class NotificationService {
  // Get all notifications
  async getNotifications(): Promise<Notification[]> {
    if (USE_MOCK_SERVICE) {
      return mockNotificationService.getNotifications();
    }
    
    try {
      const response = await api.get<{ data: Notification[] }>('/v1/notification/notifications/');
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching notifications:', error);
      throw error;
    }
  }

  // Get unread count
  async getUnreadCount(): Promise<number> {
    if (USE_MOCK_SERVICE) {
      return mockNotificationService.getUnreadCount();
    }
    
    try {
      const response = await api.get<{ data: { count: number } }>('/v1/notification/notifications/unread-count');
      return response.data.data?.count || 0;
    } catch (error) {
      console.error('Error fetching unread count:', error);
      throw error;
    }
  }

  // Mark single notification as viewed
  async markAsViewed(notificationId: string): Promise<void> {
    if (USE_MOCK_SERVICE) {
      return mockNotificationService.markAsViewed(notificationId);
    }
    
    try {
      await api.put(`/v1/notification/notifications/${notificationId}/mark-viewed`);
    } catch (error) {
      console.error('Error marking notification as viewed:', error);
      throw error;
    }
  }

  // Mark all notifications as viewed
  async markAllAsViewed(): Promise<void> {
    if (USE_MOCK_SERVICE) {
      return mockNotificationService.markAllAsViewed();
    }
    
    try {
      await api.put('/v1/notification/notifications/mark-all-viewed');
    } catch (error) {
      console.error('Error marking all notifications as viewed:', error);
      throw error;
    }
  }

  // Create case involvement
  async createCaseInvolvement(data: {
    procurementCaseId: string;
    userId: string;
    role: 'collaborator' | 'forwarded' | 'inquiry_recipient';
  }): Promise<CaseInvolvement> {
    if (USE_MOCK_SERVICE) {
      return mockNotificationService.createCaseInvolvement(data);
    }
    
    try {
      const response = await api.post<{ data: CaseInvolvement }>('/v1/notification/notifications/case-involvement', data);
      return response.data.data;
    } catch (error) {
      console.error('Error creating case involvement:', error);
      throw error;
    }
  }

  // Get case involvements
  async getCaseInvolvements(procurementCaseId: string): Promise<CaseInvolvement[]> {
    if (USE_MOCK_SERVICE) {
      return mockNotificationService.getCaseInvolvements(procurementCaseId);
    }
    
    try {
      const response = await api.get<{ data: CaseInvolvement[] }>(`/v1/notification/notifications/case-involvements/${procurementCaseId}`);
      return response.data.data || [];
    } catch (error) {
      console.error('Error fetching case involvements:', error);
      throw error;
    }
  }

  // Trigger status change notification
  async triggerStatusChange(data: {
    procurementCaseId: string;
    status: string;
    message?: string;
  }): Promise<void> {
    if (USE_MOCK_SERVICE) {
      return mockNotificationService.triggerStatusChange(data);
    }
    
    try {
      await api.post('/v1/notification/notifications/trigger/status-change', data);
    } catch (error) {
      console.error('Error triggering status change notification:', error);
      throw error;
    }
  }

  // Trigger new quote notification
  async triggerNewQuote(data: {
    procurementCaseId: string;
    quoteId: string;
    supplierId: string;
  }): Promise<void> {
    if (USE_MOCK_SERVICE) {
      return mockNotificationService.triggerNewQuote(data);
    }
    
    try {
      await api.post('/v1/notification/notifications/trigger/new-quote', data);
    } catch (error) {
      console.error('Error triggering new quote notification:', error);
      throw error;
    }
  }

  // Trigger case assignment notification
  async triggerCaseAssignment(data: {
    procurementCaseId: string;
    assignedToUserId: string;
    assignedByUserId: string;
  }): Promise<void> {
    if (USE_MOCK_SERVICE) {
      return mockNotificationService.triggerCaseAssignment(data);
    }
    
    try {
      await api.post('/v1/notification/notifications/trigger/case-assignment', data);
    } catch (error) {
      console.error('Error triggering case assignment notification:', error);
      throw error;
    }
  }
}

export const notificationService = new NotificationService();
export default notificationService; 
