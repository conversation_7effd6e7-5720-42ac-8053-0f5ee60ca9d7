import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';

// For direct backend API calls, use the base URL from env
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || '';

// For local Next.js API routes and proxy routes, use a relative URL
const isClient = typeof window !== 'undefined';

class ApiService {
  private instance: AxiosInstance;

  constructor() {
    this.instance = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
      // Adding timeout to avoid long waits on connection issues
      timeout: 10000,
    });

    this.instance.interceptors.response.use(
      (response) => {
        // The backend wraps responses in a 'data' object on success
        // so we return response.data to align with that structure.
        return response;
      },
      (error) => {
        console.error('API Error:', error.response?.data || error.message || error);
        // Log more details for connection issues
        if (!error.response) {
          console.error('Network or CORS issue detected');
        }
        // We reject with the error so that calling code can handle it.
        return Promise.reject(error);
      }
    );
  }

  public get<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.get<T>(url, config);
  }

  public post<T = any, R = AxiosResponse<T>>(url: string, data?: any, config?: AxiosRequestConfig): Promise<R> {
    // If this is a proxy route, don't use the base URL
    if (url.startsWith('/api/proxy')) {
      // Get the auth token from localStorage if available (client-side only)
      let authHeader = {};
      if (typeof window !== 'undefined') {
        const token = localStorage.getItem('access_token');
        if (token) {
          authHeader = { Authorization: `Bearer ${token}` };
        }
      }
      
      const axiosInstance = axios.create({
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json',
          ...authHeader,
          ...(config?.headers || {}),
        },
        timeout: 10000,
      });
      
      return axiosInstance.post<T, R>(url, data, config);
    }
    
    return this.instance.post<T, R>(url, data, config);
  }

  public put<T>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.put<T>(url, data, config);
  }

  public delete<T>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.instance.delete<T>(url, config);
  }
}

const api = new ApiService();

export default api;
export { api };
