import { Notification, CaseInvolvement } from './notification.service';

// Mock data for notifications
const mockNotifications: Notification[] = [
  {
    id: '1',
    caseReference: 'PRO-2023-001',
    subject: 'Case status changed',
    message: 'The procurement case PRO-2023-001 has been approved and moved to the next stage.',
    type: 'status_change',
    isRead: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 15).toISOString(), // 15 minutes ago
    procurementCaseId: '12345'
  },
  {
    id: '2',
    caseReference: 'PRO-2023-002',
    subject: 'New quote received',
    message: 'You have received a new quote for procurement case PRO-2023-002 from Supplier Inc.',
    type: 'new_quote',
    isRead: false,
    createdAt: new Date(Date.now() - 1000 * 60 * 60).toISOString(), // 1 hour ago
    procurementCaseId: '23456'
  },
  {
    id: '3',
    caseReference: 'PRO-2023-003',
    subject: 'Case assigned to you',
    message: '<PERSON> has assigned procurement case PRO-2023-003 to you.',
    type: 'case_assignment',
    isRead: true,
    createdAt: new Date(Date.now() - 1000 * 60 * 60 * 3).toISOString(), // 3 hours ago
    procurementCaseId: '34567',
    userId: 'user-123'
  }
];

// Mock data for case involvements
const mockCaseInvolvements: CaseInvolvement[] = [
  {
    id: '1',
    procurementCaseId: '12345',
    userId: 'user-123',
    role: 'collaborator',
    createdAt: new Date().toISOString()
  },
  {
    id: '2',
    procurementCaseId: '12345',
    userId: 'user-456',
    role: 'inquiry_recipient',
    createdAt: new Date().toISOString()
  }
];

class MockNotificationService {
  // Get all notifications
  async getNotifications(): Promise<Notification[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve(mockNotifications);
      }, 300); // Simulate network delay
    });
  }

  // Get unread count
  async getUnreadCount(): Promise<number> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const unreadCount = mockNotifications.filter(n => !n.isRead).length;
        resolve(unreadCount);
      }, 200);
    });
  }

  // Mark single notification as viewed
  async markAsViewed(notificationId: string): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const notification = mockNotifications.find(n => n.id === notificationId);
        if (notification) {
          notification.isRead = true;
        }
        resolve();
      }, 200);
    });
  }

  // Mark all notifications as viewed
  async markAllAsViewed(): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        mockNotifications.forEach(n => n.isRead = true);
        resolve();
      }, 200);
    });
  }

  // Create case involvement
  async createCaseInvolvement(data: {
    procurementCaseId: string;
    userId: string;
    role: 'collaborator' | 'forwarded' | 'inquiry_recipient';
  }): Promise<CaseInvolvement> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newInvolvement: CaseInvolvement = {
          id: Math.random().toString(36).substring(2, 9),
          procurementCaseId: data.procurementCaseId,
          userId: data.userId,
          role: data.role,
          createdAt: new Date().toISOString()
        };
        mockCaseInvolvements.push(newInvolvement);
        resolve(newInvolvement);
      }, 300);
    });
  }

  // Get case involvements
  async getCaseInvolvements(procurementCaseId: string): Promise<CaseInvolvement[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const involvements = mockCaseInvolvements.filter(i => i.procurementCaseId === procurementCaseId);
        resolve(involvements);
      }, 300);
    });
  }

  // Trigger status change notification
  async triggerStatusChange(data: {
    procurementCaseId: string;
    status: string;
    message?: string;
  }): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newNotification: Notification = {
          id: Math.random().toString(36).substring(2, 9),
          caseReference: `PRO-${Math.floor(Math.random() * 9000) + 1000}`,
          subject: 'Status Changed',
          message: data.message || `Case status has been changed to ${data.status}`,
          type: 'status_change',
          isRead: false,
          createdAt: new Date().toISOString(),
          procurementCaseId: data.procurementCaseId
        };
        mockNotifications.unshift(newNotification);
        resolve();
      }, 300);
    });
  }

  // Trigger new quote notification
  async triggerNewQuote(data: {
    procurementCaseId: string;
    quoteId: string;
    supplierId: string;
  }): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newNotification: Notification = {
          id: Math.random().toString(36).substring(2, 9),
          caseReference: `PRO-${Math.floor(Math.random() * 9000) + 1000}`,
          subject: 'New Quote',
          message: `A new quote has been received for procurement case ${data.procurementCaseId}`,
          type: 'new_quote',
          isRead: false,
          createdAt: new Date().toISOString(),
          procurementCaseId: data.procurementCaseId
        };
        mockNotifications.unshift(newNotification);
        resolve();
      }, 300);
    });
  }

  // Trigger case assignment notification
  async triggerCaseAssignment(data: {
    procurementCaseId: string;
    assignedToUserId: string;
    assignedByUserId: string;
  }): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newNotification: Notification = {
          id: Math.random().toString(36).substring(2, 9),
          caseReference: `PRO-${Math.floor(Math.random() * 9000) + 1000}`,
          subject: 'Case Assigned',
          message: `A procurement case has been assigned to you by user ${data.assignedByUserId}`,
          type: 'case_assignment',
          isRead: false,
          createdAt: new Date().toISOString(),
          procurementCaseId: data.procurementCaseId,
          userId: data.assignedToUserId
        };
        mockNotifications.unshift(newNotification);
        resolve();
      }, 300);
    });
  }
}

export const mockNotificationService = new MockNotificationService();
export default mockNotificationService;
