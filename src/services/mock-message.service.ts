import { Message, MessageAttachment, MessageDraft, Recipient, FolderCounts } from './message.service';

// Mock data for messages
const mockMessages: Message[] = [
  {
    id: '1',
    caseReference: 'PRO-2023-001',
    from: '<EMAIL>',
    to: ['<EMAIL>'],
    subject: 'Updated Quote for Office Supplies',
    body: '<p>Hello,</p><p>We have reviewed your requirements and updated our quote for the office supplies. Please find the details attached.</p><p>Best regards,<br><PERSON></p>',
    preview: 'Hello, We have reviewed your requirements and updated our quote for the office supplies. Please find the details attached.',
    receivedAt: new Date(Date.now() - 1000 * 60 * 30).toISOString(), // 30 minutes ago
    isRead: false,
    isStarred: true,
    hasAttachment: true,
    folder: 'inbox',
    attachments: [
      {
        id: 'a1',
        filename: 'updated_quote.pdf',
        size: 2457600,
        mimeType: 'application/pdf',
        url: '#'
      }
    ],
    threadId: 't1'
  },
  {
    id: '2',
    caseReference: 'PRO-2023-002',
    from: '<EMAIL>',
    to: ['<EMAIL>'],
    subject: 'Request for Meeting - IT Equipment Procurement',
    body: '<p>Dear Team,</p><p>I would like to schedule a meeting to discuss the procurement of new IT equipment for our department. Please let me know your availability for next week.</p><p>Regards,<br>Jane Smith</p>',
    preview: 'Dear Team, I would like to schedule a meeting to discuss the procurement of new IT equipment for our department.',
    receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 2).toISOString(), // 2 hours ago
    isRead: true,
    isStarred: false,
    hasAttachment: false,
    folder: 'inbox',
    threadId: 't2'
  },
  {
    id: '3',
    caseReference: 'PRO-2023-003',
    from: '<EMAIL>',
    to: ['<EMAIL>'],
    subject: 'Inquiry about Furniture Delivery Timeline',
    body: '<p>Hello Supplier,</p><p>We are following up on our recent order for office furniture. Could you please provide an update on the expected delivery timeline?</p><p>Thank you,<br>Procurement Team</p>',
    preview: 'Hello Supplier, We are following up on our recent order for office furniture.',
    sentAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
    receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 24).toISOString(), // 1 day ago
    isRead: true,
    isStarred: false,
    hasAttachment: false,
    folder: 'sent',
    threadId: 't3'
  },
  {
    id: '4',
    caseReference: 'PRO-2023-004',
    from: '<EMAIL>',
    to: ['<EMAIL>'],
    subject: 'Draft: Quarterly Procurement Report',
    body: '<p>Dear Manager,</p><p>Attached is the draft of our quarterly procurement report for your review. Please provide your feedback before we finalize it.</p><p>Best regards,<br>Procurement Team</p>',
    preview: 'Dear Manager, Attached is the draft of our quarterly procurement report for your review.',
    receivedAt: new Date().toISOString(),
    isRead: true,
    isStarred: false,
    hasAttachment: true,
    folder: 'drafts',
    attachments: [
      {
        id: 'a2',
        filename: 'quarterly_report_draft.xlsx',
        size: 1254400,
        mimeType: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        url: '#'
      }
    ]
  },
  {
    id: '5',
    caseReference: 'PRO-2023-005',
    from: '<EMAIL>',
    to: ['<EMAIL>'],
    subject: 'System Notification: Account Verification',
    body: '<p>This is an automated message to inform you that your account has been verified. No action is required.</p>',
    preview: 'This is an automated message to inform you that your account has been verified.',
    receivedAt: new Date(Date.now() - 1000 * 60 * 60 * 48).toISOString(), // 2 days ago
    isRead: true,
    isStarred: false,
    hasAttachment: false,
    folder: 'trash'
  }
];

// Mock folder counts
const mockFolderCounts: { [key: string]: number } = {
  inbox: 10,
  sent: 5,
  drafts: 2,
  trash: 3,
  unread: 3,
  starred: 2
};

// Mock recipients for search
const mockRecipients: Recipient[] = [
  { id: '1', username: 'John Doe', email: '<EMAIL>', type: 'user' },
  { id: '2', username: 'Jane Smith', email: '<EMAIL>', type: 'user' },
  { id: '3', username: 'Acme Corp', email: '<EMAIL>', supplierCode: 'ACME001', type: 'supplier' },
  { id: '4', username: 'Tech Solutions', email: '<EMAIL>', supplierCode: 'TECH001', type: 'supplier' }
];

class MockMessageService {
  // Get messages by folder
  async getMessagesByFolder(folder: string, page: number = 1, limit: number = 20): Promise<{
    messages: Message[];
    total: number;
    page: number;
    limit: number;
  }> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const filteredMessages = mockMessages.filter(msg => msg.folder === folder);
        const startIndex = (page - 1) * limit;
        const endIndex = startIndex + limit;
        const paginatedMessages = filteredMessages.slice(startIndex, endIndex);
        
        resolve({
          messages: paginatedMessages,
          total: filteredMessages.length,
          page,
          limit
        });
      }, 300);
    });
  }

  // Get folder counts
  async getFolderCounts(): Promise<FolderCounts> {
    return new Promise((resolve) => {
      setTimeout(() => {
        // Calculate counts dynamically to always be in sync
        const counts: FolderCounts = {
          inbox: mockMessages.filter(msg => msg.folder === 'inbox').length,
          sent: mockMessages.filter(msg => msg.folder === 'sent').length,
          drafts: mockMessages.filter(msg => msg.folder === 'drafts').length,
          trash: mockMessages.filter(msg => msg.folder === 'trash').length,
          unread: mockMessages.filter(msg => msg.folder === 'inbox' && !msg.isRead).length,
          starred: mockMessages.filter(msg => msg.isStarred).length
        };
        resolve(counts);
      }, 300);
    });
  }

  // Get message detail
  async getMessageDetail(messageId: string): Promise<Message> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const message = mockMessages.find(msg => msg.id === messageId);
        if (message) {
          // Mark as read when viewed
          message.isRead = true;
          resolve(message);
        } else {
          reject(new Error('Message not found'));
        }
      }, 300);
    });
  }

  // Create new message (draft)
  async createMessage(messageData: MessageDraft): Promise<Message> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const newMessage: Message = {
          id: Math.random().toString(36).substring(2, 9),
          caseReference: messageData.caseReference,
          from: '<EMAIL>',
          to: messageData.to,
          subject: messageData.subject,
          body: messageData.body,
          preview: messageData.body.replace(/<[^>]*>/g, '').substring(0, 100),
          receivedAt: new Date().toISOString(),
          isRead: true,
          isStarred: false,
          hasAttachment: !!(messageData.attachments && messageData.attachments.length > 0),
          folder: 'drafts'
        };
        
        mockMessages.unshift(newMessage);
        resolve(newMessage);
      }, 400);
    });
  }

  // Update message
  async updateMessage(messageId: string, messageData: Partial<MessageDraft>): Promise<Message> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const messageIndex = mockMessages.findIndex(msg => msg.id === messageId);
        if (messageIndex !== -1) {
          const message = mockMessages[messageIndex];
          
          const updatedMessage = {
            ...message,
            ...messageData,
            preview: messageData.body ? messageData.body.replace(/<[^>]*>/g, '').substring(0, 100) : message.preview,
            hasAttachment: messageData.attachments ? !!(messageData.attachments.length > 0) : message.hasAttachment
          };
          
          mockMessages[messageIndex] = updatedMessage as Message;
          resolve(updatedMessage as Message);
        } else {
          reject(new Error('Message not found'));
        }
      }, 300);
    });
  }

  // Send message
  async sendMessage(messageId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const messageIndex = mockMessages.findIndex(msg => msg.id === messageId);
        if (messageIndex !== -1) {
          const message = mockMessages[messageIndex];
          message.folder = 'sent';
          message.sentAt = new Date().toISOString();
          resolve();
        } else {
          reject(new Error('Message not found'));
        }
      }, 500);
    });
  }

  // Reply to message
  async replyToMessage(messageId: string, replyData: {
    body: string;
    attachments?: File[];
  }): Promise<Message> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const originalMessage = mockMessages.find(msg => msg.id === messageId);
        if (originalMessage) {
          const replyMessage: Message = {
            id: Math.random().toString(36).substring(2, 9),
            caseReference: originalMessage.caseReference,
            from: '<EMAIL>',
            to: [originalMessage.from],
            subject: `Re: ${originalMessage.subject}`,
            body: replyData.body,
            preview: replyData.body.replace(/<[^>]*>/g, '').substring(0, 100),
            receivedAt: new Date().toISOString(),
            sentAt: new Date().toISOString(),
            isRead: true,
            isStarred: false,
            hasAttachment: !!(replyData.attachments && replyData.attachments.length > 0),
            folder: 'sent',
            replyTo: messageId,
            threadId: originalMessage.threadId || messageId
          };
          
          mockMessages.unshift(replyMessage);
          resolve(replyMessage);
        } else {
          reject(new Error('Original message not found'));
        }
      }, 500);
    });
  }

  // Delete message
  async deleteMessage(messageId: string): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const messageIndex = mockMessages.findIndex(msg => msg.id === messageId);
        if (messageIndex !== -1) {
          const message = mockMessages[messageIndex];
          if (message.folder !== 'trash') {
            message.folder = 'trash';
          } else {
            mockMessages.splice(messageIndex, 1);
          }
          resolve();
        } else {
          reject(new Error('Message not found'));
        }
      }, 300);
    });
  }

  // Move message to folder
  async moveMessage(messageId: string, folder: string): Promise<void> {
    return new Promise((resolve, reject) => {
      setTimeout(() => {
        const message = mockMessages.find(msg => msg.id === messageId);
        if (message) {
          message.folder = folder as 'inbox' | 'sent' | 'drafts' | 'trash';
          resolve();
        } else {
          reject(new Error('Message not found'));
        }
      }, 300);
    });
  }

  // Search recipients
  async searchRecipients(query: string): Promise<Recipient[]> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const lowercaseQuery = query.toLowerCase();
        const results = mockRecipients.filter(recipient => 
          recipient.username.toLowerCase().includes(lowercaseQuery) || 
          recipient.email.toLowerCase().includes(lowercaseQuery)
        );
        resolve(results);
      }, 300);
    });
  }

  // Upload attachment (mock)
  async uploadAttachment(file: File): Promise<MessageAttachment> {
    return new Promise((resolve) => {
      setTimeout(() => {
        const mockAttachment: MessageAttachment = {
          id: Math.random().toString(36).substring(2, 9),
          filename: file.name,
          size: file.size,
          mimeType: file.type,
          url: URL.createObjectURL(file) // Creates a local object URL for preview
        };
        resolve(mockAttachment);
      }, 800); // Simulate longer upload time
    });
  }

  // Bulk delete messages
  async bulkDeleteMessages(messageIds: string[]): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        messageIds.forEach(id => {
          const message = mockMessages.find(msg => msg.id === id);
          if (message) {
            if (message.folder !== 'trash') {
              message.folder = 'trash';
            } else {
              const index = mockMessages.findIndex(msg => msg.id === id);
              if (index !== -1) {
                mockMessages.splice(index, 1);
              }
            }
          }
        });
        resolve();
      }, 500);
    });
  }

  // Bulk move messages
  async bulkMoveMessages(messageIds: string[], folder: string): Promise<void> {
    return new Promise((resolve) => {
      setTimeout(() => {
        messageIds.forEach(id => {
          const message = mockMessages.find(msg => msg.id === id);
          if (message) {
            message.folder = folder as 'inbox' | 'sent' | 'drafts' | 'trash';
          }
        });
        resolve();
      }, 500);
    });
  }

  // Get message templates
  async getMessageTemplates(): Promise<Array<{
    id: string;
    name: string;
    subject: string;
    body: string;
  }>> {
    return new Promise((resolve) => {
      setTimeout(() => {
        resolve([
          {
            id: 't1',
            name: 'Quote Request',
            subject: 'Request for Quote - [Project Name]',
            body: '<p>Dear [Supplier],</p><p>We are requesting a quote for the following items:</p><ul><li>[Item 1]</li><li>[Item 2]</li></ul><p>Please provide your best pricing and delivery timeline by [Date].</p><p>Regards,<br>[Your Name]</p>'
          },
          {
            id: 't2',
            name: 'Order Confirmation',
            subject: 'Order Confirmation - [Order Reference]',
            body: '<p>Dear [Supplier],</p><p>This email confirms our order with reference number [Order Reference].</p><p>Please proceed with processing this order according to the agreed terms.</p><p>Regards,<br>[Your Name]</p>'
          },
          {
            id: 't3',
            name: 'Delivery Follow-up',
            subject: 'Follow-up on Delivery Status - [Order Reference]',
            body: '<p>Dear [Supplier],</p><p>We are following up on the delivery status of our order [Order Reference] placed on [Order Date].</p><p>Could you please provide an update on the current status and expected delivery date?</p><p>Thank you,<br>[Your Name]</p>'
          }
        ]);
      }, 300);
    });
  }
}

export const mockMessageService = new MockMessageService();
export default mockMessageService;
