'use client';

import React, { useState, useEffect } from 'react';
import { FiEdit2, FiTrash2, <PERSON>Eye, FiChevronDown, FiChevronUp, FiFilter, FiSearch, FiX } from 'react-icons/fi';
import { useSupplierDatabase } from '@/context/SupplierDatabaseContext';
import { useRouter } from 'next/navigation';
import Link from 'next/link';

// Mock data for suppliers based on the image
const mockSuppliers = [
  {
    id: 'AAGARAG1',
    code: 'AAGARAG1',
    name: 'A.A GARAGE',
    region: 'Central',
    location: 'Kampala',
    status: 'Active',
    ppda: 'Yes',
    category: 'General Merchandise',
  },
  {
    id: 'AAENTEO1',
    code: 'AAENTEO1',
    name: 'A.A.A ENTERPRISES',
    region: 'Central',
    location: 'Kampala',
    status: 'Active',
    ppda: 'Yes',
    category: 'Medical & Healthcare',
  },
  {
    id: 'AACONSO1',
    code: 'AACONSO1',
    name: 'AAA CONSTRUMAX LIMITED',
    region: 'Eastern',
    location: 'Soroti',
    status: 'Active',
    ppda: 'Yes',
    category: 'Agricultural Tools & Produce',
  },
  {
    id: 'AABEVEO1',
    code: 'AABEVEO1',
    name: 'A.A.A BEVERAGES',
    region: 'Central',
    location: 'Kampala',
    status: 'Active',
    ppda: 'No',
    category: 'General Merchandise',
  },
  {
    id: 'ASCTECO1',
    code: 'ASCTECO1',
    name: 'ASCENDINO TECHNOLOGIES LTD',
    region: 'Northern',
    location: 'Gulu',
    status: 'Active',
    ppda: 'Yes',
    category: 'ICT Hardware & Software',
  },
  {
    id: 'BASANCO1',
    code: 'BASANCO1',
    name: 'BA SANCHO AGENCIES',
    region: 'Central',
    location: 'Kampala',
    status: 'Active',
    ppda: 'Yes',
    category: 'General Merchandise',
  },
  {
    id: 'FABALO1',
    code: 'FABALO1',
    name: 'FA-BA LIMITED',
    region: 'Central',
    location: 'Entebbe',
    status: 'Active',
    ppda: 'Yes',
    category: 'Automotive Works & Spares',
  },
  {
    id: 'BECAMEO1',
    code: 'BECAMEO1',
    name: 'BE CAMEL UGANDA LIMITED',
    region: 'Western',
    location: 'Mbarara',
    status: 'Active',
    ppda: 'Yes',
    category: 'General Merchandise',
  },
  {
    id: 'BEFORWO1',
    code: 'BEFORWO1',
    name: 'BE FORWARD UGANDA',
    region: 'Northern',
    location: 'Adjumani',
    status: 'Active',
    ppda: 'Yes',
    category: 'Automotive Works & Spares',
  },
  {
    id: 'BEMARKO1',
    code: 'BEMARKO1',
    name: 'BE MARKETING LIMITED',
    region: 'Northern',
    location: 'Nebbi',
    status: 'Deactivated',
    ppda: 'No',
    category: 'Leisure & Accommodation',
  },
  {
    id: 'CAFOODO1',
    code: 'CAFOODO1',
    name: 'CA FOOD CO. LIMITED',
    region: 'Western',
    location: 'Mbarara',
    status: 'Active',
    ppda: 'Yes',
    category: 'General Merchandise',
  },
  {
    id: 'CASOFTO1',
    code: 'CASOFTO1',
    name: 'CA SOFTWARE TECHNOLOGIES',
    region: 'Eastern',
    location: 'Soroti',
    status: 'Active',
    ppda: 'Yes',
    category: 'ICT Hardware & Software',
  },
  {
    id: 'CAJOHA01',
    code: 'CAJOHA01',
    name: 'CA JOHA SHOP ENTERPRISES',
    region: 'Central',
    location: 'Kampala',
    status: 'Active',
    ppda: 'Yes',
    category: 'Hardware & Construction',
  },
  {
    id: 'COINNO01',
    code: 'COINNO01',
    name: 'CORITEK INK INNOVATIONS (C)',
    region: 'Northern',
    location: 'Gulu',
    status: 'Active',
    ppda: 'Yes',
    category: 'ICT Hardware & Software',
  },
  {
    id: 'CKSEBO01',
    code: 'CKSEBO01',
    name: 'C K SEBOHA & C K L BULOGA CO LIMITED',
    region: 'Central',
    location: 'Kampala',
    status: 'Active',
    ppda: 'Yes',
    category: 'Oil & Gas Products',
  },
  {
    id: 'LUFRAMO1',
    code: 'LUFRAMO1',
    name: 'LUBIRI FRATERNITY INVESTMENT LTD',
    region: 'Central',
    location: 'Entebbe',
    status: 'Active',
    ppda: 'Yes',
    category: 'General Merchandise',
  },
  {
    id: 'KAMGENO1',
    code: 'KAMGENO1',
    name: 'KAMOGA GENERAL MERCHANDISE',
    region: 'Northern',
    location: 'Adjumani',
    status: 'Deactivated',
    ppda: 'Yes',
    category: 'General Merchandise',
  },
  {
    id: 'NGEAUTO1',
    code: 'NGEAUTO1',
    name: 'NGEYE AUTO SPARES',
    region: 'Central',
    location: 'Iganga',
    status: 'Active',
    ppda: 'Yes',
    category: 'Automotive Works & Spares',
  },
];

// Business categories for filtering
const businessCategories = [
  'General Merchandise',
  'Medical & Healthcare',
  'Agricultural Tools & Produce',
  'ICT Hardware & Software',
  'Automotive Works & Spares',
  'Leisure & Accommodation',
  'Hardware & Construction',
  'Oil & Gas Products',
  'Consultancy Services',
  'Non-consultancy Services',
  'Manufacturer',
  'Electricals & Electronics',
  'Furniture & Fittings',
  'Mechanical Tools & Equipment',
  'Marine Supplies'
];

// Primary filters
const primaryFilters = [
  'Prequalified suppliers',
  'PPDA registered',
  'IFMS registered',
  'VAT registered'
];

// Regions for filtering
const regions = [
  'Central',
  'Eastern',
  'Northern',
  'Western'
];

interface SupplierTableProps {
  filterStatus?: 'All suppliers' | 'Active' | 'Deactivated' | 'Blacklisted';
}

const SupplierTable: React.FC<SupplierTableProps> = ({ filterStatus = 'All suppliers' }: SupplierTableProps) => {
  const router = useRouter();
  const { 
    searchTerm, 
    setSearchTerm, 
    checkedCategories, 
    checkedPrimary,
    sortConfig,
    setSortConfig,
    handleCategoryChange,
    handlePrimaryChange,
    clearFilters: clearContextFilters
  } = useSupplierDatabase();
  const [rowsPerPage, setRowsPerPage] = useState<number>(10);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [showFilters, setShowFilters] = useState<boolean>(false);
  const [selectedRegions, setSelectedRegions] = useState<string[]>([]);
  const [ppdaFilter, setPpdaFilter] = useState<string>('all'); // 'all', 'yes', 'no'

  const [openSections, setOpenSections] = useState<{ [key: string]: boolean }> ({
    primary: true,
    ppda: true,
    region: true,
    category: true,
  });

  const toggleSection = (section: string) => {
    setOpenSections(prev => ({ ...prev, [section]: !prev[section] }));
  };
  
  // Apply all filters to suppliers
  const filteredSuppliers = mockSuppliers.filter(supplier => {
    // Filter by status
    if (filterStatus !== 'All suppliers' && supplier.status !== filterStatus) {
      return false;
    }
    
    // Filter by search term
    if (searchTerm && !(
      supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.location.toLowerCase().includes(searchTerm.toLowerCase()) ||
      supplier.category.toLowerCase().includes(searchTerm.toLowerCase())
    )) {
      return false;
    }
    
    // Filter by category from context
    const selectedCategories = Object.entries(checkedCategories)
      .filter(([_, isChecked]) => isChecked)
      .map(([category]) => category);
      
    if (selectedCategories.length > 0 && !selectedCategories.includes(supplier.category)) {
      return false;
    }
    
    // Apply primary filters from context
    if (checkedPrimary['PPDA registered'] && supplier.ppda !== 'Yes') {
      return false;
    }
    
    // Filter by region
    if (selectedRegions.length > 0 && !selectedRegions.includes(supplier.region)) {
      return false;
    }
    
    // Filter by PPDA status from the table's own filter
    if (ppdaFilter !== 'all') {
      if (ppdaFilter === 'yes' && supplier.ppda !== 'Yes') return false;
      if (ppdaFilter === 'no' && supplier.ppda !== 'No') return false;
    }
    
    // Apply other primary filters if needed
    // Example: if (checkedPrimary['PAYE registered']) { ... }
    
    return true;
  });
  
  // Sort suppliers based on sortConfig from context
  const sortedSuppliers = [...filteredSuppliers].sort((a, b) => {
    if (!sortConfig) return 0;
    
    const fieldA = a[sortConfig.key as keyof typeof a];
    const fieldB = b[sortConfig.key as keyof typeof b];
    
    if (fieldA < fieldB) return sortConfig.direction === 'ascending' ? -1 : 1;
    if (fieldA > fieldB) return sortConfig.direction === 'ascending' ? 1 : -1;
    return 0;
  });
  
  // Calculate active filter count
  const activeFilterCount = (
    (Object.values(checkedCategories).some(Boolean) ? 1 : 0) + 
    (selectedRegions.length > 0 ? 1 : 0) + 
    (ppdaFilter !== 'all' ? 1 : 0) +
    (Object.values(checkedPrimary).some(Boolean) ? 1 : 0) +
    (filterStatus !== 'All suppliers' ? 1 : 0)
  );
  
  // Get selected categories and primary filters as arrays
  const selectedCategories = Object.entries(checkedCategories)
    .filter(([_, isChecked]) => isChecked)
    .map(([category]) => category);
    
  const selectedPrimaryFilters = Object.entries(checkedPrimary)
    .filter(([_, isChecked]) => isChecked)
    .map(([filter]) => filter);

  // Calculate total pages
  const totalPages = Math.ceil(filteredSuppliers.length / rowsPerPage);

  // Get current page data
  const paginatedSuppliers = filteredSuppliers.slice(
    (currentPage - 1) * rowsPerPage,
    currentPage * rowsPerPage
  );
  
  // Handle sort click
  const handleSort = (field: string) => {
    const direction = sortConfig?.key === field && sortConfig.direction === 'ascending' 
      ? 'descending' 
      : 'ascending';
    
    setSortConfig({
      key: field,
      direction: direction as 'ascending' | 'descending'
    });
  };
  
  // Render sort indicator
  const renderSortIndicator = (field: string) => {
    if (!sortConfig || sortConfig.key !== field) return <div className="ml-1 h-4 w-4 opacity-0 group-hover:opacity-30"><FiChevronUp className="h-4 w-4" /></div>;
    return sortConfig.direction === 'ascending' 
      ? <FiChevronUp className="ml-1 h-4 w-4 text-blue-600 dark:text-blue-400" /> 
      : <FiChevronDown className="ml-1 h-4 w-4 text-blue-600 dark:text-blue-400" />;
  };
  
  // Toggle region selection
  const toggleRegion = (region: string) => {
    setSelectedRegions(prev => 
      prev.includes(region) 
        ? prev.filter(r => r !== region) 
        : [...prev, region]
    );
  };
  
  // Handle category toggle (for any remaining category toggles in the table)
  const toggleCategory = (category: string) => {
    handleCategoryChange(category);
  };
  
  // Handle primary filter toggle (for any remaining filter toggles in the table)
  const togglePrimaryFilter = (filter: string) => {
    handlePrimaryChange(filter);
  };
  
  // Clear all filters
  const clearAllFilters = () => {
    setSelectedRegions([]);
    setPpdaFilter('all');
    setSearchTerm('');
    clearContextFilters();
  };
  
  return (
    <div className="bg-white dark:bg-slate-800 rounded-md shadow overflow-hidden">
      {/* Search and filter controls */}
      <div className="px-6 py-5 bg-white dark:bg-slate-800 border-b border-gray-200 dark:border-slate-700">
        <div className="flex flex-col md:flex-row justify-between items-center gap-4">
          <div className="relative flex-1 max-w-md w-full">
            <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <FiSearch className="h-5 w-5 text-gray-400" />
            </div>
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search suppliers by name, code, location..."
              className="block w-full pl-10 pr-10 py-2.5 border border-gray-300 dark:border-slate-600 rounded-lg leading-5 bg-white dark:bg-slate-700 placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-600 dark:focus:border-blue-600 sm:text-sm shadow-sm transition-colors"
            />
            {searchTerm && (
              <div className="absolute inset-y-0 right-0 pr-3 flex items-center">
                <button
                  onClick={() => setSearchTerm('')}
                  className="text-gray-400 hover:text-gray-500 dark:hover:text-gray-300 p-1 rounded-full hover:bg-gray-100 dark:hover:bg-slate-600 transition-colors"
                  aria-label="Clear search"
                >
                  <FiX className="h-4 w-4" />
                </button>
              </div>
            )}
          </div>
          <div className="flex items-center gap-3 w-full md:w-auto">
            <button
              onClick={() => setShowFilters(!showFilters)}
              className={`flex items-center px-4 py-2.5 border ${showFilters ? 'bg-blue-50 border-blue-300 text-blue-700 dark:bg-blue-900/30 dark:border-blue-700 dark:text-blue-400' : 'border-gray-300 dark:border-slate-600 text-gray-700 dark:text-gray-300'} rounded-lg text-sm font-medium hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors shadow-sm`}
            >
              <FiFilter className="h-4 w-4 mr-2" />
              Filters
              {activeFilterCount > 0 && (
                <span className="ml-2 bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300 px-2 py-0.5 rounded-full text-xs font-semibold">
                  {activeFilterCount}
                </span>
              )}
            </button>
            
            {activeFilterCount > 0 && (
              <button
                onClick={clearAllFilters}
                className="flex items-center px-4 py-2.5 border border-gray-300 dark:border-slate-600 text-gray-700 dark:text-gray-300 rounded-lg text-sm font-medium hover:bg-gray-50 dark:hover:bg-slate-700 transition-colors shadow-sm"
              >
                <FiX className="h-4 w-4 mr-2" />
                Clear filters
              </button>
            )}
          </div>
        </div>
        
        {/* Filter panel - conditionally rendered */}
        {showFilters && (
          <div className="mt-4 p-4 border border-gray-200 dark:border-slate-700 rounded-md">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Primary filters */}
              <div className="p-4 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
                <button
                  onClick={() => toggleSection('primary')}
                  className="w-full flex justify-between items-center text-left"
                  aria-expanded={openSections.primary}
                  aria-controls="filter-section-primary"
                >
                  <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                    <span className="w-1.5 h-4 bg-blue-500 rounded-sm mr-2"></span>
                    Primary filters
                  </h3>
                  {openSections.primary ? <FiChevronUp className="h-5 w-5 text-gray-500 dark:text-gray-400" /> : <FiChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />}
                </button>
                {openSections.primary && (
                  <div id="filter-section-primary" className="mt-3 space-y-2.5">
                    {primaryFilters.map((filter) => (
                      <div key={filter} className="flex items-center">
                        <input
                          id={`filter-${filter}`}
                          type="checkbox"
                          checked={selectedPrimaryFilters.includes(filter)}
                          onChange={() => togglePrimaryFilter(filter)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-800"
                        />
                        <label htmlFor={`filter-${filter}`} className="ml-2.5 text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
                          {filter}
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* PPDA registration */}
              <div className="p-4 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
                <button
                  onClick={() => toggleSection('ppda')}
                  className="w-full flex justify-between items-center text-left"
                  aria-expanded={openSections.ppda}
                  aria-controls="filter-section-ppda"
                >
                  <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                    <span className="w-1.5 h-4 bg-green-500 rounded-sm mr-2"></span>
                    PPDA registration
                  </h3>
                  {openSections.ppda ? <FiChevronUp className="h-5 w-5 text-gray-500 dark:text-gray-400" /> : <FiChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />}
                </button>
                {openSections.ppda && (
                  <div id="filter-section-ppda" className="mt-3 space-y-2.5">
                    <div className="flex items-center">
                      <input
                        id="ppda-all"
                        type="radio"
                        checked={ppdaFilter === 'all'}
                        onChange={() => setPpdaFilter('all')}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-slate-600 dark:bg-slate-800"
                      />
                      <label htmlFor="ppda-all" className="ml-2.5 text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
                        All
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="ppda-yes"
                        type="radio"
                        checked={ppdaFilter === 'yes'}
                        onChange={() => setPpdaFilter('yes')}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-slate-600 dark:bg-slate-800"
                      />
                      <label htmlFor="ppda-yes" className="ml-2.5 text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
                        Yes
                      </label>
                    </div>
                    <div className="flex items-center">
                      <input
                        id="ppda-no"
                        type="radio"
                        checked={ppdaFilter === 'no'}
                        onChange={() => setPpdaFilter('no')}
                        className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 dark:border-slate-600 dark:bg-slate-800"
                      />
                      <label htmlFor="ppda-no" className="ml-2.5 text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
                        No
                      </label>
                    </div>
                  </div>
                )}
              </div>
              
              {/* Region */}
              <div className="p-4 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
                <button
                  onClick={() => toggleSection('region')}
                  className="w-full flex justify-between items-center text-left"
                  aria-expanded={openSections.region}
                  aria-controls="filter-section-region"
                >
                  <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                    <span className="w-1.5 h-4 bg-purple-500 rounded-sm mr-2"></span>
                    Region
                  </h3>
                  {openSections.region ? <FiChevronUp className="h-5 w-5 text-gray-500 dark:text-gray-400" /> : <FiChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />}
                </button>
                {openSections.region && (
                  <div id="filter-section-region" className="mt-3 space-y-2.5">
                    {regions.map((region) => (
                      <div key={region} className="flex items-center">
                        <input
                          id={`region-${region}`}
                          type="checkbox"
                          checked={selectedRegions.includes(region)}
                          onChange={() => toggleRegion(region)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-800"
                        />
                        <label htmlFor={`region-${region}`} className="ml-2.5 text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
                          {region}
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              
              {/* Business category */}
              <div className="p-4 bg-gray-50 dark:bg-slate-700/50 rounded-lg">
                <button
                  onClick={() => toggleSection('category')}
                  className="w-full flex justify-between items-center text-left"
                  aria-expanded={openSections.category}
                  aria-controls="filter-section-category"
                >
                  <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 flex items-center">
                    <span className="w-1.5 h-4 bg-amber-500 rounded-sm mr-2"></span>
                    Business category
                  </h3>
                  {openSections.category ? <FiChevronUp className="h-5 w-5 text-gray-500 dark:text-gray-400" /> : <FiChevronDown className="h-5 w-5 text-gray-500 dark:text-gray-400" />}
                </button>
                {openSections.category && (
                  <div id="filter-section-category" className="mt-3 h-48 overflow-y-auto pr-2 space-y-2.5 custom-scrollbar">
                    {businessCategories.map((category) => (
                      <div key={category} className="flex items-center">
                        <input
                          id={`category-${category}`}
                          type="checkbox"
                          checked={selectedCategories.includes(category)}
                          onChange={() => toggleCategory(category)}
                          className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded dark:border-slate-600 dark:bg-slate-800"
                        />
                        <label htmlFor={`category-${category}`} className="ml-2.5 text-sm text-gray-700 dark:text-gray-300 cursor-pointer">
                          {category}
                        </label>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
            
            <div className="mt-6 flex justify-end">
              <button
                onClick={clearAllFilters}
                className="px-4 py-2 text-sm font-medium text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-slate-700 hover:bg-gray-200 dark:hover:bg-slate-600 rounded-md transition-colors flex items-center"
              >
                <FiX className="h-4 w-4 mr-2" />
                Clear all filters
              </button>
            </div>
          </div>
        )}
      </div>
      
      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200 dark:divide-slate-700 border-collapse">
          <thead className="bg-gray-50 dark:bg-slate-700 sticky top-0 z-10">
            <tr>
              <th 
                scope="col" 
                className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer transition-colors hover:bg-gray-100 dark:hover:bg-slate-600 group"
                onClick={() => handleSort('code')}
              >
                <div className="flex items-center">
                  <span className="group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">Supplier Code</span>
                  {renderSortIndicator('code')}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer transition-colors hover:bg-gray-100 dark:hover:bg-slate-600 group"
                onClick={() => handleSort('name')}
              >
                <div className="flex items-center">
                  <span className="group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">Supplier Name</span>
                  {renderSortIndicator('name')}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer transition-colors hover:bg-gray-100 dark:hover:bg-slate-600 group"
                onClick={() => handleSort('region')}
              >
                <div className="flex items-center">
                  <span className="group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">Region</span>
                  {renderSortIndicator('region')}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer transition-colors hover:bg-gray-100 dark:hover:bg-slate-600 group"
                onClick={() => handleSort('location')}
              >
                <div className="flex items-center">
                  <span className="group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">Location</span>
                  {renderSortIndicator('location')}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer transition-colors hover:bg-gray-100 dark:hover:bg-slate-600 group"
                onClick={() => handleSort('status')}
              >
                <div className="flex items-center">
                  <span className="group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">Status</span>
                  {renderSortIndicator('status')}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer transition-colors hover:bg-gray-100 dark:hover:bg-slate-600 group"
                onClick={() => handleSort('ppda')}
              >
                <div className="flex items-center">
                  <span className="group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">PPDA</span>
                  {renderSortIndicator('ppda')}
                </div>
              </th>
              <th 
                scope="col" 
                className="px-6 py-3.5 text-left text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider cursor-pointer transition-colors hover:bg-gray-100 dark:hover:bg-slate-600 group"
                onClick={() => handleSort('category')}
              >
                <div className="flex items-center">
                  <span className="group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">Category</span>
                  {renderSortIndicator('category')}
                </div>
              </th>
              <th scope="col" className="px-6 py-3.5 text-right text-xs font-semibold text-gray-600 dark:text-gray-200 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-slate-800 divide-y divide-gray-200 dark:divide-slate-700">
            {paginatedSuppliers.length > 0 ? (
              paginatedSuppliers.map((supplier, index) => (
                <tr
                  key={supplier.id}
                  className={`group hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors duration-150 cursor-pointer ${index % 2 === 0 ? 'bg-white dark:bg-slate-800' : 'bg-gray-50 dark:bg-slate-700/20'}`}
                  onClick={() => router.push(`/suppliers/${supplier.id}`)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-blue-600 dark:text-blue-400 group-hover:underline">{supplier.code}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-semibold text-gray-900 dark:text-white">{supplier.name}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 dark:text-gray-300">{supplier.region}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 dark:text-gray-300">{supplier.location}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2.5 py-1 inline-flex text-xs leading-5 font-semibold rounded-full 
                      ${supplier.status === 'Active' ? 'bg-green-100 text-green-800 dark:bg-green-900/50 dark:text-green-200' : 
                        supplier.status === 'Deactivated' ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/50 dark:text-yellow-200' : 
                        'bg-red-100 text-red-800 dark:bg-red-900/50 dark:text-red-200'}`}>
                      {supplier.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 dark:text-gray-300">{supplier.ppda}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-600 dark:text-gray-300 max-w-[200px] truncate" title={supplier.category}>
                      {supplier.category}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <div className="flex justify-end space-x-3">
                      <button 
                        className="p-1.5 rounded-full text-blue-600 hover:bg-blue-50 hover:text-blue-800 dark:text-blue-400 dark:hover:bg-blue-900/20 dark:hover:text-blue-300 transition-colors"
                        title="View supplier details"
                        onClick={(e) => {
                          e.stopPropagation();
                          router.push(`/suppliers/${supplier.id}`);
                        }}
                      >
                        <FiEye className="h-4 w-4" />
                      </button>
                      <button 
                        className="p-1.5 rounded-full text-indigo-600 hover:bg-indigo-50 hover:text-indigo-800 dark:text-indigo-400 dark:hover:bg-indigo-900/20 dark:hover:text-indigo-300 transition-colors"
                        title="Edit supplier"
                      >
                        <FiEdit2 className="h-4 w-4" />
                      </button>
                      <button 
                        className="p-1.5 rounded-full text-red-600 hover:bg-red-50 hover:text-red-800 dark:text-red-400 dark:hover:bg-red-900/20 dark:hover:text-red-300 transition-colors"
                        title="Delete supplier"
                      >
                        <FiTrash2 className="h-4 w-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={8} className="px-6 py-10 text-center text-gray-500 dark:text-gray-400">
                  <div className="flex flex-col items-center justify-center space-y-3">
                    <FiSearch className="h-10 w-10 text-gray-400 dark:text-gray-600" />
                    <p className="text-lg font-medium">No suppliers found</p>
                    <p className="text-sm max-w-md">Try adjusting your search or filter criteria to find what you&apos;re looking for.</p>
                  </div>
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>
      <div className="px-6 py-5 border-t border-gray-200 dark:border-slate-700 bg-gray-50 dark:bg-slate-800/50">
        <div className="flex flex-col md:flex-row items-center justify-between gap-4">
          <div className="text-sm text-gray-600 dark:text-gray-400">
            {filteredSuppliers.length > 0 ? (
              <>
                Showing <span className="font-semibold text-gray-800 dark:text-gray-200">{(currentPage - 1) * rowsPerPage + 1}</span> to <span className="font-semibold text-gray-800 dark:text-gray-200">{Math.min(currentPage * rowsPerPage, filteredSuppliers.length)}</span> of <span className="font-semibold text-gray-800 dark:text-gray-200">{filteredSuppliers.length}</span> suppliers
              </>
            ) : (
              <span>No suppliers found</span>
            )}
          </div>
          
          {/* Rows per page selector */}
          <div className="flex items-center space-x-2">
            <label htmlFor="rows-per-page" className="text-sm text-gray-600 dark:text-gray-400">
              Rows per page:
            </label>
            <select
              id="rows-per-page"
              value={rowsPerPage}
              onChange={(e) => {
                setRowsPerPage(Number(e.target.value));
                setCurrentPage(1); // Reset to first page when changing rows per page
              }}
              className="border border-gray-300 dark:border-slate-600 rounded-md text-sm text-gray-700 dark:text-gray-300 dark:bg-slate-700 py-1.5 px-3 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 dark:focus:ring-blue-600 dark:focus:border-blue-600 outline-none"
            >
              <option value="5">5</option>
              <option value="10">10</option>
              <option value="25">25</option>
              <option value="50">50</option>
            </select>
          </div>
          
          {/* Pagination controls */}
          {totalPages > 0 && (
            <div className="flex items-center space-x-2">
              <button 
                onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                disabled={currentPage === 1}
                className={`px-3 py-1.5 border rounded-md text-sm font-medium transition-colors ${currentPage === 1 
                  ? 'border-gray-200 dark:border-slate-700 text-gray-400 dark:text-gray-600 cursor-not-allowed' 
                  : 'border-gray-300 dark:border-slate-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700'}`}
              >
                Previous
              </button>
              
              {/* Page numbers */}
              <div className="flex items-center space-x-1">
                {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                  // Show pages around current page
                  let pageNum;
                  if (totalPages <= 5) {
                    pageNum = i + 1;
                  } else if (currentPage <= 3) {
                    pageNum = i + 1;
                  } else if (currentPage >= totalPages - 2) {
                    pageNum = totalPages - 4 + i;
                  } else {
                    pageNum = currentPage - 2 + i;
                  }
                  
                  return (
                    <button
                      key={pageNum}
                      onClick={() => setCurrentPage(pageNum)}
                      className={`w-9 h-9 flex items-center justify-center rounded-md text-sm font-medium transition-colors ${
                        currentPage === pageNum
                          ? 'bg-blue-600 text-white shadow-sm'
                          : 'text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700'
                      }`}
                    >
                      {pageNum}
                    </button>
                  );
                })}
              </div>
              
              <button 
                onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                disabled={currentPage === totalPages || totalPages === 0}
                className={`px-3 py-1.5 border rounded-md text-sm font-medium transition-colors ${currentPage === totalPages || totalPages === 0 
                  ? 'border-gray-200 dark:border-slate-700 text-gray-400 dark:text-gray-600 cursor-not-allowed' 
                  : 'border-gray-300 dark:border-slate-600 text-gray-700 dark:text-gray-300 hover:bg-gray-100 dark:hover:bg-slate-700'}`}
              >
                Next
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default SupplierTable;
