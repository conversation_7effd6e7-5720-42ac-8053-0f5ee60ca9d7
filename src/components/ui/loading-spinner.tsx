import { cn } from "@/lib/utils"
import { Logo } from "@/components/logo"

interface LoadingSpinnerProps extends React.HTMLAttributes<HTMLDivElement> {
  size?: 'sm' | 'md' | 'lg'
  text?: string
  fullScreen?: boolean
  variant?: 'default' | 'dots' | 'pulse' | 'orbit' | 'wave' | 'brand'
}

export function LoadingSpinner({
  className,
  size = 'md',
  text,
  fullScreen = false,
  variant = 'default',
  ...props
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: { container: 'h-4 w-4', border: 'border-2', text: 'text-xs' },
    md: { container: 'h-8 w-8', border: 'border-2', text: 'text-sm' },
    lg: { container: 'h-12 w-12', border: 'border-4', text: 'text-base' },
  }

  const sizes = sizeClasses[size]

  // Brand variant with logo
  if (variant === 'brand' && fullScreen) {
    return (
      <div
        className={cn(
          'fixed inset-0 z-50 flex flex-col items-center justify-center',
          'bg-gradient-to-br from-white via-blue-50/30 to-indigo-100/50',
          'dark:from-slate-900 dark:via-slate-800/30 dark:to-slate-700/50',
          'backdrop-blur-sm',
          className
        )}
        {...props}
      >
        {/* Animated background particles */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute -top-4 -left-4 w-72 h-72 bg-blue-300/20 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
          <div className="absolute -top-4 -right-4 w-72 h-72 bg-purple-300/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
          <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
        </div>

        <div className="relative z-10 flex flex-col items-center">
          {/* Logo with pulse animation */}
          <div className="mb-8 animate-pulse-slow">
            <Logo />
          </div>

          {/* Modern spinner */}
          <div className="relative">
            <div className="w-16 h-16 border-4 border-blue-200 dark:border-slate-600 rounded-full animate-spin">
              <div className="absolute top-0 left-0 w-full h-full border-4 border-transparent border-t-blue-600 dark:border-t-blue-400 rounded-full animate-spin"></div>
            </div>
            <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-purple-500 dark:border-r-purple-400 rounded-full animate-spin-reverse"></div>
          </div>

          {/* Loading text with typewriter effect */}
          <div className="mt-6 text-center">
            <p className="text-lg font-medium text-gray-700 dark:text-gray-300 animate-pulse">
              {text || 'Loading...'}
            </p>
            <div className="mt-2 flex justify-center space-x-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce animation-delay-200"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce animation-delay-400"></div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  // Dots variant
  if (variant === 'dots') {
    return (
      <div
        className={cn(
          'flex flex-col items-center justify-center',
          fullScreen ? 'min-h-screen' : '',
          className
        )}
        {...props}
      >
        <div className="flex space-x-2">
          <div className={cn("bg-blue-500 rounded-full animate-bounce",
            size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4'
          )}></div>
          <div className={cn("bg-blue-500 rounded-full animate-bounce animation-delay-200",
            size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4'
          )}></div>
          <div className={cn("bg-blue-500 rounded-full animate-bounce animation-delay-400",
            size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4'
          )}></div>
        </div>
        {text && (
          <p className={cn("mt-3 font-medium text-gray-500 dark:text-gray-400", sizes.text)}>
            {text}
          </p>
        )}
      </div>
    )
  }

  // Pulse variant
  if (variant === 'pulse') {
    return (
      <div
        className={cn(
          'flex flex-col items-center justify-center',
          fullScreen ? 'min-h-screen' : '',
          className
        )}
        {...props}
      >
        <div className="relative">
          <div className={cn(
            "bg-blue-500 rounded-full animate-ping",
            sizes.container
          )}></div>
          <div className={cn(
            "absolute inset-0 bg-blue-600 rounded-full animate-pulse",
            sizes.container
          )}></div>
        </div>
        {text && (
          <p className={cn("mt-3 font-medium text-gray-500 dark:text-gray-400", sizes.text)}>
            {text}
          </p>
        )}
      </div>
    )
  }

  // Orbit variant
  if (variant === 'orbit') {
    return (
      <div
        className={cn(
          'flex flex-col items-center justify-center',
          fullScreen ? 'min-h-screen' : '',
          className
        )}
        {...props}
      >
        <div className="relative">
          <div className={cn(
            "border-2 border-gray-200 dark:border-gray-600 rounded-full",
            size === 'sm' ? 'w-8 h-8' : size === 'md' ? 'w-12 h-12' : 'w-16 h-16'
          )}>
            <div className={cn(
              "absolute top-0 left-0 w-3 h-3 bg-blue-500 rounded-full animate-orbit",
              size === 'sm' ? 'w-2 h-2' : size === 'md' ? 'w-3 h-3' : 'w-4 h-4'
            )}></div>
          </div>
        </div>
        {text && (
          <p className={cn("mt-3 font-medium text-gray-500 dark:text-gray-400", sizes.text)}>
            {text}
          </p>
        )}
      </div>
    )
  }

  // Wave variant
  if (variant === 'wave') {
    return (
      <div
        className={cn(
          'flex flex-col items-center justify-center',
          fullScreen ? 'min-h-screen' : '',
          className
        )}
        {...props}
      >
        <div className="flex space-x-1">
          {[...Array(5)].map((_, i) => (
            <div
              key={i}
              className={cn(
                "bg-blue-500 animate-wave",
                size === 'sm' ? 'w-1 h-4' : size === 'md' ? 'w-1.5 h-6' : 'w-2 h-8'
              )}
              style={{
                animationDelay: `${i * 0.1}s`
              }}
            ></div>
          ))}
        </div>
        {text && (
          <p className={cn("mt-3 font-medium text-gray-500 dark:text-gray-400", sizes.text)}>
            {text}
          </p>
        )}
      </div>
    )
  }

  // Default variant (enhanced)
  return (
    <div
      className={cn(
        'flex flex-col items-center justify-center',
        fullScreen ? 'min-h-screen' : '',
        className
      )}
      {...props}
    >
      <div className="relative">
        <div
          className={cn(
            'animate-spin rounded-full border-solid border-blue-200 dark:border-slate-600 border-t-blue-600 dark:border-t-blue-400',
            sizes.container,
            sizes.border
          )}
          style={{
            animationDuration: '1s',
            animationTimingFunction: 'cubic-bezier(0.4, 0, 0.2, 1)',
          }}
          role="status"
        >
          <span className="sr-only">Loading...</span>
        </div>
        {size !== 'sm' && (
          <div className="absolute inset-0 flex items-center justify-center">
            <div className={cn(
              "w-2 h-2 bg-blue-600 dark:bg-blue-400 rounded-full animate-pulse",
              size === 'lg' ? 'w-3 h-3' : 'w-2 h-2'
            )}></div>
          </div>
        )}
      </div>
      {text && (
        <p className={cn("mt-3 font-medium text-gray-500 dark:text-gray-400", sizes.text)}>
          {text}
        </p>
      )}
    </div>
  )
}
