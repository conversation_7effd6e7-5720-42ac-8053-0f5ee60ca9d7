'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { FiUsers, FiX, FiPlus } from 'react-icons/fi';
import { Badge } from '@/components/ui/badge';
import { api } from '@/services/api';
import axios from 'axios';
import { useGlobalModal } from '@/hooks/useModal';

interface InviteSuppliersModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const InviteSuppliersModal: React.FC<InviteSuppliersModalProps> = ({ isOpen, onClose }) => {
  const [emailInput, setEmailInput] = useState('');
  const [emailList, setEmailList] = useState<string[]>([]);
  const [isSending, setIsSending] = useState(false);
  const [error, setError] = useState('');

  // Global modal hook
  const { showSuccess, showError, showWarning } = useGlobalModal();

  // Reset state when modal closes
  useEffect(() => {
    if (!isOpen) {
      setEmailInput('');
      setEmailList([]);
      setError('');
    }
  }, [isOpen]);

  const validateEmail = (email: string) => {
    const re = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    return re.test(email);
  };

  const handleAddEmail = () => {
    const trimmedEmail = emailInput.trim();
    
    if (!trimmedEmail) {
      return;
    }
    
    // Check if input contains multiple emails separated by commas
    if (trimmedEmail.includes(',')) {
      const multipleEmails = trimmedEmail.split(',').map(e => e.trim()).filter(e => e);
      
      // Validate each email
      const validEmails: string[] = [];
      const invalidEmails: string[] = [];
      
      multipleEmails.forEach(email => {
        if (validateEmail(email) && !emailList.includes(email)) {
          validEmails.push(email);
        } else if (email) {
          invalidEmails.push(email);
        }
      });
      
      if (invalidEmails.length > 0) {
        setError(`Invalid email(s): ${invalidEmails.join(', ')}`);
      } else {
        setError('');
      }
      
      setEmailList(prev => [...prev, ...validEmails]);
    } else {
      // Single email validation
      if (validateEmail(trimmedEmail) && !emailList.includes(trimmedEmail)) {
        setEmailList(prev => [...prev, trimmedEmail]);
        setError('');
      } else if (emailList.includes(trimmedEmail)) {
        setError('Email already added');
      } else {
        setError('Please enter a valid email address');
      }
    }
    
    setEmailInput('');
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddEmail();
    } else if (e.key === ',' || e.key === ' ') {
      if (emailInput.trim()) {
        e.preventDefault();
        handleAddEmail();
      }
    }
  };

  const handleRemoveEmail = (emailToRemove: string) => {
    setEmailList(emailList.filter(email => email !== emailToRemove));
  };

  const handleSendInvitation = async () => {
    // Set loading state
    setIsSending(true);

    try {
      // Validate emails before sending
      if (!emailList || emailList.length === 0) {
        setIsSending(false);
        showWarning('No Email Addresses', 'Please add at least one email address before sending invitations.');
        return;
      }

      const validEmails = emailList.filter(email => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      });

      if (validEmails.length === 0) {
        setIsSending(false);
        showError('Invalid Email Addresses', 'No valid email addresses found. Please check your input and ensure all emails are properly formatted.');
        return;
      }

      console.log('Sending invitations to:', validEmails);

      // Get the token from localStorage
      const token = localStorage.getItem('access_token');
      if (!token) {
        throw new Error('No authentication token found. Please log in again.');
      }

      // Get current user data - first try from localStorage, then fetch from API
      let userOrgId = null;
      try {
        const userData = JSON.parse(localStorage.getItem('user') || '{}');
        // Check both spellings: organization_id (US) and organisation_id (UK)
        if (userData?.organization_id) {
          userOrgId = userData.organization_id;
          console.log('Found organization_id in localStorage:', userOrgId);
        } else if (userData?.organisation_id) {
          userOrgId = userData.organisation_id;
          console.log('Found organisation_id in localStorage:', userOrgId);
        }
      } catch (error) {
        console.error('Error loading user data from localStorage:', error);
      }

      // If no organization_id found in localStorage, fetch from API
      if (!userOrgId) {
        console.log('No organization_id in localStorage, fetching from /users/me...');
        try {
          const userResponse = await api.get('/api/proxy/auth/users/me', {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Accept': 'application/json'
            }
          });

          console.log('API response data:', userResponse.data);

          // Check both spellings: organization_id (US) and organisation_id (UK)
          const userData = userResponse.data as any;
          if (userData?.organization_id) {
            userOrgId = userData.organization_id;
            console.log('Fetched organization_id from API:', userOrgId);
          } else if (userData?.organisation_id) {
            userOrgId = userData.organisation_id;
            console.log('Fetched organisation_id from API:', userOrgId);
          }

          if (userOrgId) {
            // Update localStorage with the fresh user data
            localStorage.setItem('user', JSON.stringify(userResponse.data));
          }
        } catch (apiError) {
          console.error('Error fetching user data from API:', apiError);
        }
      }

      if (!userOrgId) {
        throw new Error('Could not determine organization ID. Please try logging in again.');
      }

      console.log('Using organization ID:', userOrgId);
      
      // Create payload according to API requirements
      // Note: API expects organization_id (US spelling) but /users/me returns organisation_id (UK spelling)
      interface InvitationPayload {
        emails: string[];
        role: string;
        message: string;
        expires_in_days: number;
        organization_id?: string | null;
      }

      const payload: InvitationPayload = {
        emails: validEmails,
        role: 'supplier',
        message: 'You have been invited to join our supplier network.',
        expires_in_days: 7
      };

      // Add organization_id if it exists (API expects US spelling)
      if (userOrgId) {
        payload.organization_id = userOrgId;
        console.log('Added organization_id to payload:', userOrgId);
      }
      
      // Use the API proxy route to avoid CORS issues
      console.log('Sending invitation through API proxy route...');
      console.log('Invitation payload:', JSON.stringify(payload, null, 2));

      // Try multiple invitation methods
      let result;
      let invitationMethod = 'unknown';

      // Method 1: Try the standard API endpoints
      try {
        console.log('Trying standard invitation API...');
        const response = await fetch('/api/proxy/invitations/bulk', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Bearer ${token}`
          },
          body: JSON.stringify(payload)
        });

        if (response.ok) {
          const data = await response.json();
          console.log('📊 Proxy route response:', data);

          // Check if the proxy route actually succeeded or if all methods failed
          if (data.successful > 0) {
            result = { data };
            invitationMethod = 'standard';
            console.log('✅ Standard API invitations sent:', result);
          } else {
            // All methods in the proxy route failed, throw error to trigger custom system
            console.log('🔄 All proxy methods failed, triggering custom system...');
            throw new Error(`Standard API failed: All proxy methods failed. ${data.results?.[0]?.details || 'Unknown error'}`);
          }
        } else {
          throw new Error(`Standard API failed: ${response.status} ${response.statusText}`);
        }
      } catch (standardError: any) {
        console.log('🔄 Standard API failed, trying custom invitation system...');
        console.log('Standard error:', standardError.message);

        // Method 2: Try custom invitation system
        try {
          console.log('📧 Creating custom invitations with SMTP email...');
          console.log('📧 Payload:', payload);

          const customResponse = await fetch('/api/custom-invitations', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'Authorization': `Bearer ${token}`
            },
            body: JSON.stringify(payload)
          });

          console.log('📧 Custom invitation response status:', customResponse.status);

          if (customResponse.ok) {
            const customData = await customResponse.json();
            console.log('📧 Custom invitation response data:', customData);
            result = { data: customData };
            invitationMethod = 'custom';
            console.log('✅ Custom invitations created successfully with SMTP emails!');
          } else {
            const errorText = await customResponse.text();
            console.error('❌ Custom invitation response error:', errorText);
            throw new Error(`Custom system failed: ${customResponse.status} ${customResponse.statusText} - ${errorText}`);
          }
        } catch (customError: any) {
          console.error('❌ Both invitation methods failed:', {
            standard: standardError.message,
            custom: customError.message
          });

          let errorMessage = 'Failed to send invitations using all available methods.\n\n';
          errorMessage += `Standard API: ${standardError.message}\n`;
          errorMessage += `Custom System: ${customError.message}`;

          showError('Invitation Failed', errorMessage);
          throw new Error(errorMessage);
        }
      }
      
      // Process results
      if (!result?.data) {
        throw new Error('Failed to send invitations: No response data');
      }
      
      // Handle response from the API
      const data = result.data;
      const successful = data.successful || 0;
      const failed = data.failed || 0;
      const results = data.results || [];

      console.log('Invitation results:', results);

      // Reset states
      setIsSending(false);

      if (failed > 0) {
        // Some invitations failed - show detailed error information
        const failedEmails = results
          .filter((r: any) => !r.success)
          .map((r: any) => {
            let errorMsg = `${r.email}: ${r.error || 'Unknown error'}`;
            if (r.details) {
              try {
                const details = typeof r.details === 'string' ? JSON.parse(r.details) : r.details;
                if (details.detail) {
                  errorMsg += ` (${details.detail})`;
                }
              } catch (e) {
                errorMsg += ` (${r.details})`;
              }
            }
            return errorMsg;
          })
          .join('\n');

        console.error('Failed invitations:', failedEmails);

        let successMessage = '';
        if (successful > 0) {
          successMessage = `${successful} invitation(s) sent successfully using ${invitationMethod} method.\n`;
        }

        showWarning('Partial Success', `${successMessage}${failed} invitation(s) failed:\n\n${failedEmails}`);
      } else {
        // All invitations sent successfully
        const methodText = invitationMethod === 'custom' ? ' using custom invitation system' : '';
        showSuccess('Invitations Sent!', `Invitations sent successfully to ${successful} supplier(s)${methodText}!`);

        // Show additional info for custom invitations
        if (invitationMethod === 'custom') {
          console.log('Custom invitation URLs created. Recipients can use these to join.');
        }

        // Clear the form only on success
        setEmailList([]);
        setEmailInput('');
        onClose();
      }
    } catch (error: any) {
      console.error('Error sending invitations:', error);
      showError('Invitation Error', `Error sending invitations: ${error.message || 'Unknown error'}`);
      setIsSending(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[500px] p-0 overflow-hidden rounded-xl shadow-xl">
        <DialogHeader className="text-center bg-gradient-to-r from-[#18546c] to-[#1a6985] text-white p-6">
          <div className="inline-flex justify-center items-center mb-4 bg-white/20 backdrop-blur-sm p-3 rounded-full shadow-lg">
            <FiUsers className="h-5 w-5 text-white" />
          </div>
          <DialogTitle className="text-2xl font-bold mb-2">Invite New Suppliers</DialogTitle>
          <DialogDescription className="text-white/90 text-base">
            Expand your supplier network
          </DialogDescription>
        </DialogHeader>
        
        <div className="p-6 space-y-4">
            <div className="grid w-full items-center gap-3">
              <Label htmlFor="emails" className="text-left font-medium text-gray-700">
                Supplier Email Addresses
              </Label>
              
              {/* Email input with add button */}
              <div className="flex gap-2">
                <Input
                  id="emails"
                  type="email"
                  placeholder="<EMAIL>"
                  value={emailInput}
                  onChange={(e: React.ChangeEvent<HTMLInputElement>) => setEmailInput(e.target.value)}
                  onKeyDown={handleKeyDown}
                  className="flex-1 h-10 px-3 text-sm rounded-md border-gray-300 focus:border-[#18546c] focus:ring-[#18546c] transition-all duration-200"
                />
                <Button 
                  type="button" 
                  onClick={handleAddEmail}
                  className="bg-[#18546c] hover:bg-[#1a6985] text-white h-10 px-3 rounded-md transition-all duration-200 shadow-sm hover:shadow-md"
                >
                  <FiPlus className="h-4 w-4" />
                </Button>
              </div>
              
              {/* Error message */}
              {error && (
                <p className="text-sm text-red-500 font-medium flex items-center gap-2">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
                  </svg>
                  {error}
                </p>
              )}
              
              {/* Email tags */}
              {emailList.length > 0 && (
                <div className="flex flex-wrap gap-2 mt-3 p-3 bg-gray-50 rounded-md border border-gray-100 max-h-[120px] overflow-y-auto">
                  {emailList.map((email, index) => (
                    <Badge 
                      key={index} 
                      className="bg-[#e8f0f2] text-[#18546c] hover:bg-[#d8e8ec] px-2 py-1 flex items-center gap-1 text-xs rounded-full transition-all duration-200"
                    >
                      {email}
                      <button 
                        type="button" 
                        onClick={() => handleRemoveEmail(email)}
                        className="ml-1 text-[#18546c] hover:text-red-500 focus:outline-none rounded-full hover:bg-white/50 p-1 transition-all duration-200"
                      >
                        <FiX className="h-4 w-4" />
                        <span className="sr-only">Remove</span>
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
              
              <p className="text-xs text-gray-500 mt-1 flex items-center gap-1">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Press Enter or comma to add multiple emails
              </p>
            </div>
        </div>
        
        <DialogFooter className="bg-gray-50 p-4 flex justify-end gap-2 border-t">
          <Button 
            variant="outline" 
            onClick={onClose} 
            className="border border-gray-300 bg-white text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-all duration-200 text-sm font-medium py-1 px-4 h-9 rounded-md"
          >
            Cancel
          </Button>
          <Button 
            onClick={handleSendInvitation} 
            disabled={emailList.length === 0 || isSending} 
            className={`bg-[#18546c] text-white hover:bg-[#1a6985] transition-all duration-200 shadow-sm hover:shadow-md text-sm font-medium py-1 px-4 h-9 rounded-md ${emailList.length === 0 || isSending ? 'opacity-70 cursor-not-allowed' : ''}`}
          >
            {isSending ? (
              <span className="flex items-center gap-2">
                <svg className="animate-spin h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Sending...
              </span>
            ) : (
              <span className="flex items-center gap-2">
                {`Send ${emailList.length > 0 ? `${emailList.length} ` : ''}Invitation${emailList.length !== 1 ? 's' : ''}`}
              </span>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default InviteSuppliersModal;
