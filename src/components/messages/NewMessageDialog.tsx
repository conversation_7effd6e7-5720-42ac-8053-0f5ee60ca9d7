'use client';

import React, { useState, useRef, useEffect } from 'react';
import { XMarkIcon, PaperClipIcon, MagnifyingGlassIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { useMessages } from '@/context/MessageContext';
import { Recipient } from '@/services/message.service';

interface NewMessageDialogProps {
  isOpen: boolean;
  onClose: () => void;
  replyTo?: {
    messageId: string;
    subject: string;
    caseReference: string;
  };
}

export const NewMessageDialog: React.FC<NewMessageDialogProps> = ({
  isOpen,
  onClose,
  replyTo
}) => {
  const { createMessage, sendMessage, searchRecipients, uploadAttachment } = useMessages();
  
  const [to, setTo] = useState<string>('');
  const [caseReference, setCaseReference] = useState(replyTo?.caseReference || '');
  const [subject, setSubject] = useState(replyTo?.subject ? `Re: ${replyTo.subject}` : '');
  const [body, setBody] = useState('');
  const [attachments, setAttachments] = useState<File[]>([]);
  const [recipients, setRecipients] = useState<Recipient[]>([]);
  const [searchResults, setSearchResults] = useState<Recipient[]>([]);
  const [isSearching, setIsSearching] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [errors, setErrors] = useState<Record<string, string>>({});
  
  const fileInputRef = useRef<HTMLInputElement>(null);
  const searchTimeoutRef = useRef<NodeJS.Timeout | undefined>(undefined);

  // Validate form
  const validateForm = () => {
    const newErrors: Record<string, string> = {};
    
    if (!to.trim()) {
      newErrors.to = 'Recipient is required';
    }
    if (!caseReference.trim()) {
      newErrors.caseReference = 'Case reference is required';
    }
    if (!subject.trim()) {
      newErrors.subject = 'Subject is required';
    }
    if (!body.trim()) {
      newErrors.body = 'Message body is required';
    }
    
    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Handle recipient search
  const handleRecipientSearch = async (query: string) => {
    if (query.length < 2) {
      setSearchResults([]);
      return;
    }

    setIsSearching(true);
    try {
      const results = await searchRecipients(query);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching recipients:', error);
    } finally {
      setIsSearching(false);
    }
  };

  // Handle recipient input change
  const handleToChange = (value: string) => {
    setTo(value);
    
    // Clear previous timeout
    if (searchTimeoutRef.current) {
      clearTimeout(searchTimeoutRef.current);
    }
    
    // Set new timeout for search
    searchTimeoutRef.current = setTimeout(() => {
      handleRecipientSearch(value);
    }, 300);
  };

  // Add recipient
  const addRecipient = (recipient: Recipient) => {
    const recipientString = recipient.supplierCode 
      ? `${recipient.username} (${recipient.supplierCode}) <${recipient.email}>`
      : `${recipient.username} <${recipient.email}>`;
    
    setRecipients(prev => [...prev, recipient]);
    setTo(prev => {
      const currentRecipients = prev.split(',').map(r => r.trim()).filter(r => r);
      return [...currentRecipients, recipientString].join(', ');
    });
    setSearchResults([]);
  };

  // Remove recipient
  const removeRecipient = (recipientId: string) => {
    const recipient = recipients.find(r => r.id === recipientId);
    if (recipient) {
      setRecipients(prev => prev.filter(r => r.id !== recipientId));
      setTo(prev => {
        const recipientString = recipient.supplierCode 
          ? `${recipient.username} (${recipient.supplierCode}) <${recipient.email}>`
          : `${recipient.username} <${recipient.email}>`;
        return prev.split(',').map(r => r.trim()).filter(r => r !== recipientString).join(', ');
      });
    }
  };

  // Handle file attachment
  const handleFileAttachment = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setAttachments(prev => [...prev, ...files]);
  };

  // Remove attachment
  const removeAttachment = (index: number) => {
    setAttachments(prev => prev.filter((_, i) => i !== index));
  };

  // Handle drag and drop
  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    const files = Array.from(e.dataTransfer.files);
    setAttachments(prev => [...prev, ...files]);
  };

  // Handle form submission
  const handleSubmit = async (action: 'save' | 'send') => {
    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    try {
      // Upload attachments
      const uploadedAttachments: File[] = [];
      for (const file of attachments) {
        try {
          await uploadAttachment(file);
          uploadedAttachments.push(file);
        } catch (error) {
          console.error('Error uploading attachment:', error);
        }
      }

      // Create message
      const messageData = {
        to: to.split(',').map(r => r.trim()).filter(r => r),
        caseReference,
        subject,
        body,
        attachments: uploadedAttachments,
      };

      const newMessage = await createMessage(messageData);

      if (action === 'send') {
        await sendMessage(newMessage.id);
      }

      onClose();
    } catch (error) {
      console.error('Error creating/sending message:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Reset form when dialog opens/closes
  useEffect(() => {
    if (!isOpen) {
      setTo('');
      setCaseReference(replyTo?.caseReference || '');
      setSubject(replyTo?.subject ? `Re: ${replyTo.subject}` : '');
      setBody('');
      setAttachments([]);
      setRecipients([]);
      setSearchResults([]);
      setErrors({});
    }
  }, [isOpen, replyTo]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (searchTimeoutRef.current) {
        clearTimeout(searchTimeoutRef.current);
      }
    };
  }, []);

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl w-full max-w-2xl max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200 dark:border-gray-700">
          <h2 className="text-lg font-semibold text-gray-900 dark:text-white">
            {replyTo ? 'Reply to Message' : 'New Message'}
          </h2>
          <Button
            variant="ghost"
            size="sm"
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <XMarkIcon className="h-5 w-5" />
          </Button>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          <div className="space-y-4">
            {/* To Field */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                To *
              </label>
              <div className="relative">
                <Input
                  type="text"
                  value={to}
                  onChange={(e) => handleToChange(e.target.value)}
                  placeholder="Enter recipient email, username, or supplier code"
                  className={errors.to ? 'border-red-500' : ''}
                />
                {isSearching && (
                  <div className="absolute right-3 top-1/2 -translate-y-1/2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
                  </div>
                )}
              </div>
              {errors.to && (
                <p className="text-sm text-red-600 mt-1">{errors.to}</p>
              )}
              
              {/* Search Results */}
              {searchResults.length > 0 && (
                <div className="mt-2 border border-gray-200 dark:border-gray-700 rounded-md bg-white dark:bg-gray-700 max-h-40 overflow-y-auto">
                  {searchResults.map((recipient) => (
                    <div
                      key={recipient.id}
                      className="p-2 hover:bg-gray-50 dark:hover:bg-gray-600 cursor-pointer border-b border-gray-100 dark:border-gray-600 last:border-b-0"
                      onClick={() => addRecipient(recipient)}
                    >
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="text-sm font-medium text-gray-900 dark:text-white">
                            {recipient.username}
                          </p>
                          <p className="text-xs text-gray-500 dark:text-gray-400">
                            {recipient.email}
                          </p>
                        </div>
                        {recipient.supplierCode && (
                          <Badge variant="secondary" className="text-xs">
                            {recipient.supplierCode}
                          </Badge>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Selected Recipients */}
              {recipients.length > 0 && (
                <div className="mt-2 flex flex-wrap gap-2">
                  {recipients.map((recipient) => (
                    <Badge
                      key={recipient.id}
                      variant="default"
                      className="text-xs"
                    >
                      {recipient.username}
                      <button
                        onClick={() => removeRecipient(recipient.id)}
                        className="ml-1 hover:text-red-500"
                      >
                        ×
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            {/* Case Reference */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Case Reference *
              </label>
              <Input
                type="text"
                value={caseReference}
                onChange={(e) => setCaseReference(e.target.value)}
                placeholder="Enter case reference"
                className={errors.caseReference ? 'border-red-500' : ''}
              />
              {errors.caseReference && (
                <p className="text-sm text-red-600 mt-1">{errors.caseReference}</p>
              )}
            </div>

            {/* Subject */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Subject *
              </label>
              <Input
                type="text"
                value={subject}
                onChange={(e) => setSubject(e.target.value)}
                placeholder="Enter subject"
                className={errors.subject ? 'border-red-500' : ''}
              />
              {errors.subject && (
                <p className="text-sm text-red-600 mt-1">{errors.subject}</p>
              )}
            </div>

            {/* Message Body */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Message *
              </label>
              <Textarea
                value={body}
                onChange={(e) => setBody(e.target.value)}
                placeholder="Enter your message"
                rows={6}
                className={errors.body ? 'border-red-500' : ''}
              />
              {errors.body && (
                <p className="text-sm text-red-600 mt-1">{errors.body}</p>
              )}
            </div>

            {/* File Attachments */}
            <div>
              <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">
                Attachments
              </label>
              <div
                className="border-2 border-dashed border-gray-300 dark:border-gray-600 rounded-lg p-4 text-center hover:border-gray-400 dark:hover:border-gray-500 transition-colors"
                onDragOver={handleDragOver}
                onDrop={handleDrop}
              >
                <PaperClipIcon className="mx-auto h-8 w-8 text-gray-400 mb-2" />
                <p className="text-sm text-gray-600 dark:text-gray-400 mb-2">
                  Drag and drop files here, or click to browse
                </p>
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => fileInputRef.current?.click()}
                >
                  Browse Files
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  onChange={handleFileAttachment}
                  className="hidden"
                />
              </div>

              {/* Attachment List */}
              {attachments.length > 0 && (
                <div className="mt-2 space-y-2">
                  {attachments.map((file, index) => (
                    <div
                      key={index}
                      className="flex items-center justify-between p-2 bg-gray-50 dark:bg-gray-700 rounded-md"
                    >
                      <div className="flex items-center space-x-2">
                        <PaperClipIcon className="h-4 w-4 text-gray-400" />
                        <span className="text-sm text-gray-700 dark:text-gray-300">
                          {file.name}
                        </span>
                        <span className="text-xs text-gray-500 dark:text-gray-400">
                          ({(file.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <button
                        onClick={() => removeAttachment(index)}
                        className="text-red-500 hover:text-red-700"
                      >
                        ×
                      </button>
                    </div>
                  ))}
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end space-x-3 p-6 border-t border-gray-200 dark:border-gray-700">
          <Button
            variant="outline"
            onClick={onClose}
            disabled={isSubmitting}
          >
            Cancel
          </Button>
          <Button
            variant="outline"
            onClick={() => handleSubmit('save')}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Saving...' : 'Save as Draft'}
          </Button>
          <Button
            onClick={() => handleSubmit('send')}
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Sending...' : 'Send'}
          </Button>
        </div>
      </div>
    </div>
  );
}; 
