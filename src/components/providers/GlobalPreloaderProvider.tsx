'use client';

import React from 'react';
import { GlobalPreloader } from '@/components/global/GlobalPreloader';
import { useGlobalPreloader } from '@/hooks/useGlobalPreloader';

interface GlobalPreloaderProviderProps {
  children: React.ReactNode;
}

export function GlobalPreloaderProvider({ children }: GlobalPreloaderProviderProps) {
  const { isLoading, text, variant } = useGlobalPreloader();

  return (
    <>
      {children}
      <GlobalPreloader 
        isLoading={isLoading} 
        text={text} 
        variant={variant}
      />
    </>
  );
}
