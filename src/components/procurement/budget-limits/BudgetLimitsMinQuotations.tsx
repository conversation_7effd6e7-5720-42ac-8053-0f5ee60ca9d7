'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import {
  BudgetLimitFormData,
  SUPPORTED_CURRENCIES,
  APPROVAL_LEVELS,
  MIN_QUOTATIONS_OPTIONS,
  canManageProcurement,
} from '../../../types/procurement.types';
import { 
  procurementApi,
  BudgetLimit,
  BudgetLimitCreate,
  BudgetLimitUpdate 
} from '../../../services/procurement.api';

export default function BudgetLimitsMinQuotations() {
  const { user } = useAuth();
  const [budgetLimits, setBudgetLimits] = useState<BudgetLimit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [formData, setFormData] = useState<BudgetLimitFormData>({
    currency: 'UGX',
    amount_threshold: 0,
    approver_level_required: 1,
    min_quotations_required: 3,
    override_case_reference: '',
    is_general_rule: true,
  });

  // Check permissions
  const canEdit = canManageProcurement(user?.role);

  // Load budget limits on component mount
  useEffect(() => {
    loadBudgetLimits();
  }, []);

  const loadBudgetLimits = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await procurementApi.budgetLimits.list();
      setBudgetLimits(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load budget limits');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async () => {
    try {
      setError(null);
      
      // Validation
      if (!formData.currency || formData.amount_threshold <= 0 || !formData.approver_level_required || !formData.min_quotations_required) {
        setError('Currency, amount threshold (> 0), approver level, and minimum quotations are required');
        return;
      }

      const createData: BudgetLimitCreate = {
        currency: formData.currency,
        amount_threshold: formData.amount_threshold,
        approver_level_required: formData.approver_level_required,
        min_quotations_required: formData.min_quotations_required,
        is_general_rule: formData.is_general_rule,
      };

      const newBudgetLimit = await procurementApi.budgetLimits.create(createData);
      setBudgetLimits([...budgetLimits, newBudgetLimit]);
      
      // Reset form
      setFormData({
        currency: 'UGX',
        amount_threshold: 0,
        approver_level_required: 1,
        min_quotations_required: 3,
        override_case_reference: '',
        is_general_rule: true,
      });
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create budget limit');
    }
  };

  const handleUpdate = async (id: number, updateData: BudgetLimitUpdate) => {
    try {
      setError(null);
      
      const updatedBudgetLimit = await procurementApi.budgetLimits.update(id, updateData);
      setBudgetLimits(budgetLimits.map(limit => 
        limit.id === id ? updatedBudgetLimit : limit
      ));
      
      setEditingId(null);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update budget limit');
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this budget limit?')) {
      return;
    }

    try {
      setError(null);
      
      await procurementApi.budgetLimits.delete(id);
      setBudgetLimits(budgetLimits.filter(limit => limit.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete budget limit');
    }
  };

  const handleSaveAndPublish = async () => {
    try {
      setError(null);
      // This could trigger a publish endpoint if needed
      alert('Settings saved and published successfully');
      await loadBudgetLimits();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save and publish');
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', { style: 'currency', currency }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit'
    });
  };

  return (
    <div className="budget-limits-min-quotations p-6">
      <div className="mb-6">
        <h2 className="text-xl font-bold mb-2">Budget Approval Limits</h2>
        <p className="text-gray-600">Manage budget approval limits and minimum quotation requirements</p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Create New Budget Limit Form */}
      {canEdit && (
        <div className="bg-gray-50 p-4 rounded mb-6">
          <h3 className="text-lg font-medium mb-4">Add New Budget Limit</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
            <div>
              <label className="block text-sm font-medium mb-1">Currency *</label>
              <select
                value={formData.currency}
                onChange={(e) => setFormData({...formData, currency: e.target.value})}
                className="w-full p-2 border border-gray-300 rounded"
              >
                {SUPPORTED_CURRENCIES.map(currency => (
                  <option key={currency} value={currency}>{currency}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Amount Threshold *</label>
              <input
                type="number"
                min="1"
                value={formData.amount_threshold}
                onChange={(e) => setFormData({...formData, amount_threshold: parseFloat(e.target.value) || 0})}
                className="w-full p-2 border border-gray-300 rounded"
                placeholder="Enter amount"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Approver Level *</label>
              <select
                value={formData.approver_level_required}
                onChange={(e) => setFormData({...formData, approver_level_required: parseInt(e.target.value)})}
                className="w-full p-2 border border-gray-300 rounded"
              >
                {APPROVAL_LEVELS.map(level => (
                  <option key={level} value={level}>Level {level}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium mb-1">Min Quotations *</label>
              <select
                value={formData.min_quotations_required}
                onChange={(e) => setFormData({...formData, min_quotations_required: parseInt(e.target.value)})}
                className="w-full p-2 border border-gray-300 rounded"
              >
                {MIN_QUOTATIONS_OPTIONS.map(option => (
                  <option key={option} value={option}>{option}</option>
                ))}
              </select>
            </div>
            <div className="flex items-end">
              <button
                onClick={handleCreate}
                className="w-full bg-blue-600 text-white p-2 rounded hover:bg-blue-700"
              >
                Add Limit
              </button>
            </div>
          </div>
          <div className="mt-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.is_general_rule}
                onChange={(e) => setFormData({...formData, is_general_rule: e.target.checked})}
                className="mr-2"
              />
              Is General Rule
            </label>
          </div>
        </div>
      )}

      {/* Budget Limits Table */}
      <div className="bg-white border border-gray-200 rounded">
        <div className="px-4 py-3 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Current Budget Limits</h3>
            {canEdit && (
              <button
                onClick={handleSaveAndPublish}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                Save & Publish
              </button>
            )}
          </div>
        </div>

        {loading ? (
          <div className="p-8 text-center text-gray-500">Loading...</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Currency</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Amount Threshold</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Approver Level</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Min Quotations</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">General Rule</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Created</th>
                  {canEdit && <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Actions</th>}
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {budgetLimits.map((limit) => (
                  <tr key={limit.id}>
                    <td className="px-4 py-2 text-sm font-medium">{limit.currency}</td>
                    <td className="px-4 py-2 text-sm">{formatCurrency(limit.amount_threshold, limit.currency)}</td>
                    <td className="px-4 py-2 text-sm">Level {limit.approver_level_required}</td>
                    <td className="px-4 py-2 text-sm">{limit.min_quotations_required}</td>
                    <td className="px-4 py-2 text-sm">
                      <span className={`px-2 py-1 rounded text-xs ${
                        limit.is_general_rule 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-gray-100 text-gray-800'
                      }`}>
                        {limit.is_general_rule ? 'Yes' : 'No'}
                      </span>
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-500">
                      {formatDate(limit.created_at)}
                    </td>
                    {canEdit && (
                      <td className="px-4 py-2 text-sm space-x-2">
                        <button
                          onClick={() => handleDelete(limit.id)}
                          className="text-red-600 hover:text-red-800"
                        >
                          Delete
                        </button>
                      </td>
                    )}
                  </tr>
                ))}
                {budgetLimits.length === 0 && (
                  <tr>
                    <td colSpan={canEdit ? 7 : 6} className="px-4 py-8 text-center text-gray-500">
                      No budget limits configured yet
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>
    </div>
  );
} 
