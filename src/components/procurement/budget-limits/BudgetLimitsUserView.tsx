'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import { canOverrideQuotations } from '../../../types/procurement.types';
import { procurementApi, BudgetLimit } from '../../../services/procurement.api';

export default function BudgetLimitsUserView() {
  const { user } = useAuth();
  const [budgetLimits, setBudgetLimits] = useState<BudgetLimit[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showOverrideForm, setShowOverrideForm] = useState(false);
  const [overrideFormData, setOverrideFormData] = useState({
    case_id: '',
    override_case_reference: '',
    justification: '',
  });

  // Check if user has override permissions
  const canOverride = canOverrideQuotations(user?.role);

  // Load budget limits on component mount
  useEffect(() => {
    loadBudgetLimits();
  }, []);

  const loadBudgetLimits = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await procurementApi.budgetLimits.list();
      setBudgetLimits(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load budget limits');
    } finally {
      setLoading(false);
    }
  };

  const handleOverrideSubmit = async () => {
    try {
      setError(null);
      
      // Validate override form
      if (!overrideFormData.case_id || !overrideFormData.override_case_reference) {
        setError('Case ID and Override Reference are required');
        return;
      }

      const overrideData = {
        case_id: parseInt(overrideFormData.case_id),
        override_case_reference: overrideFormData.override_case_reference,
        justification: overrideFormData.justification,
      };

      console.log('Override request submitted:', overrideData);
      alert('Override request submitted successfully');
      
      // Reset form and close modal
      setOverrideFormData({
        case_id: '',
        override_case_reference: '',
        justification: '',
      });
      setShowOverrideForm(false);
      
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to submit override request');
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  const getApprovalLevelLabel = (level: number) => {
    return `Level ${level}`;
  };

  const getMinQuotationsLabel = (count: number) => {
    return `${count} Quotations`;
  };

  return (
    <div className="budget-limits-user-view p-6">
      <div className="mb-6">
        <h2 className="text-xl font-bold mb-2">Budget Approval Limits</h2>
        <p className="text-gray-600">View current budget approval thresholds and quotation requirements</p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Override Permission Notice */}
      {canOverride && (
        <div className="bg-yellow-50 border border-yellow-200 rounded p-4 mb-6">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800">Override Permission</h3>
              <div className="mt-2 text-sm text-yellow-700">
                <p>You have permission to override minimum quotation requirements when justified.</p>
                <button
                  onClick={() => setShowOverrideForm(true)}
                  className="mt-2 bg-yellow-600 text-white px-3 py-1 rounded text-sm hover:bg-yellow-700"
                >
                  Request Override
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Budget Limits Table */}
      <div className="bg-white border border-gray-200 rounded">
        <div className="px-4 py-3 border-b border-gray-200">
          <h3 className="text-lg font-medium">Current Budget Approval Configuration</h3>
        </div>

        {loading ? (
          <div className="p-8 text-center text-gray-500">Loading...</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Currency</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Amount Threshold</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Approver Level</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Min Quotations</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Rule Type</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {budgetLimits.map((limit) => (
                  <tr key={limit.id}>
                    <td className="px-4 py-2 text-sm font-medium">{limit.currency}</td>
                    <td className="px-4 py-2 text-sm">{formatCurrency(limit.amount_threshold, limit.currency)}</td>
                    <td className="px-4 py-2 text-sm">
                      <span className="bg-gray-100 text-gray-800 px-2 py-1 rounded text-xs">
                        {getApprovalLevelLabel(limit.approver_level_required)}
                      </span>
                    </td>
                    <td className="px-4 py-2 text-sm">
                      <span className="bg-blue-100 text-blue-800 px-2 py-1 rounded text-xs">
                        {getMinQuotationsLabel(limit.min_quotations_required)}
                      </span>
                    </td>
                    <td className="px-4 py-2 text-sm">
                      <span className={`px-2 py-1 rounded text-xs ${
                        limit.is_general_rule 
                          ? 'bg-green-100 text-green-800' 
                          : 'bg-yellow-100 text-yellow-800'
                      }`}>
                        {limit.is_general_rule ? 'General Rule' : 'Specific Rule'}
                      </span>
                    </td>
                  </tr>
                ))}
                {budgetLimits.length === 0 && (
                  <tr>
                    <td colSpan={5} className="px-4 py-8 text-center text-gray-500">
                      No budget limits configured yet
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Override Form Modal */}
      {showOverrideForm && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">Request Quotation Override</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Case ID *</label>
                <input
                  type="text"
                  value={overrideFormData.case_id}
                  onChange={(e) => setOverrideFormData({...overrideFormData, case_id: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="Enter case ID"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Override Reference *</label>
                <input
                  type="text"
                  value={overrideFormData.override_case_reference}
                  onChange={(e) => setOverrideFormData({...overrideFormData, override_case_reference: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="Enter override reference"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Justification</label>
                <textarea
                  value={overrideFormData.justification}
                  onChange={(e) => setOverrideFormData({...overrideFormData, justification: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded h-20"
                  placeholder="Enter justification for override"
                />
              </div>
            </div>
            
            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleOverrideSubmit}
                className="flex-1 bg-blue-600 text-white p-2 rounded hover:bg-blue-700"
              >
                Submit Override
              </button>
              <button
                onClick={() => setShowOverrideForm(false)}
                className="flex-1 bg-gray-300 text-gray-700 p-2 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Summary */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
        <div className="bg-white border border-gray-200 rounded p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{budgetLimits.length}</div>
          <div className="text-sm text-gray-600">Active Limits</div>
        </div>
        <div className="bg-white border border-gray-200 rounded p-4 text-center">
          <div className="text-2xl font-bold text-green-600">
            {new Set(budgetLimits.map(l => l.currency)).size}
          </div>
          <div className="text-sm text-gray-600">Currencies</div>
        </div>
        <div className="bg-white border border-gray-200 rounded p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">
            {Math.max(...budgetLimits.map(l => l.min_quotations_required), 0)}
          </div>
          <div className="text-sm text-gray-600">Max Quotations</div>
        </div>
      </div>
    </div>
  );
} 
