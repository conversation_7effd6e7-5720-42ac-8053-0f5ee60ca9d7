'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import {
  BudgetCodeFormData,
  SUPPORTED_CURRENCIES,
  canManageProcurement,
} from '../../../types/procurement.types';
import { 
  procurementApi,
  BudgetCode,
  BudgetCodeCreate,
  BudgetCodeUpdate 
} from '../../../services/procurement.api';

export default function BudgetCodesManagement() {
  const { user } = useAuth();
  const [budgetCodes, setBudgetCodes] = useState<BudgetCode[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [formData, setFormData] = useState<BudgetCodeFormData>({
    code: '',
    description: '',
    department: '',
    budget_limit: null,
    currency: 'UGX',
    is_active: true,
  });

  // Check permissions
  const canEdit = canManageProcurement(user?.role);

  // Load budget codes on component mount
  useEffect(() => {
    loadBudgetCodes();
  }, []);

  const loadBudgetCodes = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await procurementApi.budgetCodes.list();
      setBudgetCodes(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load budget codes');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async () => {
    try {
      setError(null);
      
      // Validation
      if (!formData.code?.trim() || !formData.description?.trim()) {
        setError('Code and description are required');
        return;
      }

      // Check for duplicate codes
      if (budgetCodes.some(code => code.code.toLowerCase() === formData.code.toLowerCase())) {
        setError('Budget code already exists');
        return;
      }

      const createData: BudgetCodeCreate = {
        code: formData.code.toUpperCase().trim(),
        description: formData.description.trim(),
        department: formData.department?.trim() || undefined,
        is_active: formData.is_active,
      };

      const newBudgetCode = await procurementApi.budgetCodes.create(createData);
      setBudgetCodes([...budgetCodes, newBudgetCode]);
      
      // Reset form and close modal
      resetForm();
      setShowAddModal(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create budget code');
    }
  };

  const handleUpdate = async (id: number, updateData: BudgetCodeUpdate) => {
    try {
      setError(null);
      
      const updatedBudgetCode = await procurementApi.budgetCodes.update(id, updateData);
      setBudgetCodes(budgetCodes.map(code => 
        code.id === id ? updatedBudgetCode : code
      ));
      
      setEditingId(null);
      resetForm();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update budget code');
    }
  };

  const handleToggleActive = async (code: BudgetCode) => {
    try {
      setError(null);
      
      const updateData: BudgetCodeUpdate = {
        is_active: !code.is_active,
      };
      await handleUpdate(code.id, updateData);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to toggle budget code status');
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this budget code?')) {
      return;
    }

    try {
      setError(null);
      
      await procurementApi.budgetCodes.delete(id);
      setBudgetCodes(budgetCodes.filter(code => code.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete budget code');
    }
  };

  const handleSaveAndPublish = async () => {
    try {
      setError(null);
      alert('Budget codes saved and published successfully');
      await loadBudgetCodes();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save and publish');
    }
  };

  const resetForm = () => {
    setFormData({
      code: '',
      description: '',
      department: '',
      budget_limit: null,
      currency: 'UGX',
      is_active: true,
    });
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency,
    }).format(amount);
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <div className="budget-codes-management p-6">
      <div className="mb-6">
        <h2 className="text-xl font-bold mb-2">Budget Codes</h2>
        <p className="text-gray-600">Manage budget codes for financial tracking and categorization</p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Add New Code Button */}
      {canEdit && (
        <div className="mb-6">
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Add New Budget Code
          </button>
        </div>
      )}

      {/* Budget Codes Table */}
      <div className="bg-white border border-gray-200 rounded">
        <div className="px-4 py-3 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Budget Codes</h3>
            {canEdit && (
              <button
                onClick={handleSaveAndPublish}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                Save & Publish
              </button>
            )}
          </div>
        </div>

        {loading ? (
          <div className="p-8 text-center text-gray-500">Loading...</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Code</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Description</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Department</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Status</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Created</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {budgetCodes.map((code) => (
                  <tr key={code.id}>
                    <td className="px-4 py-2 text-sm font-medium font-mono">{code.code}</td>
                    <td className="px-4 py-2 text-sm">{code.description}</td>
                    <td className="px-4 py-2 text-sm">{code.department || 'Not specified'}</td>
                    <td className="px-4 py-2 text-sm">
                      <button
                        onClick={() => handleToggleActive(code)}
                        className={`px-2 py-1 rounded text-xs cursor-pointer hover:opacity-80 ${
                          code.is_active
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}
                      >
                        {code.is_active ? 'Active' : 'Inactive'}
                      </button>
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-500">
                      {formatDate(code.created_at)}
                    </td>
                    <td className="px-4 py-2 text-sm space-x-2">
                      <button
                        onClick={() => setEditingId(code.id)}
                        className="text-blue-600 hover:text-blue-800"
                      >
                        Edit
                      </button>
                      <button
                        onClick={() => handleDelete(code.id)}
                        className="text-red-600 hover:text-red-800"
                      >
                        Delete
                      </button>
                    </td>
                  </tr>
                ))}
                {budgetCodes.length === 0 && (
                  <tr>
                    <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                      No budget codes created yet
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Add/Edit Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">Add New Budget Code</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Code *</label>
                <input
                  type="text"
                  value={formData.code}
                  onChange={(e) => setFormData({...formData, code: e.target.value.toUpperCase()})}
                  className="w-full p-2 border border-gray-300 rounded font-mono"
                  placeholder="Enter budget code (e.g., BC001)"
                  maxLength={20}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Description *</label>
                <input
                  type="text"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="Enter description"
                  maxLength={200}
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Department (Optional)</label>
                <input
                  type="text"
                  value={formData.department}
                  onChange={(e) => setFormData({...formData, department: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="Enter department name"
                  maxLength={100}
                />
              </div>

              <div className="flex items-center">
                <input
                  type="checkbox"
                  checked={formData.is_active}
                  onChange={(e) => setFormData({...formData, is_active: e.target.checked})}
                  className="mr-2"
                />
                <label className="text-sm">Active</label>
              </div>
            </div>
            
            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleCreate}
                className="flex-1 bg-blue-600 text-white p-2 rounded hover:bg-blue-700"
              >
                Create Code
              </button>
              <button
                onClick={() => {
                  setShowAddModal(false);
                  resetForm();
                }}
                className="flex-1 bg-gray-300 text-gray-700 p-2 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mt-6">
        <div className="bg-white border border-gray-200 rounded p-4 text-center">
          <div className="text-2xl font-bold text-blue-600">{budgetCodes.length}</div>
          <div className="text-sm text-gray-600">Total Codes</div>
        </div>
        <div className="bg-white border border-gray-200 rounded p-4 text-center">
          <div className="text-2xl font-bold text-green-600">
            {budgetCodes.filter(code => code.is_active).length}
          </div>
          <div className="text-sm text-gray-600">Active Codes</div>
        </div>
        <div className="bg-white border border-gray-200 rounded p-4 text-center">
          <div className="text-2xl font-bold text-orange-600">
            {new Set(budgetCodes.map(code => code.department).filter(Boolean)).size}
          </div>
          <div className="text-sm text-gray-600">Departments</div>
        </div>
      </div>
    </div>
  );
} 
