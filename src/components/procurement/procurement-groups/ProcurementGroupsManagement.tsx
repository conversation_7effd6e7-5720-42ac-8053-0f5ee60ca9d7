'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/context/AuthContext';
import {
  ProcurementGroupFormData,
  canManageProcurement,
} from '../../../types/procurement.types';
import { 
  procurementApi,
  ProcurementGroup,
  ProcurementGroupCreate,
  ProcurementGroupUpdate 
} from '../../../services/procurement.api';

export default function ProcurementGroupsManagement() {
  const { user } = useAuth();
  const [procurementGroups, setProcurementGroups] = useState<ProcurementGroup[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showAddModal, setShowAddModal] = useState(false);
  const [editingId, setEditingId] = useState<number | null>(null);
  const [formData, setFormData] = useState<ProcurementGroupFormData>({
    group_id_code: '',
    name: '',
    description: '',
    invoices_billed_to: '',
    status: 'active',
  });

  // Check permissions
  const canEdit = canManageProcurement(user?.role);

  // Load procurement groups on component mount
  useEffect(() => {
    loadProcurementGroups();
  }, []);

  const loadProcurementGroups = async () => {
    try {
      setLoading(true);
      setError(null);
      
      const data = await procurementApi.procurementGroups.list();
      setProcurementGroups(data);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load procurement groups');
    } finally {
      setLoading(false);
    }
  };

  const handleCreate = async () => {
    try {
      setError(null);
      
      // Validation
      if (!formData.group_id_code?.trim() || !formData.name?.trim()) {
        setError('Group ID code and name are required');
        return;
      }

      // Check for duplicate group codes
      if (procurementGroups.some(group => group.group_id_code.toLowerCase() === formData.group_id_code.toLowerCase())) {
        setError('Group ID code already exists');
        return;
      }

      const createData: ProcurementGroupCreate = {
        group_id_code: formData.group_id_code.trim(),
        name: formData.name.trim(),
        invoices_billed_to: formData.invoices_billed_to?.trim() || undefined,
        status: formData.status === 'active' ? 'Active' : 'Deactivated',
      };

      const newGroup = await procurementApi.procurementGroups.create(createData);
      setProcurementGroups([...procurementGroups, newGroup]);
      
      // Reset form and close modal
      resetForm();
      setShowAddModal(false);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create procurement group');
    }
  };

  const handleUpdate = async (id: number, updateData: ProcurementGroupUpdate) => {
    try {
      setError(null);
      
      const updatedGroup = await procurementApi.procurementGroups.update(id, updateData);
      setProcurementGroups(procurementGroups.map(group => 
        group.id === id ? updatedGroup : group
      ));
      
      setEditingId(null);
      resetForm();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update procurement group');
    }
  };

  const handleDelete = async (id: number) => {
    if (!confirm('Are you sure you want to delete this procurement group?')) {
      return;
    }

    try {
      setError(null);
      
      await procurementApi.procurementGroups.delete(id);
      setProcurementGroups(procurementGroups.filter(group => group.id !== id));
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to delete procurement group');
    }
  };

  const handleToggleStatus = async (group: ProcurementGroup) => {
    try {
      setError(null);
      
      const newStatus = group.status === 'Active' ? 'Deactivated' : 'Active';
      await procurementApi.procurementGroups.updateStatus(group.id, newStatus);
      await loadProcurementGroups();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to toggle group status');
    }
  };

  const handlePublishGroup = async (id: number) => {
    try {
      setError(null);
      
      await procurementApi.procurementGroups.publish(id);
      alert('Procurement group published successfully');
      await loadProcurementGroups();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to publish group');
    }
  };

  const handleSaveAndPublish = async () => {
    try {
      setError(null);
      alert('All procurement groups saved and published successfully');
      await loadProcurementGroups();
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to save and publish');
    }
  };

  const resetForm = () => {
    setFormData({
      group_id_code: '',
      name: '',
      description: '',
      invoices_billed_to: '',
      status: 'active',
    });
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric', month: 'short', day: 'numeric', hour: '2-digit', minute: '2-digit'
    });
  };

  return (
    <div className="procurement-groups-management p-6">
      <div className="mb-6">
        <h2 className="text-xl font-bold mb-2">Procurement Groups</h2>
        <p className="text-gray-600">Manage procurement groups for projects, departments, and offices</p>
      </div>

      {error && (
        <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
          {error}
        </div>
      )}

      {/* Add New Group Button */}
      {canEdit && (
        <div className="mb-6">
          <button
            onClick={() => setShowAddModal(true)}
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700"
          >
            Add New Group
          </button>
        </div>
      )}

      {/* Procurement Groups Table */}
      <div className="bg-white border border-gray-200 rounded">
        <div className="px-4 py-3 border-b border-gray-200">
          <div className="flex justify-between items-center">
            <h3 className="text-lg font-medium">Procurement Groups</h3>
            {canEdit && (
              <button
                onClick={handleSaveAndPublish}
                className="bg-green-600 text-white px-4 py-2 rounded hover:bg-green-700"
              >
                Save & Publish
              </button>
            )}
          </div>
        </div>

        {loading ? (
          <div className="p-8 text-center text-gray-500">Loading...</div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Group ID</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Name</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Invoices Billed To</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Status</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Created</th>
                  <th className="px-4 py-2 text-left text-sm font-medium text-gray-500">Actions</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {procurementGroups.map((group) => (
                  <tr key={group.id}>
                    <td className="px-4 py-2 text-sm font-medium">{group.group_id_code}</td>
                    <td className="px-4 py-2 text-sm">{group.name}</td>
                    <td className="px-4 py-2 text-sm">{group.invoices_billed_to || 'Not specified'}</td>
                    <td className="px-4 py-2 text-sm">
                      <button
                        onClick={() => handleToggleStatus(group)}
                        className={`px-2 py-1 rounded text-xs ${
                          group.status === 'Active'
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        } cursor-pointer hover:opacity-80`}
                      >
                        {group.status}
                      </button>
                    </td>
                    <td className="px-4 py-2 text-sm text-gray-500">
                      {formatDate(group.created_at)}
                    </td>
                    <td className="px-4 py-2 text-sm">
                      <button
                        onClick={() => handleDelete(group.id)}
                        className="text-red-600 hover:text-red-800 mr-2"
                      >
                        Delete
                      </button>
                      <button
                        onClick={() => handlePublishGroup(group.id)}
                        className="text-green-600 hover:text-green-800"
                      >
                        Publish
                      </button>
                    </td>
                  </tr>
                ))}
                {procurementGroups.length === 0 && (
                  <tr>
                    <td colSpan={6} className="px-4 py-8 text-center text-gray-500">
                      No procurement groups created yet
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
        )}
      </div>

      {/* Add/Edit Modal */}
      {showAddModal && (
        <div className="fixed inset-0 bg-gray-600 bg-opacity-50 flex items-center justify-center p-4">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <h3 className="text-lg font-medium mb-4">Add New Procurement Group</h3>
            
            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-1">Group ID Code</label>
                <input
                  type="text"
                  value={formData.group_id_code}
                  onChange={(e) => setFormData({...formData, group_id_code: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="Enter group ID"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Name</label>
                <input
                  type="text"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="Enter group name"
                />
              </div>
              
              <div>
                <label className="block text-sm font-medium mb-1">Invoices Billed To</label>
                <input
                  type="text"
                  value={formData.invoices_billed_to}
                  onChange={(e) => setFormData({...formData, invoices_billed_to: e.target.value})}
                  className="w-full p-2 border border-gray-300 rounded"
                  placeholder="Enter billing entity (optional)"
                />
              </div>
            </div>
            
            <div className="flex space-x-3 mt-6">
              <button
                onClick={handleCreate}
                className="flex-1 bg-blue-600 text-white p-2 rounded hover:bg-blue-700"
              >
                Create Group
              </button>
              <button
                onClick={() => setShowAddModal(false)}
                className="flex-1 bg-gray-300 text-gray-700 p-2 rounded hover:bg-gray-400"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
} 
