'use client';

import React, { useEffect, useState } from 'react';
import { Logo } from '@/components/logo';
import { cn } from '@/lib/utils';

interface GlobalPreloaderProps {
  isLoading: boolean;
  text?: string;
  variant?: 'default' | 'minimal' | 'elegant' | 'modern' | 'gradient';
  onComplete?: () => void;
}

export function GlobalPreloader({ 
  isLoading, 
  text = 'Loading...', 
  variant = 'modern',
  onComplete 
}: GlobalPreloaderProps) {
  const [isVisible, setIsVisible] = useState(isLoading);
  const [animationPhase, setAnimationPhase] = useState<'enter' | 'loading' | 'exit'>('enter');

  useEffect(() => {
    if (isLoading) {
      setIsVisible(true);
      setAnimationPhase('enter');
      // Transition to loading phase after enter animation
      const timer = setTimeout(() => setAnimationPhase('loading'), 300);
      return () => clearTimeout(timer);
    } else {
      setAnimationPhase('exit');
      // Hide after exit animation
      const timer = setTimeout(() => {
        setIsVisible(false);
        onComplete?.();
      }, 600);
      return () => clearTimeout(timer);
    }
  }, [isLoading, onComplete]);

  if (!isVisible) return null;

  // Default variant - Clean and professional
  if (variant === 'default') {
    return (
      <div className={cn(
        'fixed inset-0 z-50 flex flex-col items-center justify-center',
        'bg-white dark:bg-slate-900 transition-opacity duration-300',
        animationPhase === 'exit' ? 'opacity-0' : 'opacity-100'
      )}>
        <div className="flex flex-col items-center space-y-6">
          <div className="animate-pulse-slow">
            <Logo />
          </div>
          
          <div className="relative">
            <div className="w-12 h-12 border-4 border-gray-200 dark:border-slate-600 rounded-full animate-spin">
              <div className="absolute top-0 left-0 w-full h-full border-4 border-transparent border-t-blue-600 dark:border-t-blue-400 rounded-full"></div>
            </div>
          </div>
          
          <p className="text-sm font-medium text-gray-600 dark:text-gray-400 animate-pulse">
            {text}
          </p>
        </div>
      </div>
    );
  }

  // Minimal variant - Simple and fast
  if (variant === 'minimal') {
    return (
      <div className={cn(
        'fixed inset-0 z-50 flex items-center justify-center',
        'bg-white/90 dark:bg-slate-900/90 backdrop-blur-sm transition-all duration-300',
        animationPhase === 'exit' ? 'opacity-0 scale-95' : 'opacity-100 scale-100'
      )}>
        <div className="flex flex-col items-center space-y-4">
          <div className="flex space-x-2">
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce"></div>
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce animation-delay-200"></div>
            <div className="w-3 h-3 bg-blue-500 rounded-full animate-bounce animation-delay-400"></div>
          </div>
          <p className="text-sm text-gray-600 dark:text-gray-400">{text}</p>
        </div>
      </div>
    );
  }

  // Elegant variant - Sophisticated design
  if (variant === 'elegant') {
    return (
      <div className={cn(
        'fixed inset-0 z-50 flex flex-col items-center justify-center',
        'bg-gradient-to-br from-slate-50 to-white dark:from-slate-900 dark:to-slate-800',
        'transition-all duration-500',
        animationPhase === 'exit' ? 'opacity-0' : 'opacity-100'
      )}>
        {/* Decorative elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-1/4 left-1/4 w-64 h-64 bg-blue-100/30 dark:bg-blue-900/20 rounded-full blur-3xl animate-float"></div>
          <div className="absolute bottom-1/4 right-1/4 w-64 h-64 bg-purple-100/30 dark:bg-purple-900/20 rounded-full blur-3xl animate-float animation-delay-1000"></div>
        </div>

        <div className="relative z-10 flex flex-col items-center space-y-8">
          <div className={cn(
            'transform transition-all duration-700',
            animationPhase === 'enter' ? 'scale-0 rotate-180' : 'scale-100 rotate-0'
          )}>
            <Logo />
          </div>

          <div className="relative">
            <div className="w-16 h-16 border-2 border-gray-200 dark:border-slate-600 rounded-full">
              <div className="absolute inset-0 border-2 border-transparent border-t-blue-500 rounded-full animate-spin"></div>
              <div className="absolute inset-2 border-2 border-transparent border-b-purple-500 rounded-full animate-spin-reverse"></div>
            </div>
          </div>

          <div className="text-center space-y-2">
            <p className="text-lg font-semibold text-gray-800 dark:text-gray-200">{text}</p>
            <div className="flex justify-center space-x-1">
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse animation-delay-200"></div>
              <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse animation-delay-400"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Gradient variant - Colorful and vibrant
  if (variant === 'gradient') {
    return (
      <div className={cn(
        'fixed inset-0 z-50 flex flex-col items-center justify-center',
        'bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500',
        'transition-all duration-500',
        animationPhase === 'exit' ? 'opacity-0 scale-110' : 'opacity-100 scale-100'
      )}>
        {/* Animated background */}
        <div className="absolute inset-0">
          <div className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent animate-shimmer"></div>
        </div>

        <div className="relative z-10 flex flex-col items-center space-y-8 text-white">
          <div className={cn(
            'transform transition-all duration-1000',
            animationPhase === 'enter' ? 'scale-0 opacity-0' : 'scale-100 opacity-100'
          )}>
            <div className="p-4 bg-white/20 backdrop-blur-sm rounded-2xl">
              <Logo />
            </div>
          </div>

          <div className="relative">
            <div className="w-20 h-20 border-4 border-white/30 rounded-full animate-spin">
              <div className="absolute top-0 left-0 w-full h-full border-4 border-transparent border-t-white rounded-full"></div>
            </div>
            <div className="absolute inset-0 w-20 h-20 border-4 border-transparent border-r-yellow-300 rounded-full animate-spin-reverse"></div>
          </div>

          <div className="text-center space-y-3">
            <p className="text-xl font-bold text-white drop-shadow-lg">{text}</p>
            <div className="flex justify-center space-x-2">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className="w-1 h-6 bg-white/80 animate-wave"
                  style={{ animationDelay: `${i * 0.1}s` }}
                ></div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Modern variant (default) - Contemporary design with glassmorphism
  return (
    <div className={cn(
      'fixed inset-0 z-50 flex flex-col items-center justify-center',
      'bg-gradient-to-br from-white/95 via-blue-50/50 to-indigo-100/30',
      'dark:from-slate-900/95 dark:via-slate-800/50 dark:to-slate-700/30',
      'backdrop-blur-md transition-all duration-500',
      animationPhase === 'exit' ? 'opacity-0' : 'opacity-100'
    )}>
      {/* Floating particles */}
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute -top-4 -left-4 w-72 h-72 bg-blue-300/20 rounded-full mix-blend-multiply filter blur-xl animate-blob"></div>
        <div className="absolute -top-4 -right-4 w-72 h-72 bg-purple-300/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-2000"></div>
        <div className="absolute -bottom-8 left-20 w-72 h-72 bg-pink-300/20 rounded-full mix-blend-multiply filter blur-xl animate-blob animation-delay-4000"></div>
      </div>

      <div className="relative z-10 flex flex-col items-center space-y-8">
        {/* Logo with glassmorphism container */}
        <div className={cn(
          'p-6 bg-white/20 dark:bg-slate-800/20 backdrop-blur-sm rounded-3xl',
          'border border-white/30 dark:border-slate-700/30 shadow-xl',
          'transform transition-all duration-700',
          animationPhase === 'enter' ? 'scale-0 rotate-12' : 'scale-100 rotate-0'
        )}>
          <div className="animate-pulse-slow">
            <Logo />
          </div>
        </div>

        {/* Modern spinner */}
        <div className="relative">
          <div className="w-16 h-16 border-4 border-blue-200/50 dark:border-slate-600/50 rounded-full animate-spin">
            <div className="absolute top-0 left-0 w-full h-full border-4 border-transparent border-t-blue-600 dark:border-t-blue-400 rounded-full animate-spin"></div>
          </div>
          <div className="absolute inset-0 w-16 h-16 border-4 border-transparent border-r-purple-500/70 dark:border-r-purple-400/70 rounded-full animate-spin-reverse"></div>
          
          {/* Center dot */}
          <div className="absolute inset-0 flex items-center justify-center">
            <div className="w-3 h-3 bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-pulse"></div>
          </div>
        </div>

        {/* Loading text with enhanced styling */}
        <div className="text-center space-y-3">
          <p className="text-lg font-semibold text-gray-700 dark:text-gray-300">
            {text}
          </p>
          <div className="flex justify-center space-x-1">
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce animation-delay-200"></div>
            <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce animation-delay-400"></div>
          </div>
        </div>

        {/* Progress indicator */}
        <div className="w-64 h-1 bg-gray-200/50 dark:bg-slate-700/50 rounded-full overflow-hidden">
          <div className="h-full bg-gradient-to-r from-blue-500 to-purple-500 rounded-full animate-shimmer"></div>
        </div>
      </div>
    </div>
  );
}
