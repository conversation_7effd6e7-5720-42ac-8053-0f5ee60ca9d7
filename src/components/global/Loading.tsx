'use client';

import { LoadingSpinner } from '@/components/ui/loading-spinner';

interface LoadingProps {
  fullScreen?: boolean;
  text?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'dots' | 'pulse' | 'orbit' | 'wave' | 'brand';
  className?: string;
}

export function Loading({
  fullScreen = true,
  text = 'Loading...',
  size = 'md',
  variant = 'default',
  className = ''
}: LoadingProps) {
  return (
    <div className={`flex items-center justify-center ${fullScreen ? 'min-h-screen' : 'py-8'} ${className}`}>
      <LoadingSpinner
        size={size}
        text={text}
        fullScreen={fullScreen}
        variant={variant}
      />
    </div>
  );
}

// Full page loading component with brand variant
export function FullPageLoading({ text = 'Loading...', variant = 'brand' }: { text?: string; variant?: 'default' | 'dots' | 'pulse' | 'orbit' | 'wave' | 'brand' } = {}) {
  return <Loading fullScreen text={text} size="lg" variant={variant} />;
}

// Inline loading component
export function InlineLoading({ variant = 'dots' }: { variant?: 'default' | 'dots' | 'pulse' | 'orbit' | 'wave' } = {}) {
  return <Loading fullScreen={false} text="" size="sm" variant={variant} className="py-2" />;
}

// Button loading component
export function ButtonLoading({ variant = 'dots' }: { variant?: 'default' | 'dots' | 'pulse' | 'orbit' | 'wave' } = {}) {
  return (
    <div className="flex items-center justify-center">
      <LoadingSpinner size="sm" variant={variant} />
    </div>
  );
}

// Beautiful page transition loading
export function PageTransitionLoading() {
  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm">
      <LoadingSpinner
        size="lg"
        variant="brand"
        text="Loading page..."
        fullScreen={false}
      />
    </div>
  );
}

// Skeleton loading for content
export function ContentLoading() {
  return (
    <div className="animate-pulse space-y-4 p-6">
      <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
      <div className="space-y-2">
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-5/6"></div>
      </div>
      <div className="h-32 bg-gray-200 dark:bg-gray-700 rounded"></div>
    </div>
  );
}

// Card loading skeleton
export function CardLoading() {
  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-6 animate-pulse">
      <div className="flex items-center space-x-4 mb-4">
        <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
        <div className="space-y-2 flex-1">
          <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
          <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4"></div>
        </div>
      </div>
      <div className="space-y-2">
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded"></div>
        <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-4/5"></div>
      </div>
    </div>
  );
}
