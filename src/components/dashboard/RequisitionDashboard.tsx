'use client';

import React from 'react';
import { ChevronDown, ChevronRight, Filter, Search, ArrowUpDown } from 'lucide-react';
import RequisitionFilterSidebar from './RequisitionFilterSidebar';

// Mock data based on the screenshot
type Requisition = {
  id: string;
  projectId: string;
  year: number;
  caseNo: number;
  reference: string;
  subject: string;
  status: string;
  dateCreated: string;
  budgetCode: string;
  budgetCategory: string;
};

const mockRequisitions: Requisition[] = [
  {
    id: 'REQ-001',
    projectId: '0002',
    year: 2024,
    caseNo: 180,
    reference: 'UNICEF/0002/24/180',
    subject: 'Spare parts vehicle UAG 257R',
    status: 'Requisition',
    dateCreated: 'Oct 14, 2024',
    budgetCode: '228002',
    budgetCategory: 'Maintenance vehicles',
  },
  {
    id: 'REQ-002',
    projectId: '0002',
    year: 2024,
    caseNo: 179,
    reference: 'UNICEF/0002/24/179',
    subject: 'Lawn mower',
    status: 'Requisition',
    dateCreated: 'Oct 12, 2024',
    budgetCode: '228003',
    budgetCategory: 'Maintenance machinery',
  },
    {
    id: 'REQ-003',
    projectId: '0002',
    year: 2024,
    caseNo: 178,
    reference: 'UNICEF/0002/24/178',
    subject: 'Water pump spares',
    status: 'Requisition',
    dateCreated: 'Oct 11, 2024',
    budgetCode: '228004',
    budgetCategory: 'Construction equipment',
  },
  {
    id: 'REQ-004',
    projectId: '0002',
    year: 2024,
    caseNo: 177,
    reference: 'UNICEF/0002/24/177',
    subject: 'Carburetor assembly',
    status: 'Requisition',
    dateCreated: 'Oct 13, 2024',
    budgetCode: '228005',
    budgetCategory: 'Maintenance machinery',
  },
  {
    id: 'REQ-005',
    projectId: '0002',
    year: 2024,
    caseNo: 176,
    reference: 'UNICEF/0002/24/176',
    subject: 'Spark plugs set',
    status: 'Requisition',
    dateCreated: 'Oct 10, 2024',
    budgetCode: '228006',
    budgetCategory: 'Maintenance machinery',
  },
  {
    id: 'REQ-006',
    projectId: '0002',
    year: 2024,
    caseNo: 175,
    reference: 'UNICEF/0002/24/175',
    subject: 'Muffler exhaust pipe',
    status: 'Submitted',
    dateCreated: 'Oct 9, 2024',
    budgetCode: '228007',
    budgetCategory: 'Maintenance machinery',
  },
  {
    id: 'REQ-007',
    projectId: '0002',
    year: 2024,
    caseNo: 174,
    reference: 'UNICEF/0002/24/174',
    subject: 'Piston rings kit',
    status: 'Submitted',
    dateCreated: 'Oct 8, 2024',
    budgetCode: '228008',
    budgetCategory: 'Maintenance vehicles',
  },
  {
    id: 'REQ-008',
    projectId: '0002',
    year: 2024,
    caseNo: 173,
    reference: 'UNICEF/0002/24/173',
    subject: 'Fuel filter replacement',
    status: 'Submitted',
    dateCreated: 'Oct 7, 2024',
    budgetCode: '228009',
    budgetCategory: 'Maintenance vehicles',
  },
];

interface RequisitionDashboardProps {
  projectId: string;
}

const RequisitionDashboard: React.FC<RequisitionDashboardProps> = ({ projectId }) => {
  const [searchTerm, setSearchTerm] = React.useState('');
  const [sortConfig, setSortConfig] = React.useState<{ key: keyof Requisition; direction: string } | null>(null);
  const [currentPage, setCurrentPage] = React.useState(1);
  const [isFilterOpen, setIsFilterOpen] = React.useState(false);
  const [filters, setFilters] = React.useState<{ status: string[], category: string[] }>({ status: [], category: [] });
  const itemsPerPage = 8;

  const sortedRequisitions = React.useMemo(() => {
    let sortableItems = [...mockRequisitions];
    if (sortConfig !== null) {
      sortableItems.sort((a, b) => {
        if (a[sortConfig.key] < b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? -1 : 1;
        }
        if (a[sortConfig.key] > b[sortConfig.key]) {
          return sortConfig.direction === 'ascending' ? 1 : -1;
        }
        return 0;
      });
    }
    return sortableItems;
  }, [sortConfig]);

  const filteredRequisitions = sortedRequisitions.filter((req) => {
    const searchMatch = Object.values(req).some((value) =>
      String(value).toLowerCase().includes(searchTerm.toLowerCase())
    );

    const statusMatch = filters.status.length === 0 || filters.status.includes(req.status);
    const categoryMatch = filters.category.length === 0 || filters.category.includes(req.budgetCategory);

    return searchMatch && statusMatch && categoryMatch;
  });

  const requestSort = (key: keyof Requisition) => {
    let direction = 'ascending';
    if (sortConfig && sortConfig.key === key && sortConfig.direction === 'ascending') {
      direction = 'descending';
    }
    setSortConfig({ key, direction });
  };

  const paginatedRequisitions = filteredRequisitions.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  const totalPages = Math.ceil(filteredRequisitions.length / itemsPerPage);


  return (
    <div className="h-full w-full flex flex-col bg-[#f9f9f9] text-sm">
      <div className="bg-white border-b border-gray-200 px-8 py-4">
        <div className="flex items-center text-gray-500">
          <span>0002 Mechanical</span>
          <ChevronRight className="h-4 w-4 mx-1" />
          <span className="text-gray-800 font-semibold">Requisitions</span>
        </div>
        <h1 className="text-2xl font-bold text-gray-800 mt-2">Requisitions</h1>
      </div>

      <div className="flex-grow p-8 relative">
        <RequisitionFilterSidebar 
          isOpen={isFilterOpen} 
          onClose={() => setIsFilterOpen(false)} 
          filters={filters}
          setFilters={setFilters}
        />
        <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
          {/* Toolbar */}
          <div className="flex justify-between items-center mb-4">
            <div className="relative w-1/3">
              <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400" />
              <input
                type="text"
                placeholder="Search ID, Year, Subject, Reference..."
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
            <div className="flex items-center space-x-2">
              <button onClick={() => setIsFilterOpen(true)} className="flex items-center px-4 py-2 border border-gray-300 rounded-lg text-gray-700 hover:bg-gray-100">
                <Filter className="h-4 w-4 mr-2" />
                <span>Filter</span>
              </button>
              
              <button className="bg-[#18546c] text-white px-4 py-2 rounded-lg hover:bg-opacity-90">
                + Create New
              </button>
            </div>
          </div>

          {/* Table */}
          <div className="overflow-x-auto">
            <table className="w-full text-left">
              <thead>
                <tr className="border-b border-gray-200 bg-gray-50 text-xs text-gray-500 uppercase">
                  <th className="p-3 font-medium cursor-pointer" onClick={() => requestSort('projectId')}>Project ID</th>
                  <th className="p-3 font-medium cursor-pointer" onClick={() => requestSort('year')}>Year</th>
                  <th className="p-3 font-medium cursor-pointer" onClick={() => requestSort('caseNo')}>Case No.</th>
                  <th className="p-3 font-medium cursor-pointer" onClick={() => requestSort('reference')}>Reference</th>
                  <th className="p-3 font-medium cursor-pointer" onClick={() => requestSort('subject')}>Subject</th>
                  <th className="p-3 font-medium cursor-pointer" onClick={() => requestSort('status')}>Status</th>
                  <th className="p-3 font-medium cursor-pointer" onClick={() => requestSort('dateCreated')}>Date created</th>
                  <th className="p-3 font-medium cursor-pointer" onClick={() => requestSort('budgetCode')}>Budget code</th>
                  <th className="p-3 font-medium cursor-pointer" onClick={() => requestSort('budgetCategory')}>Budget category</th>
                </tr>
              </thead>
              <tbody>
                {paginatedRequisitions.map((req, index) => (
                  <tr key={req.id} className={`border-b border-gray-200 hover:bg-gray-100 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50'}`}>
                    <td className="p-3">{req.projectId}</td>
                    <td className="p-3">{req.year}</td>
                    <td className="p-3">{req.caseNo}</td>
                    <td className="p-3">{req.reference}</td>
                    <td className="p-3">{req.subject}</td>
                    <td className="p-3">{req.status}</td>
                    <td className="p-3">{req.dateCreated}</td>
                    <td className="p-3">{req.budgetCode}</td>
                    <td className="p-3">{req.budgetCategory}</td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          {/* Pagination */}
          <div className="flex justify-between items-center mt-4 text-xs text-gray-600">
            <div>
              Showing {Math.min((currentPage - 1) * itemsPerPage + 1, filteredRequisitions.length)} to {Math.min(currentPage * itemsPerPage, filteredRequisitions.length)} of {filteredRequisitions.length}
            </div>
            <div className="flex items-center space-x-1">
                <button 
                  onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                  disabled={currentPage === 1}
                  className='px-2 py-1 border rounded-md bg-white disabled:opacity-50'>&lt;</button>
                {[...Array(totalPages).keys()].map(num => (
                  <button 
                    key={num + 1}
                    onClick={() => setCurrentPage(num + 1)}
                    className={`px-2 py-1 border rounded-md ${currentPage === num + 1 ? 'bg-[#18546c] text-white' : 'bg-white'}`}>
                      {num + 1}
                    </button>
                ))}
                <button 
                  onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                  disabled={currentPage === totalPages}
                  className='px-2 py-1 border rounded-md bg-white disabled:opacity-50'>&gt;</button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RequisitionDashboard;
