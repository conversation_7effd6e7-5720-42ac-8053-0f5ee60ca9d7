'use client';

import React from 'react';
import Link from 'next/link';
import { User } from '@/context/AuthContext';
import {
  ClockIcon,
  DocumentTextIcon,
  BanknotesIcon,
  CheckCircleIcon,
  ClipboardDocumentIcon,
  ShieldCheckIcon,
  ShoppingBagIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  InformationCircleIcon,
} from '@heroicons/react/24/solid';

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  iconBgColor: string;
  iconTextColor: string;
  detailsLink: string;
}

// Get the appropriate hover styles based on the background color
const getHoverStyles = (bgClass: string) => {
  if (bgClass.includes('orange')) return {
    border: 'group-hover:border-orange-300',
    sectionBg: 'group-hover:bg-orange-50 dark:group-hover:bg-orange-900/20',
    text: 'group-hover:text-orange-600 dark:group-hover:text-orange-400',
  };
  if (bgClass.includes('blue')) return {
    border: 'group-hover:border-blue-300',
    sectionBg: 'group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20',
    text: 'group-hover:text-blue-600 dark:group-hover:text-blue-400',
  };
  if (bgClass.includes('pink')) return {
    border: 'group-hover:border-pink-300',
    sectionBg: 'group-hover:bg-pink-50 dark:group-hover:bg-pink-900/20',
    text: 'group-hover:text-pink-600 dark:group-hover:text-pink-400',
  };
  if (bgClass.includes('green')) return {
    border: 'group-hover:border-green-300',
    sectionBg: 'group-hover:bg-green-50 dark:group-hover:bg-green-900/20',
    text: 'group-hover:text-green-600 dark:group-hover:text-green-400',
  };
  return {
    border: 'group-hover:border-blue-300',
    sectionBg: 'group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20',
    text: 'group-hover:text-blue-600 dark:group-hover:text-blue-400',
  };
};

const MetricCard = React.memo<MetricCardProps>(({ 
  title,
  value,
  icon,
  iconBgColor,
  iconTextColor,
  detailsLink,
}) => {
  // Get the hover styles based on the icon background color
  const { border, sectionBg, text } = getHoverStyles(iconBgColor);
  
  return (
  <div 
    className={`group bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-transparent hover:${sectionBg} p-6 transition-all duration-200 cursor-pointer will-change-transform`}
    style={{ transform: 'translateZ(0)' }} // Hardware acceleration
  >
    <div className="flex items-start space-x-4 mb-6">
      <div 
        className={`p-2.5 rounded-lg ${iconBgColor} ${iconTextColor} transition-transform duration-300 ease-out group-hover:scale-110`}
      >
        {icon}
      </div>
      <div>
        <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">{title}</p>
        <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{value}</p>
      </div>
    </div>
    <div 
      className={`mt-4 pt-4 -mx-6 -mb-6 px-6 pb-4 rounded-b-lg transition-all duration-300 ease-out ${sectionBg} ${border}`}
    >
      <div className="ml-[75px]">
        <Link 
          href={detailsLink} 
          className={`text-sm font-medium flex items-center transition-colors text-gray-500 dark:text-gray-400 ${text} hover:opacity-80`}
          prefetch={false} // Only prefetch when needed
        >
          <span className="group-hover:translate-x-0.5 transition-transform duration-300 ease-out inline-flex items-center">
            View Details
            <ChevronRightIcon className="ml-1 h-4 w-4 group-hover:translate-x-0.5 transition-transform duration-300 ease-out" />
          </span>
        </Link>
      </div>
    </div>
  </div>
  );
});

const PurchaseCard = React.memo<{ title: string; value: string | number; icon: React.ReactNode }>(({ title, value, icon }) => (
  <div 
    className="bg-green-50 dark:bg-green-900/20 rounded-lg p-6 border border-green-100 dark:border-green-800/30 hover:bg-green-100 dark:hover:bg-green-900/30 transition-all duration-300 ease-out cursor-pointer will-change-transform min-h-24 h-full flex"
    style={{ transform: 'translateZ(0)' }} // Hardware acceleration
  >
    <div className="flex items-start space-x-3">
      <div className="p-2 bg-green-100 dark:bg-green-800/50 text-green-600 dark:text-green-400 rounded-lg transition-transform duration-300 ease-out hover:scale-110">
        {icon}
      </div>
      <div className="flex-1">
        <p className="text-xs text-green-700 dark:text-green-300 font-medium uppercase tracking-wide mb-1">{title}</p>
        <p className="text-2xl font-bold text-green-900 dark:text-green-100">{value}</p>
      </div>
    </div>
  </div>
));

interface SupplierDashboardProps {
  user: User;
}

// Add display name to memoized components for better debugging
MetricCard.displayName = 'MetricCard';
PurchaseCard.displayName = 'PurchaseCard';

const SupplierDashboard: React.FC<SupplierDashboardProps> = ({ user }) => {
  const [selectedCurrency, setSelectedCurrency] = React.useState<'USD' | 'UGX' | 'KES'>('USD');
  const [isCurrencyDropdownOpen, setIsCurrencyDropdownOpen] = React.useState(false);
  const [selectedProject, setSelectedProject] = React.useState('PRJ-0001');
  const [isProjectDropdownOpen, setIsProjectDropdownOpen] = React.useState(false);
  const currencies: Array<'USD' | 'UGX' | 'KES'> = ['USD', 'UGX', 'KES'];
  
  // Sample projects data - replace with actual data from your API
  const projects = [
    { id: 'PRJ-0001', name: 'Project Alpha' },
    { id: 'PRJ-0002', name: 'Project Beta' },
    { id: 'PRJ-0003', name: 'Project Gamma' },
    { id: 'PRJ-0004', name: 'Project Delta' },
  ];
  const currentDate = new Date().toLocaleDateString('en-US', { 
    weekday: 'long', 
    day: 'numeric', 
    month: 'long', 
    year: 'numeric' 
  });

  return (
    <div className="p-16 h-full flex flex-col overflow-hidden">
      {/* Header with Greeting and Create Button */}
      <div className="flex justify-between items-start mb-6">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Hi, {user?.first_name || user?.full_name?.split(' ')[0] || 'User'}!</h1>
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 text-sm mt-2">
            <div className="flex items-center">
              <p className="text-gray-400 dark:text-gray-400 font-medium">{currentDate}</p>
            </div>
            <p className="text-gray-400 dark:text-gray-500">Here&apos;s your business overview at a glance.</p>
          </div>
        </div>
        <button className="bg-gray-100 text-[#18546c] px-4 py-2 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md">
          <span className="mr-1.5 text-base">+</span> Create New
        </button>
      </div>

      {/* Top Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
        <Link href="/pending-inquiries" className="block hover:no-underline">
          <MetricCard
            title="Pending Inquiries"
            value={35}
            icon={<ClockIcon className="h-10 w-10" />}
            iconBgColor="bg-orange-100 dark:bg-orange-900/30"
            iconTextColor="text-orange-600 dark:text-orange-400"
            detailsLink="/pending-inquiries"
          />
        </Link>
        <Link href="/sales-orders" className="block hover:no-underline">
          <MetricCard
            title="Sales Orders"
            value={17}
            icon={<DocumentTextIcon className="h-10 w-10" />}
            iconBgColor="bg-blue-100 dark:bg-blue-900/30"
            iconTextColor="text-blue-600 dark:text-blue-400"
            detailsLink="/sales-orders"
          />
        </Link>
        <Link href="/sales-invoices" className="block hover:no-underline">
          <MetricCard
            title="Sales Invoices"
            value={22}
            icon={<BanknotesIcon className="h-10 w-10" />}
            iconBgColor="bg-pink-100 dark:bg-pink-900/30"
            iconTextColor="text-pink-600 dark:text-pink-400"
            detailsLink="/sales-invoices"
          />
        </Link>
        <Link href="/closed-cases" className="block hover:no-underline">
          <MetricCard
            title="Closed Cases"
            value={35}
            icon={<CheckCircleIcon className="h-10 w-10" />}
            iconBgColor="bg-green-100 dark:bg-green-900/30"
            iconTextColor="text-green-600 dark:text-green-400"
            detailsLink="/closed-cases"
          />
        </Link>
      </div>

      {/* Bottom Section */}
      <div className="grid grid-cols-1 lg:grid-cols-6 gap-6 flex-1 min-h-0">
        {/* Purchases Section */}
        <div className="lg:col-span-2">
          <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-8">
            <div className="flex justify-between items-center mb-4">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Purchases</h2>
            </div>
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4 auto-rows-[6rem] items-stretch">
                <Link href="/pending-requisitions" className="block hover:no-underline h-full">
                  <PurchaseCard
                    title="Pending Requisitions"
                    value="04"
                    icon={<ClipboardDocumentIcon className="h-6 w-6" />}
                  />
                </Link>
                <Link href="/quotations" className="block hover:no-underline h-full">
                  <PurchaseCard
                    title="Quotations - In"
                    value="13"
                    icon={<DocumentTextIcon className="h-6 w-6" />}
                  />
                </Link>
                <Link href="/pending-requisitions?status=Approved" className="block hover:no-underline h-full">
                  <PurchaseCard
                    title="Approved"
                    value="07"
                    icon={<ShieldCheckIcon className="h-6 w-6" />}
                  />
                </Link>
                <Link href="/invoices" className="block hover:no-underline h-full">
                  <PurchaseCard
                    title="Invoices - In"
                    value="02"
                    icon={<BanknotesIcon className="h-6 w-6" />}
                  />
                </Link>
              </div>
              <div className="grid grid-cols-1">
                <Link href="/purchase-orders" className="block hover:no-underline h-full">
                  <PurchaseCard
                    title="Purchase Orders"
                    value="09"
                    icon={<ShoppingBagIcon className="h-6 w-6" />}
                  />
                </Link>
              </div>
            </div>
          </div>
        </div>

        {/* Sales Performance Section */}
        <div className="lg:col-span-4 bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-100 dark:border-slate-700 p-6 flex flex-col">
          <div className="flex justify-between items-center mb-6">
            <div className="flex items-center">
              <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mr-2">Sales Performance</h2>
              <InformationCircleIcon className="h-4 w-4 text-gray-400 dark:text-gray-500 cursor-pointer" title="Sales performance details"/>
            </div>
            <div className="relative">
              <button
                onClick={() => setIsCurrencyDropdownOpen(!isCurrencyDropdownOpen)}
                className="text-sm text-gray-700 dark:text-gray-300 bg-white dark:bg-slate-700 border border-gray-200 dark:border-slate-600 hover:bg-gray-50 dark:hover:bg-slate-600 px-3 py-2 rounded-lg flex items-center font-medium shadow-sm"
              >
                {selectedCurrency}
                <ChevronDownIcon className={`ml-2 h-4 w-4 transition-transform duration-200 ${isCurrencyDropdownOpen ? 'rotate-180' : ''}`} />
              </button>
              {isCurrencyDropdownOpen && (
                <div className="absolute right-0 mt-2 w-20 bg-white dark:bg-slate-800 rounded-lg shadow-lg border border-gray-200 dark:border-slate-600 z-10">
                  {currencies.map((currency) => (
                    <button
                      key={currency}
                      onClick={() => {
                        setSelectedCurrency(currency);
                        setIsCurrencyDropdownOpen(false);
                      }}
                      className="block w-full text-left px-3 py-2 text-sm text-gray-700 hover:bg-gray-50 dark:text-gray-300 dark:hover:bg-slate-700 first:rounded-t-lg last:rounded-b-lg"
                    >
                      {currency}
                    </button>
                  ))}
                </div>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-8 mb-6">
            <div className="flex items-center space-x-2">
              <span className="h-3 w-3 bg-blue-500 rounded-full"></span>
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Sales</span>
            </div>
            <div className="flex items-center space-x-2">
              <span className="h-3 w-3 bg-red-400 rounded-full"></span>
              <span className="text-sm text-gray-600 dark:text-gray-400">Total Purchases</span>
            </div>
          </div>

          <div className="flex items-start space-x-8 mb-6">
            <div>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">$254,101</p>
            </div>
            <div>
              <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">$71,803</p>
            </div>
          </div>

          {/* Chart Placeholder */}
          <div className="flex-1 min-h-[200px] bg-gradient-to-br from-blue-50 to-red-50 dark:from-slate-700/50 dark:to-slate-600/50 rounded-lg flex items-center justify-center border border-gray-100 dark:border-slate-600">
            <div className="text-center">
              <p className="text-gray-500 dark:text-gray-400 text-sm mb-2">Sales Performance Chart</p>
              <p className="text-xs text-gray-400 dark:text-gray-500">Chart visualization will be displayed here</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupplierDashboard;
