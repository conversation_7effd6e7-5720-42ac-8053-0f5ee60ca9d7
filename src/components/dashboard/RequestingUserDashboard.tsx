'use client';

import React from 'react';
import { User } from '@/context/AuthContext';
import Link from 'next/link';
import { FiFileText, FiClock, FiCheckCircle, FiShoppingBag, FiChevronRight, FiPlus } from 'react-icons/fi';

// Get the appropriate hover styles based on the background color
const getHoverStyles = (bgClass: string) => {
  if (bgClass.includes('orange')) return {
    border: 'group-hover:border-orange-300',
    sectionBg: 'group-hover:bg-orange-50 dark:group-hover:bg-orange-900/20',
    text: 'group-hover:text-orange-600 dark:group-hover:text-orange-400',
  };
  if (bgClass.includes('blue')) return {
    border: 'group-hover:border-blue-300',
    sectionBg: 'group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20',
    text: 'group-hover:text-blue-600 dark:group-hover:text-blue-400',
  };
  if (bgClass.includes('green')) return {
    border: 'group-hover:border-green-300',
    sectionBg: 'group-hover:bg-green-50 dark:group-hover:bg-green-900/20',
    text: 'group-hover:text-green-600 dark:group-hover:text-green-400',
  };
  if (bgClass.includes('purple')) return {
    border: 'group-hover:border-purple-300',
    sectionBg: 'group-hover:bg-purple-50 dark:group-hover:bg-purple-900/20',
    text: 'group-hover:text-purple-600 dark:group-hover:text-purple-400',
  };
  return {
    border: 'group-hover:border-blue-300',
    sectionBg: 'group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20',
    text: 'group-hover:text-blue-600 dark:group-hover:text-blue-400',
  };
};

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  iconBgColor: string;
  iconTextColor: string;
  detailsLink: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  iconBgColor,
  iconTextColor,
  detailsLink,
}) => {
  const { border, sectionBg, text } = getHoverStyles(iconBgColor);
  
  return (
    <div className="group bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-transparent hover:border-gray-200 dark:hover:border-slate-700 p-6 transition-all duration-200 cursor-pointer">
      <div className="flex items-start space-x-4 mb-6">
        <div className={`p-2.5 rounded-lg ${iconBgColor} ${iconTextColor}`}>
          {icon}
        </div>
        <div>
          <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">{title}</p>
          <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{value}</p>
        </div>
      </div>
      <div className={`mt-4 pt-4 -mx-6 -mb-6 px-6 pb-4 rounded-b-lg transition-colors ${sectionBg} ${border}`}>
        <div className="ml-[75px]">
          <Link 
            href={detailsLink} 
            className={`text-sm font-medium flex items-center transition-colors text-gray-500 dark:text-gray-400 ${text} hover:opacity-80`}
          >
            View Details
            <FiChevronRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  );
};

interface RequestingUserDashboardProps {
  user: User;
}

const RequestingUserDashboard: React.FC<RequestingUserDashboardProps> = ({ user }) => {
  return (
    <div className="space-y-6 p-16">
      {/* Welcome Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Hi {user.first_name || user.full_name?.split(' ')[0] || 'User'},
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {new Date().toLocaleDateString('en-US', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' })} • Here&apos;s your request overview and status updates.
          </p>
        </div>
        <button className="bg-gray-100 text-[#18546c] px-4 py-2 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md">
          <FiPlus className="mr-1.5 text-base" />
          New Request
        </button>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4 overflow-x-auto pb-4">
        <MetricCard
          title="My Requests"
          value="12"
          icon={<FiFileText className="h-10 w-10" />}
          iconBgColor="bg-blue-100 dark:bg-blue-900/30"
          iconTextColor="text-blue-600 dark:text-blue-400"
          detailsLink="/my-requests"
        />

        <MetricCard
          title="Pending Approval"
          value="5"
          icon={<FiClock className="h-10 w-10" />}
          iconBgColor="bg-orange-100 dark:bg-orange-900/30"
          iconTextColor="text-orange-600 dark:text-orange-400"
          detailsLink="/my-requests/pending"
        />

        <MetricCard
          title="Approved Requests"
          value="7"
          icon={<FiCheckCircle className="h-10 w-10" />}
          iconBgColor="bg-green-100 dark:bg-green-900/30"
          iconTextColor="text-green-600 dark:text-green-400"
          detailsLink="/my-requests/approved"
        />

        <MetricCard
          title="Total Value"
          value="$8,450"
          icon={<FiShoppingBag className="h-10 w-10" />}
          iconBgColor="bg-purple-100 dark:bg-purple-900/30"
          iconTextColor="text-purple-600 dark:text-purple-400"
          detailsLink="/my-requests/value"
        />
      </div>

      {/* Request Management Section */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">My Requests</h2>
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-6">
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-transparent hover:border-gray-200 dark:hover:border-slate-700 p-6 transition-all duration-200 h-full">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Recent Requests</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">Track your recent requisition requests</p>
              
              <div className="space-y-4">
                <div className="flex items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
                  <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg mr-3">
                    <FiCheckCircle className="h-5 w-5 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <p className="font-medium text-green-800 dark:text-green-200">Office Supplies</p>
                    <p className="text-xs text-green-600 dark:text-green-400">Approved - $450</p>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-orange-50 dark:bg-orange-900/20 rounded-lg border border-orange-200 dark:border-orange-800">
                  <div className="p-2 bg-orange-100 dark:bg-orange-900/30 rounded-lg mr-3">
                    <FiClock className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                  </div>
                  <div>
                    <p className="font-medium text-orange-800 dark:text-orange-200">Computer Equipment</p>
                    <p className="text-xs text-orange-600 dark:text-orange-400">Pending - $2,300</p>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg mr-3">
                    <FiFileText className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <p className="font-medium text-blue-800 dark:text-blue-200">Stationery Items</p>
                    <p className="text-xs text-blue-600 dark:text-blue-400">Draft - $125</p>
                  </div>
                </div>
                
                <div className="mt-4">
                  <Link href="/my-requests" className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center transition-colors">
                    View All My Requests
                    <svg className="ml-1 w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          </div>
          
          <div className="lg:col-span-4">
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6 h-full">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Request Status Overview</h3>
                <svg className="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                </svg>
              </div>
              
              <div className="flex justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-blue-400 mr-2"></div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Draft</p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">3</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-orange-400 mr-2"></div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Pending</p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">5</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-green-400 mr-2"></div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Approved</p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">7</p>
                  </div>
                </div>
                <div className="text-right">
                  <button className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                    This Month
                    <svg className="h-4 w-4 inline-block text-gray-400 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>
              </div>
              
              <div className="h-64 bg-gray-50 dark:bg-slate-700/30 rounded-lg flex items-center justify-center">
                <div className="text-center p-6">
                  <FiFileText className="w-16 h-16 mx-auto text-gray-400 dark:text-gray-500" />
                  <h4 className="mt-3 text-sm font-medium text-gray-900 dark:text-gray-100">Request Timeline</h4>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Your request history and status timeline</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Quick Actions Section */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Quick Actions</h2>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          <Link href="/requisitions/create" className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-transparent hover:border-gray-200 dark:hover:border-slate-700 p-6 transition-all duration-200 group">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg">
                <FiFileText className="h-6 w-6 text-blue-600 dark:text-blue-400" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-colors">Create Requisition</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Submit new request</p>
              </div>
            </div>
          </Link>

          <Link href="/my-requests/drafts" className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-transparent hover:border-gray-200 dark:hover:border-slate-700 p-6 transition-all duration-200 group">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
                <FiClock className="h-6 w-6 text-gray-600 dark:text-gray-400" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white group-hover:text-gray-600 dark:group-hover:text-gray-400 transition-colors">Continue Drafts</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">3 incomplete requests</p>
              </div>
            </div>
          </Link>

          <Link href="/my-requests/track" className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-transparent hover:border-gray-200 dark:hover:border-slate-700 p-6 transition-all duration-200 group">
            <div className="flex items-center space-x-3">
              <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                <FiCheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
              </div>
              <div>
                <h3 className="font-medium text-gray-900 dark:text-white group-hover:text-green-600 dark:group-hover:text-green-400 transition-colors">Track Requests</h3>
                <p className="text-sm text-gray-500 dark:text-gray-400">Monitor request status</p>
              </div>
            </div>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default RequestingUserDashboard;
