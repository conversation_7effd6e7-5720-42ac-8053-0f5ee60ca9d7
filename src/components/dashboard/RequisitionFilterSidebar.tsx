'use client';

import React from 'react';
import { X } from 'lucide-react';

interface RequisitionFilterSidebarProps {
  isOpen: boolean;
  onClose: () => void;
  filters: { status: string[], category: string[] };
  setFilters: React.Dispatch<React.SetStateAction<{ status: string[], category: string[] }>>;
}

const RequisitionFilterSidebar: React.FC<RequisitionFilterSidebarProps> = ({ isOpen, onClose, filters, setFilters }) => {
  const [localFilters, setLocalFilters] = React.useState(filters);

  React.useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleStatusChange = (status: string) => {
    setLocalFilters(prev => {
      const newStatus = prev.status.includes(status)
        ? prev.status.filter(s => s !== status)
        : [...prev.status, status];
      return { ...prev, status: newStatus };
    });
  };

  const handleCategoryChange = (category: string) => {
    setLocalFilters(prev => {
      const newCategory = prev.category.includes(category)
        ? prev.category.filter(c => c !== category)
        : [...prev.category, category];
      return { ...prev, category: newCategory };
    });
  };

  const applyFilters = () => {
    setFilters(localFilters);
    onClose();
  };

  const clearFilters = () => {
    const cleared = { status: [], category: [] };
    setLocalFilters(cleared);
    setFilters(cleared);
    onClose();
  };
  const statuses = ['Requisition', 'Submitted'];
  const categories = ['Maintenance vehicles', 'Maintenance machinery', 'Construction equipment'];

  return (
    <div
      className={`fixed top-0 right-0 h-full w-80 bg-white shadow-lg transform transition-transform duration-300 ease-in-out z-50 ${isOpen ? 'translate-x-0' : 'translate-x-full'}`}>
      <div className="flex flex-col h-full">
        {/* Header */}
        <div className="flex justify-between items-center p-4 border-b border-gray-200">
          <h2 className="font-semibold text-lg">Filter Requisitions</h2>
          <button onClick={onClose} className="p-1 rounded-full hover:bg-gray-100">
            <X className="h-5 w-5" />
          </button>
        </div>

        {/* Filters */}
        <div className="flex-grow p-4 overflow-y-auto">
          <div className="mb-6">
            <h3 className="font-medium mb-2">Status</h3>
            {statuses.map(status => (
              <div key={status} className="flex items-center mb-2">
                <input 
                  type="checkbox" 
                  id={`status-${status}`}
                  checked={localFilters.status.includes(status)}
                  onChange={() => handleStatusChange(status)}
                  className="h-4 w-4 rounded border-gray-300 text-[#18546c] focus:ring-[#18546c]" />
                <label htmlFor={`status-${status}`} className="ml-2 text-sm text-gray-700">{status}</label>
              </div>
            ))}
          </div>

          <div>
            <h3 className="font-medium mb-2">Budget Category</h3>
            {categories.map(category => (
              <div key={category} className="flex items-center mb-2">
                <input 
                  type="checkbox" 
                  id={`category-${category}`}
                  checked={localFilters.category.includes(category)}
                  onChange={() => handleCategoryChange(category)}
                  className="h-4 w-4 rounded border-gray-300 text-[#18546c] focus:ring-[#18546c]" />
                <label htmlFor={`category-${category}`} className="ml-2 text-sm text-gray-700">{category}</label>
              </div>
            ))}
          </div>
        </div>

        {/* Footer */}
        <div className="p-4 border-t border-gray-200">
          <button onClick={applyFilters} className="w-full bg-[#18546c] text-white py-2 rounded-lg hover:bg-opacity-90">
            Apply Filters
          </button>
          <button onClick={clearFilters} className="w-full mt-2 text-sm text-gray-600 hover:underline">
            Clear All
          </button>
        </div>
      </div>
    </div>
  );
};

export default RequisitionFilterSidebar;
