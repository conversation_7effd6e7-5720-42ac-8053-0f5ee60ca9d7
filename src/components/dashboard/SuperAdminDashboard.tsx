'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { User } from '@/context/AuthContext';
import {
  UsersIcon,
  BuildingOfficeIcon,
  DocumentTextIcon,
  BanknotesIcon,
  CheckCircleIcon,
  ClipboardDocumentIcon,
  ShieldCheckIcon,
  UserPlusIcon,
  ChevronRightIcon,
  ChevronDownIcon,
  InformationCircleIcon,
  CogIcon,
  EnvelopeIcon,
  ShoppingBagIcon,
} from '@heroicons/react/24/solid';
import { api } from '@/services/api';

interface Organization {
  id: string;
  name: string;
  type: string;
  status: string;
  [key: string]: any;
}

interface DashboardUser {
  id: string;
  name: string;
  email: string;
  role: string;
  [key: string]: any;
}

interface Invitation {
  id: string;
  email: string;
  status: string;
  [key: string]: any;
}

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  iconBgColor: string;
  iconTextColor: string;
  detailsLink: string;
}

// Get the appropriate hover styles based on the background color
const getHoverStyles = (bgClass: string) => {
  if (bgClass.includes('orange')) return {
    border: 'group-hover:border-orange-300',
    sectionBg: 'group-hover:bg-orange-50 dark:group-hover:bg-orange-900/20',
    text: 'group-hover:text-orange-600 dark:group-hover:text-orange-400',
  };
  if (bgClass.includes('blue')) return {
    border: 'group-hover:border-blue-300',
    sectionBg: 'group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20',
    text: 'group-hover:text-blue-600 dark:group-hover:text-blue-400',
  };
  if (bgClass.includes('pink')) return {
    border: 'group-hover:border-pink-300',
    sectionBg: 'group-hover:bg-pink-50 dark:group-hover:bg-pink-900/20',
    text: 'group-hover:text-pink-600 dark:group-hover:text-pink-400',
  };
  if (bgClass.includes('green')) return {
    border: 'group-hover:border-green-300',
    sectionBg: 'group-hover:bg-green-50 dark:group-hover:bg-green-900/20',
    text: 'group-hover:text-green-600 dark:group-hover:text-green-400',
  };
  if (bgClass.includes('purple')) return {
    border: 'group-hover:border-purple-300',
    sectionBg: 'group-hover:bg-purple-50 dark:group-hover:bg-purple-900/20',
    text: 'group-hover:text-purple-600 dark:group-hover:text-purple-400',
  };
  return {
    border: 'group-hover:border-gray-300',
    sectionBg: 'group-hover:bg-gray-50 dark:group-hover:bg-gray-800',
    text: 'group-hover:text-gray-600 dark:group-hover:text-gray-300',
  };
};

const MetricCard = React.memo<MetricCardProps>(({ 
  title,
  value,
  icon,
  iconBgColor,
  iconTextColor,
  detailsLink
}) => {
  const hoverStyles = getHoverStyles(iconBgColor);
  
  return (
    <div className={`group bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-100 dark:border-slate-700 p-6 transition-all duration-200 hover:shadow-md ${hoverStyles.border}`}>
      <div className="flex justify-between items-start mb-4">
        <div className={`p-3 rounded-lg ${iconBgColor}`}>
          <div className={iconTextColor}>
            {icon}
          </div>
        </div>
        <ChevronRightIcon className={`h-5 w-5 text-gray-400 dark:text-gray-600 transition-all duration-200 ${hoverStyles.text}`} />
      </div>
      <h3 className="text-lg font-semibold text-gray-700 dark:text-gray-200 mb-1">{title}</h3>
      <div className="flex justify-between items-end">
        <p className="text-2xl font-bold text-gray-900 dark:text-white">{value}</p>
        <Link href={detailsLink} className="text-sm text-blue-600 dark:text-blue-400 hover:underline">
          View All
        </Link>
      </div>
    </div>
  );
});

interface AdminCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
}

const AdminCard = React.memo<AdminCardProps>(({ 
  title,
  value,
  icon
}) => {
  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-100 dark:border-slate-700 p-4 hover:shadow-md transition-all duration-200">
      <div className="flex items-center space-x-3">
        <div className="text-gray-500 dark:text-gray-400">
          {icon}
        </div>
        <div>
          <h4 className="text-sm font-medium text-gray-500 dark:text-gray-400">{title}</h4>
          <p className="text-lg font-semibold text-gray-900 dark:text-white">{value}</p>
        </div>
      </div>
    </div>
  );
});

interface SuperAdminDashboardProps {
  user: User;
}

// Add display name to memoized components for better debugging
MetricCard.displayName = 'MetricCard';
AdminCard.displayName = 'AdminCard';

const SuperAdminDashboard: React.FC<SuperAdminDashboardProps> = ({ user }) => {
  const [organizations, setOrganizations] = useState<Organization[]>([]);
  const [users, setUsers] = useState<DashboardUser[]>([]);
  const [invitations, setInvitations] = useState<Invitation[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  
  // Fetch dashboard data
  useEffect(() => {
    const fetchDashboardData = async () => {
      setIsLoading(true);
      setError(null);
      
      try {
        // Fetch organizations
        const orgsResponse = await api.get('/organizations', {
          params: { limit: 100 }
        });
        
        // Type check and safely access data
        if (orgsResponse && typeof orgsResponse === 'object' && 'data' in orgsResponse) {
          setOrganizations((orgsResponse.data as any)?.items || []);
        }
        
        // Fetch users (this endpoint might need to be implemented)
        const usersResponse = await api.get('/users', {
          params: { limit: 100 }
        }).catch(() => ({ data: { items: [] } }));
        
        // Type check and safely access data
        if (usersResponse && typeof usersResponse === 'object' && 'data' in usersResponse) {
          setUsers((usersResponse.data as any)?.items || []);
        }
        
        // Fetch pending invitations
        const invitationsResponse = await api.get('/invitations', {
          params: { status: 'pending' }
        }).catch(() => ({ data: { items: [] } }));
        
        // Type check and safely access data
        if (invitationsResponse && typeof invitationsResponse === 'object' && 'data' in invitationsResponse) {
          setInvitations((invitationsResponse.data as any)?.items || []);
        }
        
      } catch (err: any) {
        console.error('Error fetching dashboard data:', err);
        setError('Failed to load dashboard data. Please try again later.');
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchDashboardData();
  }, []);
  
  const currentDate = new Date().toLocaleDateString('en-US', { 
    weekday: 'long', 
    day: 'numeric', 
    month: 'long', 
    year: 'numeric' 
  });
  
  // Calculate metrics
  const activeOrganizations = organizations.filter(org => org.status === 'active').length;
  const pendingInvitations = invitations.filter(inv => inv.status === 'pending').length;
  const activeUsers = users.filter(user => user.status === 'active').length;
  
  // Organization types
  const supplierOrgs = organizations.filter(org => org.type === 'supplier').length;
  const buyerOrgs = organizations.filter(org => org.type === 'buyer').length;
  const partnerOrgs = organizations.filter(org => org.type === 'partner').length;

  return (
    <div className="bg-gray-50 dark:bg-slate-900 min-h-screen p-16">
      {/* Header with Greeting and Create Button */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">Welcome, {user?.full_name?.split(' ')[0] || 'Admin'}!</h1>
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 text-sm mt-2">
            <div className="flex items-center">
              <p className="text-gray-400 dark:text-gray-400 font-medium">{currentDate}</p>
            </div>
            <p className="text-gray-400 dark:text-gray-500">System overview and administration panel.</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <Link href="/settings" className="bg-gray-100 text-gray-700 px-4 py-2 rounded-lg hover:bg-gray-200 dark:bg-slate-800 dark:text-white dark:hover:bg-slate-700 transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md">
            <CogIcon className="h-4 w-4 mr-2" />
            Settings
          </Link>
          <Link href="/organizations/new" className="bg-[#18546c] text-white px-4 py-2 rounded-lg hover:bg-[#134255] transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md">
            <span className="mr-1.5 text-base">+</span> New Organization
          </Link>
        </div>
      </div>

      {error && (
        <div className="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded-lg mb-6">
          {error}
        </div>
      )}

      {isLoading ? (
        <div className="flex justify-center items-center h-64">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-[#18546c]"></div>
        </div>
      ) : (
        <>
          {/* Top Metrics */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
            <Link href="/organizations" className="block hover:no-underline">
              <MetricCard
                title="Organizations"
                value={organizations.length}
                icon={<BuildingOfficeIcon className="h-10 w-10" />}
                iconBgColor="bg-blue-100 dark:bg-blue-900/30"
                iconTextColor="text-blue-600 dark:text-blue-400"
                detailsLink="/organizations"
              />
            </Link>
            <Link href="/users" className="block hover:no-underline">
              <MetricCard
                title="Users"
                value={users.length}
                icon={<UsersIcon className="h-10 w-10" />}
                iconBgColor="bg-purple-100 dark:bg-purple-900/30"
                iconTextColor="text-purple-600 dark:text-purple-400"
                detailsLink="/users"
              />
            </Link>
            <Link href="/invitations" className="block hover:no-underline">
              <MetricCard
                title="Pending Invitations"
                value={pendingInvitations}
                icon={<EnvelopeIcon className="h-10 w-10" />}
                iconBgColor="bg-orange-100 dark:bg-orange-900/30"
                iconTextColor="text-orange-600 dark:text-orange-400"
                detailsLink="/invitations"
              />
            </Link>
            <Link href="/procurements" className="block hover:no-underline">
              <MetricCard
                title="Procurements"
                value={35}
                icon={<DocumentTextIcon className="h-10 w-10" />}
                iconBgColor="bg-green-100 dark:bg-green-900/30"
                iconTextColor="text-green-600 dark:text-green-400"
                detailsLink="/procurements"
              />
            </Link>
          </div>

          {/* Bottom Section */}
          <div className="grid grid-cols-1 lg:grid-cols-6 gap-6">
            {/* Organization Stats Section */}
            <div className="lg:col-span-2">
              <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-200 dark:border-slate-700 p-8">
                <div className="flex justify-between items-center mb-4">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100">Organization Stats</h2>
                </div>
                <div className="space-y-3">
                  <div className="grid grid-cols-2 gap-3">
                    <Link href="/organizations?type=supplier" className="block hover:no-underline">
                      <AdminCard
                        title="Suppliers"
                        value={supplierOrgs}
                        icon={<ShoppingBagIcon className="h-6 w-6" />}
                      />
                    </Link>
                    <Link href="/organizations?type=buyer" className="block hover:no-underline">
                      <AdminCard
                        title="Buyers"
                        value={buyerOrgs}
                        icon={<BanknotesIcon className="h-6 w-6" />}
                      />
                    </Link>
                  </div>
                  <div className="grid grid-cols-2 gap-3">
                    <Link href="/organizations?type=partner" className="block hover:no-underline">
                      <AdminCard
                        title="Partners"
                        value={partnerOrgs}
                        icon={<ShieldCheckIcon className="h-6 w-6" />}
                      />
                    </Link>
                    <Link href="/organizations?status=active" className="block hover:no-underline">
                      <AdminCard
                        title="Active"
                        value={activeOrganizations}
                        icon={<CheckCircleIcon className="h-6 w-6" />}
                      />
                    </Link>
                  </div>
                  <div className="grid grid-cols-1">
                    <Link href="/invitations/new" className="block hover:no-underline">
                      <div className="bg-[#18546c]/10 hover:bg-[#18546c]/20 rounded-lg p-4 transition-colors duration-200 flex items-center justify-center space-x-2">
                        <UserPlusIcon className="h-5 w-5 text-[#18546c]" />
                        <span className="text-[#18546c] font-medium">Invite New User</span>
                      </div>
                    </Link>
                  </div>
                </div>
              </div>
            </div>

            {/* Agents vs Submission Section */}
            <div className="lg:col-span-4 bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-gray-100 dark:border-slate-700 p-6">
              <div className="flex justify-between items-center mb-6">
                <div className="flex items-center">
                  <h2 className="text-lg font-semibold text-gray-900 dark:text-gray-100 mr-2">Agents vs Submission</h2>
                  <InformationCircleIcon className="h-4 w-4 text-gray-400 dark:text-gray-500 cursor-pointer" title="Agent performance metrics compared to submission rates"/>
                </div>
                <Link href="/reports/agents-submissions" className="text-sm text-blue-600 dark:text-blue-400 hover:underline">
                  View Full Report
                </Link>
              </div>

              {/* Agents vs Submission Chart */}
              <div className="mb-6">
                <div className="h-64 bg-gradient-to-br from-blue-50 to-green-50 dark:from-slate-700/50 dark:to-slate-600/50 rounded-lg flex items-center justify-center border border-gray-100 dark:border-slate-600">
                  <div className="text-center">
                    <p className="text-gray-500 dark:text-gray-400 text-sm mb-2">Agents vs Submission Performance</p>
                    <p className="text-xs text-gray-400 dark:text-gray-500">Chart visualization will be displayed here</p>
                  </div>
                </div>
              </div>
              
              {/* Agents vs Submission Stats */}
              <div className="grid grid-cols-2 gap-4">
                <div className="bg-gray-50 dark:bg-slate-700/30 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Top Performing Agents</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-800 dark:text-gray-200">Sarah Johnson</span>
                      <span className="text-sm font-medium text-green-600 dark:text-green-400">94%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-800 dark:text-gray-200">Michael Chen</span>
                      <span className="text-sm font-medium text-green-600 dark:text-green-400">89%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-800 dark:text-gray-200">David Ouma</span>
                      <span className="text-sm font-medium text-green-600 dark:text-green-400">87%</span>
                    </div>
                  </div>
                </div>
                
                <div className="bg-gray-50 dark:bg-slate-700/30 rounded-lg p-4">
                  <h3 className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Submission Metrics</h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-800 dark:text-gray-200">On-time Submissions</span>
                      <span className="text-sm font-medium text-blue-600 dark:text-blue-400">78%</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-800 dark:text-gray-200">Average Processing Time</span>
                      <span className="text-sm font-medium text-blue-600 dark:text-blue-400">2.4 days</span>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-gray-800 dark:text-gray-200">Completion Rate</span>
                      <span className="text-sm font-medium text-blue-600 dark:text-blue-400">91%</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default SuperAdminDashboard;
