'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { User } from '@/context/AuthContext';
import {
  ClockIcon,
  DocumentTextIcon,
  BanknotesIcon,
  CheckCircleIcon,
  ClipboardDocumentIcon,
  InboxIcon,
  ChartBarIcon,
  ExclamationTriangleIcon,
  PlusIcon,
  ArrowTrendingUpIcon,
  CurrencyDollarIcon,
} from '@heroicons/react/24/solid';

interface SupplierUserDashboardProps {
  user: User;
}

interface MetricCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  iconBgColor: string;
  iconTextColor: string;
  detailsLink?: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const MetricCard: React.FC<MetricCardProps> = ({ 
  title, 
  value, 
  icon, 
  iconBgColor, 
  iconTextColor, 
  detailsLink,
  trend 
}) => (
  <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-gray-100 dark:border-slate-700">
    <div className="flex items-center justify-between">
      <div className="flex-1">
        <div className={`inline-flex items-center justify-center w-12 h-12 rounded-lg ${iconBgColor} mb-4`}>
          <span className={iconTextColor}>
            {icon}
          </span>
        </div>
        <p className="text-sm font-medium text-gray-600 dark:text-gray-400 mb-1">{title}</p>
        <p className="text-2xl font-bold text-gray-900 dark:text-gray-100">{value}</p>
        {trend && (
          <div className={`flex items-center mt-2 text-sm ${trend.isPositive ? 'text-green-600' : 'text-red-600'}`}>
            <ArrowTrendingUpIcon className={`h-4 w-4 mr-1 ${trend.isPositive ? '' : 'rotate-180'}`} />
            <span>{Math.abs(trend.value)}% from last month</span>
          </div>
        )}
      </div>
    </div>
    {detailsLink && (
      <div className="mt-4">
        <Link 
          href={detailsLink} 
          className="text-sm text-[#18546c] hover:text-[#134255] font-medium transition-colors duration-200"
        >
          View details →
        </Link>
      </div>
    )}
  </div>
);

const SupplierUserDashboard: React.FC<SupplierUserDashboardProps> = ({ user }) => {
  const [currentDate, setCurrentDate] = useState('');

  useEffect(() => {
    const date = new Date().toLocaleDateString('en-US', { 
      weekday: 'long', 
      day: 'numeric', 
      month: 'long', 
      year: 'numeric' 
    });
    setCurrentDate(date);
  }, []);

  // Mock data for supplier user dashboard
  const dashboardData = {
    pendingInquiries: 12,
    activeQuotations: 8,
    pendingOrders: 5,
    completedOrders: 23,
    totalRevenue: '$45,230',
    monthlyRevenue: '$8,450',
    responseRate: '94%',
    avgResponseTime: '2.3 hrs'
  };

  const recentInquiries = [
    {
      id: 'INQ-001',
      title: 'Office Supplies Request',
      company: 'ABC Corporation',
      value: '$2,500',
      dueDate: '2024-10-02',
      status: 'pending',
      priority: 'high'
    },
    {
      id: 'INQ-002', 
      title: 'IT Equipment Procurement',
      company: 'Tech Solutions Ltd',
      value: '$15,000',
      dueDate: '2024-10-05',
      status: 'responded',
      priority: 'medium'
    },
    {
      id: 'INQ-003',
      title: 'Construction Materials',
      company: 'BuildCorp Inc',
      value: '$8,750',
      dueDate: '2024-10-08',
      status: 'pending',
      priority: 'low'
    }
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'bg-yellow-100 text-yellow-800';
      case 'responded':
        return 'bg-green-100 text-green-800';
      case 'expired':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'text-red-600';
      case 'medium':
        return 'text-yellow-600';
      case 'low':
        return 'text-green-600';
      default:
        return 'text-gray-600';
    }
  };

  return (
    <div className="p-6 bg-gray-50 dark:bg-slate-900 min-h-screen">
      {/* Header with Greeting */}
      <div className="flex justify-between items-start mb-8">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-gray-100">
            Welcome, {user?.first_name || user?.full_name?.split(' ')[0] || 'User'}!
          </h1>
          <div className="flex flex-col sm:flex-row sm:items-center space-y-2 sm:space-y-0 sm:space-x-4 text-sm mt-2">
            <div className="flex items-center">
              <p className="text-gray-400 dark:text-gray-400 font-medium">{currentDate}</p>
            </div>
            <p className="text-gray-400 dark:text-gray-500">Here&apos;s your supplier activity overview.</p>
          </div>
        </div>
        <div className="flex space-x-3">
          <Link 
            href="/quotations/new" 
            className="bg-[#18546c] text-white px-4 py-2 rounded-lg hover:bg-[#134255] transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md"
          >
            <PlusIcon className="h-4 w-4 mr-2" />
            New Quotation
          </Link>
        </div>
      </div>

      {/* Top Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
        <Link href="/inquiries" className="block hover:no-underline">
          <MetricCard
            title="Pending Inquiries"
            value={dashboardData.pendingInquiries}
            icon={<InboxIcon className="h-6 w-6" />}
            iconBgColor="bg-orange-100 dark:bg-orange-900/30"
            iconTextColor="text-orange-600 dark:text-orange-400"
            detailsLink="/inquiries"
            trend={{ value: 12, isPositive: true }}
          />
        </Link>
        <Link href="/quotations" className="block hover:no-underline">
          <MetricCard
            title="Active Quotations"
            value={dashboardData.activeQuotations}
            icon={<DocumentTextIcon className="h-6 w-6" />}
            iconBgColor="bg-blue-100 dark:bg-blue-900/30"
            iconTextColor="text-blue-600 dark:text-blue-400"
            detailsLink="/quotations"
            trend={{ value: 8, isPositive: true }}
          />
        </Link>
        <Link href="/orders" className="block hover:no-underline">
          <MetricCard
            title="Pending Orders"
            value={dashboardData.pendingOrders}
            icon={<ClockIcon className="h-6 w-6" />}
            iconBgColor="bg-yellow-100 dark:bg-yellow-900/30"
            iconTextColor="text-yellow-600 dark:text-yellow-400"
            detailsLink="/orders"
            trend={{ value: 5, isPositive: false }}
          />
        </Link>
        <Link href="/orders?status=completed" className="block hover:no-underline">
          <MetricCard
            title="Completed Orders"
            value={dashboardData.completedOrders}
            icon={<CheckCircleIcon className="h-6 w-6" />}
            iconBgColor="bg-green-100 dark:bg-green-900/30"
            iconTextColor="text-green-600 dark:text-green-400"
            detailsLink="/orders?status=completed"
            trend={{ value: 15, isPositive: true }}
          />
        </Link>
      </div>

      {/* Secondary Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-8 mb-8">
        <MetricCard
          title="Total Revenue"
          value={dashboardData.totalRevenue}
          icon={<CurrencyDollarIcon className="h-6 w-6" />}
          iconBgColor="bg-purple-100 dark:bg-purple-900/30"
          iconTextColor="text-purple-600 dark:text-purple-400"
          trend={{ value: 23, isPositive: true }}
        />
        <MetricCard
          title="Monthly Revenue"
          value={dashboardData.monthlyRevenue}
          icon={<ChartBarIcon className="h-6 w-6" />}
          iconBgColor="bg-indigo-100 dark:bg-indigo-900/30"
          iconTextColor="text-indigo-600 dark:text-indigo-400"
          trend={{ value: 18, isPositive: true }}
        />
        <MetricCard
          title="Response Rate"
          value={dashboardData.responseRate}
          icon={<ArrowTrendingUpIcon className="h-6 w-6" />}
          iconBgColor="bg-green-100 dark:bg-green-900/30"
          iconTextColor="text-green-600 dark:text-green-400"
        />
        <MetricCard
          title="Avg Response Time"
          value={dashboardData.avgResponseTime}
          icon={<ClockIcon className="h-6 w-6" />}
          iconBgColor="bg-blue-100 dark:bg-blue-900/30"
          iconTextColor="text-blue-600 dark:text-blue-400"
        />
      </div>

      {/* Recent Inquiries */}
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6 mb-8 border border-gray-100 dark:border-slate-700">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-gray-100">Recent Inquiries</h2>
          <Link 
            href="/inquiries" 
            className="text-[#18546c] hover:text-[#134255] font-medium text-sm transition-colors duration-200"
          >
            View all →
          </Link>
        </div>
        
        <div className="overflow-x-auto">
          <table className="min-w-full">
            <thead>
              <tr className="border-b border-gray-200 dark:border-slate-600">
                <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Inquiry</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Company</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Value</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Due Date</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Status</th>
                <th className="text-left py-3 px-4 font-medium text-gray-600 dark:text-gray-400">Priority</th>
              </tr>
            </thead>
            <tbody>
              {recentInquiries.map((inquiry, index) => (
                <tr key={inquiry.id} className={`border-b border-gray-100 dark:border-slate-700 hover:bg-gray-100 dark:hover:bg-slate-700 transition-colors ${index % 2 === 0 ? 'bg-white dark:bg-slate-800' : 'bg-gray-50 dark:bg-slate-700/20'}`}>
                  <td className="py-4 px-4">
                    <div>
                      <p className="font-medium text-gray-900 dark:text-gray-100">{inquiry.title}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">{inquiry.id}</p>
                    </div>
                  </td>
                  <td className="py-4 px-4 text-gray-900 dark:text-gray-100">{inquiry.company}</td>
                  <td className="py-4 px-4 font-medium text-gray-900 dark:text-gray-100">{inquiry.value}</td>
                  <td className="py-4 px-4 text-gray-900 dark:text-gray-100">{inquiry.dueDate}</td>
                  <td className="py-4 px-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded-full ${getStatusColor(inquiry.status)}`}>
                      {inquiry.status.charAt(0).toUpperCase() + inquiry.status.slice(1)}
                    </span>
                  </td>
                  <td className="py-4 px-4">
                    <span className={`font-medium ${getPriorityColor(inquiry.priority)}`}>
                      {inquiry.priority.charAt(0).toUpperCase() + inquiry.priority.slice(1)}
                    </span>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <Link 
          href="/inquiries" 
          className="bg-white dark:bg-slate-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-gray-100 dark:border-slate-700 group"
        >
          <div className="flex items-center">
            <div className="bg-orange-100 dark:bg-orange-900/30 p-3 rounded-lg mr-4">
              <InboxIcon className="h-6 w-6 text-orange-600 dark:text-orange-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-[#18546c] transition-colors">
                Respond to Inquiries
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {dashboardData.pendingInquiries} pending responses
              </p>
            </div>
          </div>
        </Link>

        <Link 
          href="/quotations/new" 
          className="bg-white dark:bg-slate-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-gray-100 dark:border-slate-700 group"
        >
          <div className="flex items-center">
            <div className="bg-blue-100 dark:bg-blue-900/30 p-3 rounded-lg mr-4">
              <DocumentTextIcon className="h-6 w-6 text-blue-600 dark:text-blue-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-[#18546c] transition-colors">
                Create Quotation
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Submit new quotation
              </p>
            </div>
          </div>
        </Link>

        <Link 
          href="/orders" 
          className="bg-white dark:bg-slate-800 rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300 p-6 border border-gray-100 dark:border-slate-700 group"
        >
          <div className="flex items-center">
            <div className="bg-green-100 dark:bg-green-900/30 p-3 rounded-lg mr-4">
              <CheckCircleIcon className="h-6 w-6 text-green-600 dark:text-green-400" />
            </div>
            <div>
              <h3 className="font-semibold text-gray-900 dark:text-gray-100 group-hover:text-[#18546c] transition-colors">
                Manage Orders
              </h3>
              <p className="text-sm text-gray-500 dark:text-gray-400">
                {dashboardData.pendingOrders} orders to process
              </p>
            </div>
          </div>
        </Link>
      </div>
    </div>
  );
};

export default SupplierUserDashboard;
