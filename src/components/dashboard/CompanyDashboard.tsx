'use client';

import React, { useState } from 'react';
import { User } from '@/context/AuthContext';
import Link from 'next/link';
import { Fi<PERSON>lock, FiFileText, FiCheckCircle, FiShoppingBag, FiChevronRight, FiX } from 'react-icons/fi';

// Get the appropriate hover styles based on the background color
const getHoverStyles = (bgClass: string) => {
  if (bgClass.includes('orange')) return {
    border: 'group-hover:border-orange-300',
    sectionBg: 'group-hover:bg-orange-50 dark:group-hover:bg-orange-900/20',
    text: 'group-hover:text-orange-600 dark:group-hover:text-orange-400',
  };
  if (bgClass.includes('blue')) return {
    border: 'group-hover:border-blue-300',
    sectionBg: 'group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20',
    text: 'group-hover:text-blue-600 dark:group-hover:text-blue-400',
  };
  if (bgClass.includes('green')) return {
    border: 'group-hover:border-green-300',
    sectionBg: 'group-hover:bg-green-50 dark:group-hover:bg-green-900/20',
    text: 'group-hover:text-green-600 dark:group-hover:text-green-400',
  };
  if (bgClass.includes('purple')) return {
    border: 'group-hover:border-purple-300',
    sectionBg: 'group-hover:bg-purple-50 dark:group-hover:bg-purple-900/20',
    text: 'group-hover:text-purple-600 dark:group-hover:text-purple-400',
  };
  return {
    border: 'group-hover:border-blue-300',
    sectionBg: 'group-hover:bg-blue-50 dark:group-hover:bg-blue-900/20',
    text: 'group-hover:text-blue-600 dark:group-hover:text-blue-400',
  };
};

interface MetricCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  iconBgColor: string;
  iconTextColor: string;
  detailsLink: string;
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  icon,
  iconBgColor,
  iconTextColor,
  detailsLink,
}) => {
  const { border, sectionBg, text } = getHoverStyles(iconBgColor);
  
  return (
    <div className="group bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-transparent hover:border-gray-200 dark:hover:border-slate-700 p-6 transition-all duration-200 cursor-pointer">
      <div className="flex items-start space-x-4 mb-6">
        <div className={`p-2.5 rounded-lg ${iconBgColor} ${iconTextColor}`}>
          {icon}
        </div>
        <div>
          <p className="text-sm text-gray-500 dark:text-gray-400 font-medium">{title}</p>
          <p className="text-3xl font-bold text-gray-900 dark:text-gray-100 mt-2">{value}</p>
        </div>
      </div>
      <div className={`mt-4 pt-4 -mx-6 -mb-6 px-6 pb-4 rounded-b-lg transition-colors ${sectionBg} ${border}`}>
        <div className="ml-[75px]">
          <Link 
            href={detailsLink} 
            className={`text-sm font-medium flex items-center transition-colors text-gray-500 dark:text-gray-400 ${text} hover:opacity-80`}
          >
            View Details
            <FiChevronRight className="ml-1 h-4 w-4" />
          </Link>
        </div>
      </div>
    </div>
  );
};

interface CompanyDashboardProps {
  user: User;
}

const CompanyDashboard: React.FC<CompanyDashboardProps> = ({ user }) => {
  const [showCreateModal, setShowCreateModal] = useState(false);

  return (
    <div className="space-y-6 p-16">
      {/* Welcome Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4">
        <div>
          <h2 className="text-2xl font-semibold text-gray-900 dark:text-white">
            Hi {user.first_name || user.full_name?.split(' ')[0] || 'Admin'},
          </h2>
          <p className="text-sm text-gray-500 dark:text-gray-400">
            {new Date().toLocaleDateString('en-US', { weekday: 'long', day: 'numeric', month: 'long', year: 'numeric' })} • Here is a quick snapshot of your purchases.
          </p>
        </div>
        <button
          onClick={() => setShowCreateModal(true)}
          className="bg-gray-100 text-[#18546c] px-4 py-2 rounded-lg hover:bg-[#18546c] hover:text-white dark:bg-slate-800 dark:text-white dark:hover:bg-[#18546c] transition-colors duration-200 flex items-center font-medium text-sm shadow hover:shadow-md"
        >
          <span className="mr-1.5 text-base">+</span> Create New
        </button>
      </div>

      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4 overflow-x-auto pb-4">
        <MetricCard
          title="Submitted Requisitions"
          value="85"
          icon={<FiClock className="h-10 w-10" />}
          iconBgColor="bg-amber-100 dark:bg-amber-900/30"
          iconTextColor="text-amber-600 dark:text-amber-400"
          detailsLink="/requisitions"
        />

        <MetricCard
          title="Quotations"
          value="132"
          icon={<FiFileText className="h-10 w-10" />}
          iconBgColor="bg-blue-100 dark:bg-blue-900/30"
          iconTextColor="text-blue-600 dark:text-blue-400"
          detailsLink="/quotations"
        />

        <MetricCard
          title="For Approval"
          value="63"
          icon={<FiCheckCircle className="h-10 w-10" />}
          iconBgColor="bg-green-100 dark:bg-green-900/30"
          iconTextColor="text-green-600 dark:text-green-400"
          detailsLink="/approvals"
        />

        <MetricCard
          title="Purchase Orders"
          value="42"
          icon={<FiShoppingBag className="h-10 w-10" />}
          iconBgColor="bg-purple-100 dark:bg-purple-900/30"
          iconTextColor="text-purple-600 dark:text-purple-400"
          detailsLink="/purchase-orders"
        />

        <MetricCard
          title="Invoices"
          value="61"
          icon={
            <svg className="h-10 w-10" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
            </svg>
          }
          iconBgColor="bg-red-100 dark:bg-red-900/30"
          iconTextColor="text-red-600 dark:text-red-400"
          detailsLink="/invoices"
        />
      </div>

      {/* Assets & Inventory Section */}
      <div className="mt-8">
        <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-6">Assets & Inventory</h2>
        <div className="grid grid-cols-1 lg:grid-cols-6 gap-6">
          <div className="lg:col-span-2">
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm border border-transparent hover:border-gray-200 dark:hover:border-slate-700 p-6 transition-all duration-200 h-full">
              <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-4">Inventory Overview</h3>
              <p className="text-sm text-gray-500 dark:text-gray-400 mb-6">Check stock levels, create and approve stores requisitions</p>
              
              <div className="space-y-4">
                <div className="flex items-center p-3 bg-gray-50 dark:bg-slate-700/30 rounded-lg">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg mr-3">
                    <svg className="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium text-gray-800 dark:text-gray-200">Central Stores - Kampala</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">24 Luthuli Avenue, Bugolobi - Kampala</p>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-gray-50 dark:bg-slate-700/30 rounded-lg">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg mr-3">
                    <svg className="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium text-gray-800 dark:text-gray-200">Mpigi Stores</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">41 Kiwamirembe Lane, Mpigi</p>
                  </div>
                </div>
                
                <div className="flex items-center p-3 bg-gray-50 dark:bg-slate-700/30 rounded-lg">
                  <div className="p-2 bg-blue-100 dark:bg-blue-900/30 rounded-lg mr-3">
                    <svg className="h-6 w-6 text-blue-600 dark:text-blue-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1.5} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                  </div>
                  <div>
                    <p className="font-medium text-gray-800 dark:text-gray-200">Lira Stores</p>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">12 Obote Road, Lira</p>
                  </div>
                </div>
                
                <div className="mt-4">
                  <Link href="#" className="text-sm text-blue-600 dark:text-blue-400 hover:underline flex items-center transition-colors">
                    View All
                    <svg className="ml-1 w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14 5l7 7m0 0l-7 7m7-7H3" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          </div>
          
          <div className="lg:col-span-4">
            <div className="bg-white dark:bg-slate-800 rounded-lg shadow p-6 h-full">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-900 dark:text-white">Budget Performance</h3>
                <svg className="h-10 w-10 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              
              <div className="flex justify-between mb-6">
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-red-400 mr-2"></div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Total Expenditure</p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">$252,060</p>
                  </div>
                </div>
                <div className="flex items-center">
                  <div className="w-3 h-3 rounded-full bg-blue-400 mr-2"></div>
                  <div>
                    <p className="text-xs text-gray-500 dark:text-gray-400">Budget Balance</p>
                    <p className="text-lg font-bold text-gray-900 dark:text-white">$23,205</p>
                  </div>
                </div>
                <div className="text-right">
                  <button className="text-xs text-gray-500 dark:text-gray-400 flex items-center">
                    USD
                    <svg className="h-4 w-4 inline-block text-gray-400 ml-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </button>
                </div>
              </div>
              
              <div className="h-64 bg-gray-50 dark:bg-slate-700/30 rounded-lg flex items-center justify-center">
                <div className="text-center p-6">
                  <svg 
                    className="w-16 h-16 mx-auto text-gray-400 dark:text-gray-500" 
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={1.5} 
                      d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" 
                    />
                  </svg>
                  <h4 className="mt-3 text-sm font-medium text-gray-900 dark:text-gray-100">Budget Performance</h4>
                  <p className="mt-1 text-sm text-gray-500 dark:text-gray-400">Chart will be displayed here</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Create New Requisition Modal */}
      {showCreateModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-lg p-6 w-full max-w-md">
            <div className="flex items-center justify-between mb-6">
              <h3 className="text-lg font-medium text-gray-900">Create new requisition</h3>
              <button
                onClick={() => setShowCreateModal(false)}
                className="text-gray-400 hover:text-gray-600"
              >
                <FiX className="h-5 w-5" />
              </button>
            </div>

            <div className="space-y-3">
              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg text-left transition-colors duration-200">
                Goods procurement
              </button>

              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg text-left transition-colors duration-200">
                Services procurement
              </button>

              <button className="w-full bg-gray-100 hover:bg-gray-200 text-gray-700 px-4 py-3 rounded-lg text-left transition-colors duration-200">
                Stores requisition
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default CompanyDashboard;
