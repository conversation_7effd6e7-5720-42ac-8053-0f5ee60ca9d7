'use client';

import React, { useState, useEffect, memo } from 'react';
import Link from 'next/link';
import { usePathname } from 'next/navigation';
import {
  Squares2X2Icon,
  BriefcaseIcon,
  UsersIcon,
  EnvelopeIcon,
  ChartBarIcon,
  TrashIcon,
  ArchiveBoxIcon,
  Cog6ToothIcon,
  QuestionMarkCircleIcon,
  ArrowRightOnRectangleIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  Bars3Icon,
  XMarkIcon,
  PlusIcon,
  EyeIcon,
  CircleStackIcon,
  UserPlusIcon,
  ChevronLeftIcon,
  ChevronRightIcon
} from '@heroicons/react/20/solid';
import { Logo } from '@/components/logo';
import InviteSuppliersModal from '@/components/Modals/InviteSuppliersModal';

interface AppSidebarProps {
  isMobileOpen?: boolean;
  onMobileClose?: () => void;
  isCollapsed?: boolean;
  onToggleCollapse?: () => void;
}

interface NavSubSubItem {
  title: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string; size?: number }>;
  className?: string;
  size?: number;
  onClick?: () => void;
  external?: boolean;
}

interface NavSubItem {
  title: string;
  href?: string;
  icon?: React.ComponentType<{ className?: string; size?: number }>;
  className?: string;
  size?: number;
  onClick?: () => void;
  external?: boolean;
  items?: NavSubSubItem[];
}

interface NavItem {
  title: string;
  href?: string;
  icon: React.ComponentType<{ className?: string; size?: number }>;
  items?: NavSubItem[];
  badge?: string;
  divider?: boolean;
}

interface NavSection {
  title?: string;
  items: NavItem[];
}

const AppSidebar = ({ isMobileOpen = false, onMobileClose, isCollapsed = false, onToggleCollapse }: AppSidebarProps) => {
  const pathname = usePathname();
  const [expandedItem, setExpandedItem] = useState<string>('');
  const [expandedSubItem, setExpandedSubItem] = useState<string>('');
  const [isInviteModalOpen, setInviteModalOpen] = useState(false);
  const [isMobile, setIsMobile] = useState(false);

  // Check if we're on mobile on initial render and when window resizes
  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };

    // Initial check
    checkIfMobile();

    // Add event listener for window resize
    window.addEventListener('resize', checkIfMobile);

    // Cleanup
    return () => window.removeEventListener('resize', checkIfMobile);
  }, []);

  // No auto-expand behavior - all menus remain collapsed by default
  // useEffect(() => {
  //   if (pathname.startsWith('/supplier-database') && expandedItem !== 'Supplier Database') {
  //     setExpandedItem('Supplier Database');
  //   }
  // }, [pathname, expandedItem]);

  const openInviteModal = () => setInviteModalOpen(true);
  const closeInviteModal = () => setInviteModalOpen(false);

  const isActive = (path: string) => pathname === path;
  const isParentActive = (items?: NavSubItem[]) => {
    if (!items) return false;
    return items.some(item => item.href && pathname.startsWith(item.href));
  };

  const toggleExpand = (title: string) => {
    setExpandedItem(prev => prev === title ? '' : title);
  };

  const toggleSubExpand = (title: string) => {
    setExpandedSubItem(prev => prev === title ? '' : title);
  };
  
  // Function to handle navigation item click - alias for backward compatibility
  const toggleExpanded = toggleExpand;

  // Navigation data structure
  const navigationSections: NavSection[] = [
    {
      items: [
        {
          title: 'Dashboard',
          href: '/dashboard',
          icon: Squares2X2Icon,
        },
        {
          title: 'Projects',
          icon: BriefcaseIcon,
          items: [
            {
              title: '0001 Administration',
              icon: BriefcaseIcon,
              items: [
                {
                  title: 'Requisition',
                  href: '/projects/0001/requisition',
                  icon: BriefcaseIcon,
                },
                {
                  title: 'Inquiry',
                  href: '/projects/0001/inquiry',
                  icon: BriefcaseIcon,
                },
                {
                  title: 'PO',
                  href: '/projects/0001/po',
                  icon: BriefcaseIcon,
                },
              ],
            },
            {
              title: '0002 Mechanical',
              icon: BriefcaseIcon,
              items: [
                {
                  title: 'Requisition',
                  href: '/projects/0002/requisition',
                  icon: BriefcaseIcon,
                },
                {
                  title: 'Inquiry',
                  href: '/projects/0002/inquiry',
                  icon: BriefcaseIcon,
                },
                {
                  title: 'PO',
                  href: '/projects/0002/po',
                  icon: BriefcaseIcon,
                },
              ],
            },
            {
              title: '0003 Civil Works',
              icon: BriefcaseIcon,
              items: [
                {
                  title: 'Requisition',
                  href: '/projects/0003/requisition',
                  icon: BriefcaseIcon,
                },
                {
                  title: 'Inquiry',
                  href: '/projects/0003/inquiry',
                  icon: BriefcaseIcon,
                },
                {
                  title: 'PO',
                  href: '/projects/0003/po',
                  icon: BriefcaseIcon,
                },
              ],
            },
            {
              title: '0004 Legal & Audit',
              icon: BriefcaseIcon,
              items: [
                {
                  title: 'Requisition',
                  href: '/projects/0004/requisition',
                  icon: BriefcaseIcon,
                },
                {
                  title: 'Inquiry',
                  href: '/projects/0004/inquiry',
                  icon: BriefcaseIcon,
                },
                {
                  title: 'PO',
                  href: '/projects/0004/po',
                  icon: BriefcaseIcon,
                },
              ],
            },
            {
              title: '0005 Design',
              icon: BriefcaseIcon,
              items: [
                {
                  title: 'Requisition',
                  href: '/projects/0005/requisition',
                  icon: BriefcaseIcon,
                },
                {
                  title: 'Inquiry',
                  href: '/projects/0005/inquiry',
                  icon: BriefcaseIcon,
                },
                {
                  title: 'PO',
                  href: '/projects/0005/po',
                  icon: BriefcaseIcon,
                },
              ],
            },
          ],
        },
        {
          title: 'Supplier Database',
          icon: UsersIcon,
          items: [
            {
              title: 'View all suppliers',
              href: '/supplier-database',
              icon: CircleStackIcon,
              external: true,
            },
            {
              title: 'Pre-qualified suppliers',
              href: '/supplier-database/pre-qualified',
              icon: EyeIcon,
            },
            {
              title: 'Invitations',
              href: '/supplier-database/invitations',
              icon: EnvelopeIcon,
            },
            {
              title: 'Invite new suppliers',
              icon: UserPlusIcon,
              onClick: openInviteModal,
            },      
            
          ],
        },
        {
          title: 'Messages',
          href: '/messages',
          icon: EnvelopeIcon,
          badge: '3',
        },
        {
          title: 'Reports',
          href: '/reports',
          icon: ChartBarIcon,
          badge: '12',
        },
        {
          title: 'Bin',
          href: '/bin',
          icon: TrashIcon,
        },
        {
          title: 'Assets & Inventory',
          href: '/inventory',
          icon: ArchiveBoxIcon,
          badge: '8',
        },
        {
          title: 'Settings',
          href: '/settings',
          icon: Cog6ToothIcon,
          divider: true,
        },
        {
          title: 'Help Centre',
          href: '/help-centre',
          icon: QuestionMarkCircleIcon,
        },
      ],
    },
  ];

  // Sub-item renderer function
  const renderSubItem = (subItem: NavSubItem, isLast: boolean) => {
    const subItemIsActive = subItem.href ? isActive(subItem.href) : false;
    const hasSubSubItems = subItem.items && subItem.items.length > 0;
    const isSubExpanded = expandedSubItem === subItem.title;

    if (hasSubSubItems) {
      return (
        <div key={subItem.title} className="space-y-1">
          <button
            onClick={() => toggleSubExpand(subItem.title)}
            className={`flex items-center justify-between w-full py-2 pl-7 pr-3 text-sm font-medium rounded-r-lg transition-colors duration-300 ease-out group relative ${subItemIsActive || isSubExpanded ? 'text-[#2a6e78] bg-[#f0f4f8] before:absolute before:left-0 before:top-0 before:h-full before:w-[5px] before:bg-[#2a6e78]' : 'text-gray-700 hover:bg-[#f0f4f8] hover:text-[#2a6e78] hover:before:absolute hover:before:left-0 hover:before:top-0 hover:before:h-full hover:before:w-[5px] hover:before:bg-[#2a6e78]'} dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-[#2a6e78]`}
          >
            <span className="truncate flex-1 text-left">{subItem.title}</span>
            <div className="flex items-center mr-2">
              <ChevronDownIcon
                className={`h-3 w-3 flex-shrink-0 transition-transform duration-300 ease-out ${
                  isSubExpanded ? 'transform rotate-180' : ''
                } ${subItemIsActive || isSubExpanded ? 'text-[#2a6e78]' : 'text-gray-400'} will-change-transform`}
              />
            </div>
          </button>

          <div
            className={`mt-1 space-y-1 overflow-hidden transition-all duration-300 ease-out`}
            style={{
              maxHeight: isSubExpanded ? '300px' : '0px',
              opacity: isSubExpanded ? 1 : 0,
              willChange: 'max-height, opacity'
            }}
          >
            <div className="relative ml-6 space-y-0.5 py-1">
              {/* Continuous vertical line for 3rd level */}
              <div className="absolute left-0 top-0 bottom-0 w-px bg-gray-200 dark:bg-gray-700" />
              {subItem.items?.map((subSubItem, index, array) => (
                <div key={subSubItem.title} className="relative">
                  <Link
                    href={subSubItem.href || '#'}
                    className={`flex items-center py-1.5 pl-8 pr-3 text-xs font-medium rounded-r-lg transition-colors duration-300 ease-out group relative ${
                      subSubItem.href && isActive(subSubItem.href)
                        ? 'text-[#2a6e78] bg-[#f0f4f8] before:absolute before:left-0 before:top-0 before:h-full before:w-[3px] before:bg-[#2a6e78]'
                        : 'text-gray-600 hover:bg-[#f0f4f8] hover:text-[#2a6e78] hover:before:absolute hover:before:left-0 hover:before:top-0 hover:before:h-full hover:before:w-[3px] hover:before:bg-[#2a6e78]'
                    } dark:text-gray-400 dark:hover:bg-slate-700/50 dark:hover:text-[#2a6e78]`}
                    target={subSubItem.external ? '_blank' : undefined}
                    rel={subSubItem.external ? 'noopener noreferrer' : undefined}
                    onClick={subSubItem.onClick}
                  >
                    <span className="truncate">{subSubItem.title}</span>
                  </Link>
                </div>
              ))}
            </div>
          </div>
        </div>
      );
    }

    return (
      <>
        {/* No horizontal connector line - only vertical line is shown */}

        <Link
          href={subItem.href || '#'}
          className={`flex items-center py-2 pl-7 pr-3 text-sm font-medium rounded-r-lg transition-colors duration-300 ease-out group relative ${subItemIsActive ? 'text-[#2a6e78] bg-[#f0f4f8] before:absolute before:left-0 before:top-0 before:h-full before:w-[5px] before:bg-[#2a6e78]' : 'text-gray-700 hover:bg-[#f0f4f8] hover:text-[#2a6e78] hover:before:absolute hover:before:left-0 hover:before:top-0 hover:before:h-full hover:before:w-[5px] hover:before:bg-[#2a6e78]'} dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-[#2a6e78]`}
          target={subItem.external ? '_blank' : undefined}
          rel={subItem.external ? 'noopener noreferrer' : undefined}
          onClick={subItem.onClick}
        >
          <span className="truncate">{subItem.title}</span>
        </Link>
      </>
    );
  };

  // Navigation item renderer function
  const renderNavItem = (item: NavItem) => {
    const hasSubItems = item.items && item.items.length > 0;
    const isExpanded = expandedItem === item.title;
    const itemIsActive = item.href ? isActive(item.href) : isParentActive(item.items);

    if (hasSubItems) {
      return (
        <div key={item.title} className="space-y-1">
          <button
            onClick={() => toggleExpand(item.title)}
            className={`flex items-center justify-between w-full py-2.5 text-sm font-medium rounded-r-lg transition-all duration-300 ease-out group relative ${itemIsActive || isExpanded ? 'text-[#2a6e78] bg-[#f0f4f8] before:absolute before:left-0 before:top-0 before:h-full before:w-[5px] before:bg-[#2a6e78]' : 'text-gray-700 hover:bg-[#f0f4f8] hover:text-[#2a6e78] hover:before:absolute hover:before:left-0 hover:before:top-0 hover:before:h-full hover:before:w-[5px] hover:before:bg-[#2a6e78]'} dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-[#2a6e78] mr-2 pl-6 pr-3 will-change-transform`}
          >
            <div className="flex items-center flex-1">
              <item.icon
                className={`mr-3 h-5 w-5 flex-shrink-0 transition-colors duration-300 ease-out ${itemIsActive || isExpanded ? 'text-[#2a6e78]' : 'text-gray-500 group-hover:text-[#2a6e78]'}`}
              />
              <span className="truncate font-medium text-left">{item.title}</span>
            </div>
            <div className="flex items-center space-x-2 mr-2">
              {item.badge && (
                <span className="text-[10px] font-medium text-gray-600 bg-gray-200 dark:text-gray-400 dark:bg-gray-700 px-2 rounded-full inline-flex items-center justify-center min-w-[20px] h-5 transform transition-transform duration-300 ease-out hover:scale-110">
                  {item.badge}
                </span>
              )}
              <ChevronDownIcon
                className={`h-4 w-4 flex-shrink-0 transition-transform duration-300 ease-out ${
                  isExpanded ? 'transform rotate-180' : ''
                } ${itemIsActive || isExpanded ? 'text-[#2a6e78]' : 'text-gray-400'} will-change-transform`}
              />
            </div>
          </button>

          <div
            className={`mt-1 space-y-1 overflow-hidden transition-all duration-300 ease-out`}
            style={{
              maxHeight: isExpanded ? '500px' : '0px',
              opacity: isExpanded ? 1 : 0,
              willChange: 'max-height, opacity'
            }}
          >
            <div className="relative ml-8 space-y-0.5 py-1">
              {/* Continuous vertical line */}
              <div className="absolute left-0 top-0 bottom-0 w-px bg-gray-300 dark:bg-gray-600 group-hover:bg-[#2a6e78]" />
              {item.items?.map((subItem, index, array) => (
                <div key={subItem.title} className="relative">
                  {renderSubItem(subItem, index === array.length - 1)}
                </div>
              ))}
            </div>
          </div>
        </div>
      );
    }

    // Regular navigation item
    if (item.href) {
      return (
        <Link
          key={item.title}
          href={item.href || '#'}
          onClick={() => isMobile && onMobileClose?.()}
          className={`flex items-center py-2.5 text-sm font-medium rounded-r-lg transition-all duration-300 ease-out group relative ${itemIsActive ? 'text-[#2a6e78] bg-[#f0f4f8] before:absolute before:left-0 before:top-0 before:h-full before:w-[5px] before:bg-[#2a6e78]' : 'text-gray-700 hover:bg-[#f0f4f8] hover:text-[#2a6e78] hover:before:absolute hover:before:left-0 hover:before:top-0 hover:before:h-full hover:before:w-[5px] hover:before:bg-[#2a6e78]'} dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-[#2a6e78] mr-2 pl-6 pr-3`}
        >
          <item.icon
            className={`mr-3 h-5 w-5 flex-shrink-0 transition-colors duration-300 ease-out ${itemIsActive ? 'text-[#2a6e78]' : 'text-gray-500 group-hover:text-[#2a6e78]'}`}
          />
          <div className="flex items-center justify-between w-full">
            <span className="truncate font-medium">{item.title}</span>
            {item.badge && (
              <span className="ml-2 text-[10px] font-medium text-gray-600 bg-gray-200 dark:text-gray-400 dark:bg-gray-700 px-2 rounded-full inline-flex items-center justify-center min-w-[20px] h-5 transform transition-transform duration-300 ease-out hover:scale-110">
                {item.badge}
              </span>
            )}
          </div>
        </Link>
      );
    }

    return (
      <Link
        key={item.title}
        href={item.href || '#'}
        prefetch={false} // Only prefetch when needed
        className={`flex items-center py-2.5 text-sm font-medium rounded-r-lg transition-all duration-300 ease-out group relative ${itemIsActive ? 'text-[#2a6e78] bg-[#f0f4f8] before:absolute before:left-0 before:top-0 before:h-full before:w-[5px] before:bg-[#2a6e78]' : 'text-gray-700 hover:bg-[#f0f4f8] hover:text-[#2a6e78] hover:before:absolute hover:before:left-0 hover:before:top-0 hover:before:h-full hover:before:w-[5px] hover:before:bg-[#2a6e78]'} dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-[#2a6e78] mr-2 pl-6 pr-3 will-change-transform`}
      >
        <item.icon
          className={`mr-3 h-5 w-5 flex-shrink-0 transition-transform duration-300 ease-out ${itemIsActive ? 'text-[#2a6e78]' : 'text-gray-500 group-hover:text-[#2a6e78]'} group-hover:scale-110 will-change-transform`}
        />
        <div className="flex items-center justify-between w-full">
          <span className="truncate font-medium">{item.title}</span>
          {item.badge && (
            <span className="ml-2 text-[10px] font-medium text-gray-600 bg-gray-200 dark:text-gray-400 dark:bg-gray-700 px-2 rounded-full inline-flex items-center justify-center min-w-[20px] h-5 transform transition-transform duration-300 ease-out hover:scale-110">
              {item.badge}
            </span>
          )}
        </div>
      </Link>
    );
  };

  return (
    <div className="relative h-full overflow-visible">
      {/* Invite Suppliers Modal */}
      <InviteSuppliersModal isOpen={isInviteModalOpen} onClose={closeInviteModal} />
      
      <aside className={`${isMobile ? 'fixed inset-y-0 left-0 z-50 transform transition-transform duration-300 ease-in-out' : 'relative h-full'} ${isMobile && !isMobileOpen ? '-translate-x-full' : 'translate-x-0'} ${isCollapsed ? 'w-16' : 'w-64'} transition-all duration-300 flex-shrink-0 bg-white dark:bg-slate-800 shadow-xl border-r border-gray-200 dark:border-slate-700 flex flex-col overflow-visible`}>
        {/* Toggle button positioned exactly in the vertical middle of sidebar */}
        {!isMobile && onToggleCollapse && (
          <div className="absolute right-0 top-1/2 -translate-y-1/2 z-20 flex items-center justify-center">
            <div className="relative">
              {/* Curved bend/tab with rounded corners on the right side - 3x height */}
              <div className="absolute top-0 -right-5 h-[120px] w-5 bg-white dark:bg-slate-800 border-t border-r border-b border-gray-200 dark:border-slate-700 rounded-tr-[24px] rounded-br-[24px]"></div>
              
              {/* Toggle button - exact center of the tab - 3x height */}
              <button
                onClick={onToggleCollapse}
                className="absolute flex items-center justify-center h-[120px] w-5 -right-5 text-gray-500 dark:text-gray-400 hover:text-[#18546c] dark:hover:text-[#2a6e78] focus:outline-none z-10"
                aria-label="Toggle sidebar"
              >
                {isCollapsed ? (
                  <ChevronRightIcon className="h-4 w-4" />
                ) : (
                  <ChevronLeftIcon className="h-4 w-4" />
                )}
              </button>
            </div>
          </div>
        )}
      {/* Header */}
      <div className="h-16 flex items-center bg-[#18546c] px-6 shadow-sm">
        <div className="flex items-center">
          <div className="scale-75 origin-left">
            <Logo variant="dark" />
          </div>
        </div>
        {isMobile && (
          <button
            onClick={onMobileClose}
            className="text-white p-2 rounded-full hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/20 transition-colors"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        )}
      </div>
      {/* Navigation */}
      <div className="flex-1 flex flex-col overflow-y-auto">
        <nav className="pr-3 py-4 space-y-6">
          {navigationSections.map((section, sectionIndex) => (
            <div key={sectionIndex} className="space-y-2">
              {section.title && (
                <h3 
                  className={`pl-6 pr-3 text-xs font-semibold text-gray-500 uppercase tracking-wider mb-2 transition-all duration-300 ${isCollapsed ? 'opacity-0 h-0 m-0 overflow-hidden' : 'opacity-100'}`}
                >
                  {section.title}
                </h3>
              )}
              {section.items.map((item, itemIndex) => (
                <React.Fragment key={item.title}>
                  {item.divider && (
                    <div className="my-6 border-t border-gray-200 dark:border-slate-700" />
                  )}
                  <div className={itemIndex > 0 && !item.divider ? "mt-1" : ""}>
                    {renderNavItem(item)}
                  </div>
                </React.Fragment>
              ))}
            </div>
          ))}
        </nav>

        {/* Logout at the bottom */}
        <div className="mt-auto pt-4 border-t border-gray-200 dark:border-slate-700 pr-1">
          <Link
            href="/logout"
            onClick={() => isMobile && onMobileClose?.()}
            className={`flex items-center py-2.5 text-sm font-medium rounded-r-lg transition-all duration-200 group text-gray-700 hover:bg-[#f0f4f8] hover:text-red-600 dark:text-gray-300 dark:hover:bg-slate-700/50 dark:hover:text-red-400 mr-2 ${isCollapsed ? 'justify-center pl-2 pr-2' : 'pl-6 pr-3'}`}
            title="Logout"
          >
            <ArrowRightOnRectangleIcon className={`${isCollapsed ? '' : 'mr-3'} h-4 w-4 flex-shrink-0 transition-colors text-gray-500 group-hover:text-red-600 dark:group-hover:text-red-400`} />
            {!isCollapsed && <span className="truncate font-medium">Logout</span>}
          </Link>
        </div>
      </div>
      {/* Invite Suppliers Modal */}
      <InviteSuppliersModal isOpen={isInviteModalOpen} onClose={closeInviteModal} />
    </aside>

    {/* We removed the redundant toggle button here since we already have one on the sidebar */}
  </div>
  );
};

export const MobileMenuButton = ({ onClick }: { onClick: () => void }) => {
  return (
    <div className="relative md:hidden">
      {/* Curved bend around the button */}
      <div className="absolute -right-3 top-1/2 transform -translate-y-1/2 w-6 h-12 bg-white dark:bg-slate-800 rounded-l-full z-0 shadow-md"></div>
      
      {/* Button with higher z-index to appear above the bend */}
      <button
        onClick={onClick}
        className="relative z-10 p-2 rounded-full text-white hover:bg-white/10 focus:outline-none focus:ring-2 focus:ring-white/20 transition-colors"
        aria-label="Open mobile menu"
      >
        <Bars3Icon className="h-5 w-5" />
      </button>
    </div>
  );
};

export default AppSidebar;
