'use client';

import React, { useState, useEffect, useRef } from 'react';
import { useNotifications } from '@/context/NotificationContext';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Button } from '@/components/ui/button';
import { formatDistanceToNow } from 'date-fns';
import Link from 'next/link';

interface NotificationDropdownProps {
  isOpen: boolean;
  onClose: () => void;
}

const NotificationDropdown: React.FC<NotificationDropdownProps> = ({ isOpen, onClose }) => {
  const { notifications, loading, error, markAllAsViewed, markAsViewed } = useNotifications();
  const dropdownRef = useRef<HTMLDivElement>(null);
  
  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    } else {
      document.removeEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  if (!isOpen) return null;

  const getNotificationTypeIcon = (type: string) => {
    switch (type) {
      case 'status_change':
        return <span className="text-blue-500">⟳</span>;
      case 'new_quote':
        return <span className="text-green-500">$</span>;
      case 'case_assignment':
        return <span className="text-orange-500">⇥</span>;
      case 'new_invoice':
        return <span className="text-purple-500">$</span>;
      case 'new_credit_note':
        return <span className="text-green-500">↓</span>;
      case 'new_debit_note':
        return <span className="text-red-500">↑</span>;
      default:
        return <span className="text-gray-500">•</span>;
    }
  };

  // Function to get notification link based on type
  const getNotificationLink = (notification: any) => {
    const { type, procurementCaseId } = notification;
    
    switch (type) {
      case 'status_change':
      case 'case_assignment':
        return `/procurement/${procurementCaseId}`;
      case 'new_quote':
        return `/procurement/${procurementCaseId}/quotes`;
      case 'new_invoice':
      case 'new_credit_note':
      case 'new_debit_note':
        return `/procurement/${procurementCaseId}/invoices`;
      default:
        return '#';
    }
  };

  return (
    <div 
      ref={dropdownRef}
      className="absolute right-0 top-full mt-2 w-80 bg-white dark:bg-gray-800 rounded-md shadow-lg z-50 overflow-hidden"
    >
      <div className="p-3 bg-gray-50 dark:bg-gray-700 border-b dark:border-gray-600 flex justify-between items-center">
        <h3 className="text-sm font-medium">Notifications</h3>
        <Button 
          variant="ghost" 
          size="sm" 
          onClick={markAllAsViewed}
          className="text-xs hover:bg-gray-200 dark:hover:bg-gray-600"
        >
          Mark all as read
        </Button>
      </div>
      
      <ScrollArea className="max-h-80">
        {loading ? (
          <div className="p-4 text-center text-sm text-gray-500">Loading notifications...</div>
        ) : error ? (
          <div className="p-4 text-center text-sm text-red-500">Failed to load notifications</div>
        ) : notifications.length === 0 ? (
          <div className="p-4 text-center text-sm text-gray-500">No notifications</div>
        ) : (
          <div>
            {notifications.map((notification) => (
              <Link 
                href={getNotificationLink(notification)} 
                key={notification.id}
                onClick={() => markAsViewed(notification.id)}
                className="block border-b dark:border-gray-700 last:border-0"
              >
                <div className={`p-3 hover:bg-gray-50 dark:hover:bg-gray-700 flex items-start gap-3 ${!notification.isRead ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}>
                  <div className="mt-1">
                    {getNotificationTypeIcon(notification.type)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <div className="flex justify-between items-start">
                      <p className={`text-sm ${!notification.isRead ? 'font-medium' : ''}`}>{notification.subject}</p>
                      {!notification.isRead && (
                        <Badge variant="default" className="ml-2 bg-blue-500">New</Badge>
                      )}
                    </div>
                    <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
                      {notification.caseReference}
                    </p>
                    <p className="text-xs text-gray-600 dark:text-gray-300 line-clamp-2 mt-1">
                      {notification.message}
                    </p>
                    <p className="text-xs text-gray-400 dark:text-gray-500 mt-1">
                      {formatDistanceToNow(new Date(notification.createdAt), { addSuffix: true })}
                    </p>
                  </div>
                </div>
              </Link>
            ))}
          </div>
        )}
      </ScrollArea>
      
      <div className="p-2 bg-gray-50 dark:bg-gray-700 border-t dark:border-gray-600 text-center">
        <Link href="/notifications" className="text-xs text-blue-600 dark:text-blue-400 hover:underline">
          View all notifications
        </Link>
      </div>
    </div>
  );
};

export default NotificationDropdown;
