'use client';

import React, { useState, useEffect } from 'react';
import Link from 'next/link';
import { useTheme } from 'next-themes';
import { MagnifyingGlassIcon, BellIcon, Cog6ToothIcon, ArrowRightOnRectangleIcon, SunIcon, MoonIcon, XMarkIcon, ChevronDownIcon, ChatBubbleBottomCenterTextIcon, CheckCircleIcon, ArchiveBoxIcon } from '@heroicons/react/24/solid';
import { useAuth } from '@/context/AuthContext';
import { useNotifications } from '@/context/NotificationContext';
import messageService from '@/services/message.service';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Button } from '@/components/ui/button';
import { DropdownMenu, DropdownMenuContent, DropdownMenuGroup, DropdownMenuItem, DropdownMenuLabel, DropdownMenuSeparator, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { HeaderMenu } from './HeaderMenu';
import NotificationDropdown from './NotificationDropdown';
import MessageDropdown from './MessageDropdown';
import { Input } from '@/components/ui/input';

interface AppHeaderProps {
  className?: string;
}

const AppHeader: React.FC<AppHeaderProps> = ({ className }) => {
  const [isMobile, setIsMobile] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [isNotificationOpen, setIsNotificationOpen] = useState(false);
  const [isMessageOpen, setIsMessageOpen] = useState(false);
  const [isToolsDropdownOpen, setToolsDropdownOpen] = useState(false);
  const [isAdminSubmenuOpen, setAdminSubmenuOpen] = useState(false);
  const [isDocumentTemplatesSubmenuOpen, setDocumentTemplatesSubmenuOpen] = useState(false);
  const [folderCounts, setFolderCounts] = useState<{ [key: string]: number }>({});
  
  // Check if we're on mobile on initial render and when window resizes
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768);
    };

    const handleClickOutside = (event: MouseEvent) => {
      // Close dropdowns when clicking outside
      if (isToolsDropdownOpen) {
        const target = event.target as HTMLElement;
        if (!target.closest('.tools-dropdown')) {
          setToolsDropdownOpen(false);
          setAdminSubmenuOpen(false);
          setDocumentTemplatesSubmenuOpen(false);
        }
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    document.addEventListener('mousedown', handleClickOutside);
    
    // Fetch folder counts for message indicator
    const fetchFolderCounts = async () => {
      try {
        const counts = await messageService.getFolderCounts();
        setFolderCounts(counts as unknown as { [key: string]: number });
      } catch (error) {
        console.error('Error fetching folder counts:', error);
      }
    };
    
    fetchFolderCounts();
    
    return () => {
      window.removeEventListener('resize', handleResize);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isToolsDropdownOpen]);


  
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Searching for:', searchTerm);
    // Implement your search functionality here
  };

  // Function to get user initials from first and last name
  const getUserInitials = () => {
    if (!user) return 'U';

    const firstName = user.first_name || '';
    const lastName = user.last_name || '';

    // If we have both first and last name
    if (firstName && lastName) {
      return `${firstName.charAt(0).toUpperCase()}${lastName.charAt(0).toUpperCase()}`;
    }

    // If we only have full_name, try to extract first and last
    if (user.full_name) {
      const nameParts = user.full_name.trim().split(' ');
      if (nameParts.length >= 2) {
        return `${nameParts[0].charAt(0).toUpperCase()}${nameParts[nameParts.length - 1].charAt(0).toUpperCase()}`;
      }
      // If only one name, use first two characters or just first character
      return nameParts[0].length >= 2
        ? `${nameParts[0].charAt(0).toUpperCase()}${nameParts[0].charAt(1).toUpperCase()}`
        : nameParts[0].charAt(0).toUpperCase();
    }

    // Fallback
    return 'U';
  };

  const { user, logout } = useAuth();
  const { theme, setTheme } = useTheme();
  const { unreadCount } = useNotifications();

  const toggleTheme = () => {
    setTheme(theme === 'light' ? 'dark' : 'light');
  };

  const roleMap: { [key: string]: string } = {
    s_admin: 'Supplier Admin',
    c_admin: 'Company Admin',
  };

  const firstName = user?.first_name || '';  
  const lastName = user?.last_name || '';
  const fullName = firstName && lastName ? `${firstName} ${lastName}` : 
                   firstName || lastName || user?.full_name || 'User Name';
  const userRole = user?.role || '';
  const displayRole = roleMap[userRole] || userRole || 'No role assigned';
  const avatarFallback = getUserInitials();

  return (
    <header className={`h-16 bg-[#18546c] flex-1 flex items-center justify-between px-2 md:px-6 ${className}`}>
      {/* Left side - Main menu */}
      <div className="flex items-center space-x-6">
        {/* Main menu items - hidden on mobile */}
        <div className="hidden md:flex items-center space-x-4 text-white text-sm">
          <button className="hover:bg-black/10 px-2 py-1 rounded">File</button>

          {/* Tools Dropdown with nested Admin Settings submenu */}
          <div className="relative tools-dropdown">
            <button 
              onClick={() => setToolsDropdownOpen(!isToolsDropdownOpen)} 
              className="hover:bg-black/10 px-2 py-1 rounded text-white text-sm flex items-center"
            >
              Tools
              <ChevronDownIcon className="h-4 w-4 ml-1" />
            </button>
            
            {/* Main Tools Dropdown */}
            {isToolsDropdownOpen && (
              <div className="absolute left-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 w-56 z-50 text-gray-800 dark:text-gray-200">
                <button className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <span className="font-medium">Price comparison</span>
                </button>
                <Link
                  href="/procurement-plan"
                  className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => {
                    setToolsDropdownOpen(false);
                    setAdminSubmenuOpen(false);
                  }}
                >
                  <span>Procurement plan</span>
                </Link>
                <button className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <span>Set collaborators</span>
                </button>
                <div 
                  className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between cursor-pointer"
                  onMouseEnter={() => setAdminSubmenuOpen(true)}
                >
                  <span className="font-medium">Admin settings</span>
                  <ChevronDownIcon className="h-4 w-4 transform -rotate-90 text-gray-500 dark:text-gray-400" />
                </div>
              </div>
            )}
            
            {/* Admin Settings Submenu */}
            {isToolsDropdownOpen && isAdminSubmenuOpen && (
              <div
                className="absolute left-0 top-full mt-1 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 w-56 z-50 text-gray-800 dark:text-gray-200"
                onMouseLeave={() => setAdminSubmenuOpen(false)}
              >
                <Link 
                  href="/admin-settings" 
                  className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => {
                    setToolsDropdownOpen(false);
                    setAdminSubmenuOpen(false);
                  }}
                >
                  <span className="font-medium">Admin Settings Home</span>
                </Link>
                <Link 
                  href="/admin-settings/manage-users" 
                  className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => {
                    setToolsDropdownOpen(false);
                    setAdminSubmenuOpen(false);
                  }}
                >
                  <span>Manage users</span>
                </Link>
                <Link 
                  href="/admin-settings/procurement-groups" 
                  className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => {
                    setToolsDropdownOpen(false);
                    setAdminSubmenuOpen(false);
                  }}
                >
                  <span>Procurement groups</span>
                </Link>
                <Link 
                  href="/admin-settings/budget-codes" 
                  className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => {
                    setToolsDropdownOpen(false);
                    setAdminSubmenuOpen(false);
                  }}
                >
                  <span>Budget codes</span>
                </Link>
                <Link 
                  href="/admin-settings/budget-approval-limits" 
                  className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => {
                    setToolsDropdownOpen(false);
                    setAdminSubmenuOpen(false);
                  }}
                >
                  <span>Budget approval limits</span>
                </Link>
                <Link 
                  href="/budget-approval-limits" 
                  className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                  onClick={() => {
                    setToolsDropdownOpen(false);
                    setAdminSubmenuOpen(false);
                  }}
                >
                  <span>View Budget Limits (User)</span>
                </Link>
                <button className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <span>Manage suppliers</span>
                </button>
                <button className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <span>Spend groups</span>
                </button>
                <div className="relative">
                  <div
                    className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700 flex items-center justify-between cursor-pointer"
                    onMouseEnter={() => setDocumentTemplatesSubmenuOpen(true)}
                    onMouseLeave={() => setDocumentTemplatesSubmenuOpen(false)}
                  >
                    <span className="font-medium">Document templates</span>
                    <ChevronDownIcon className="h-4 w-4 transform -rotate-90 text-gray-500 dark:text-gray-400" />
                  </div>

                  {/* Document Templates Submenu */}
                  {isDocumentTemplatesSubmenuOpen && (
                    <div
                      className="absolute left-full top-0 ml-1 bg-white dark:bg-gray-800 rounded-md shadow-lg py-1 w-56 z-50 text-gray-800 dark:text-gray-200"
                      onMouseEnter={() => setDocumentTemplatesSubmenuOpen(true)}
                      onMouseLeave={() => setDocumentTemplatesSubmenuOpen(false)}
                    >
                      {/* Company Admin sees Document templates directly */}
                      {user?.role === 'c_admin' && (
                        <Link
                          href="/document-templates"
                          className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                          onClick={() => {
                            setToolsDropdownOpen(false);
                            setDocumentTemplatesSubmenuOpen(false);
                          }}
                        >
                          <span>Document templates</span>
                        </Link>
                      )}

                      {/* Supplier Admin sees Sales and Purchases Templates */}
                      {user?.role === 's_admin' && (
                        <>
                          <Link
                            href="/company-templates"
                            className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                            onClick={() => {
                              setToolsDropdownOpen(false);
                              setDocumentTemplatesSubmenuOpen(false);
                            }}
                          >
                            <span>Sales Templates</span>
                          </Link>
                          <Link
                            href="/supplier-templates"
                            className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                            onClick={() => {
                              setToolsDropdownOpen(false);
                              setDocumentTemplatesSubmenuOpen(false);
                            }}
                          >
                            <span>Purchases Templates</span>
                          </Link>
                        </>
                      )}

                      {/* Super Admin sees all options */}
                      {(user?.role === 'super_admin' || user?.role === 'admin') && (
                        <>
                          <Link
                            href="/company-templates"
                            className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                            onClick={() => {
                              setToolsDropdownOpen(false);
                              setDocumentTemplatesSubmenuOpen(false);
                            }}
                          >
                            <span>Sales Templates</span>
                          </Link>
                          <Link
                            href="/supplier-templates"
                            className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                            onClick={() => {
                              setToolsDropdownOpen(false);
                              setDocumentTemplatesSubmenuOpen(false);
                            }}
                          >
                            <span>Purchases Templates</span>
                          </Link>
                          <Link
                            href="/document-templates"
                            className="block w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700"
                            onClick={() => {
                              setToolsDropdownOpen(false);
                              setDocumentTemplatesSubmenuOpen(false);
                            }}
                          >
                            <span>Document templates</span>
                          </Link>
                        </>
                      )}
                    </div>
                  )}
                </div>
                <button className="w-full text-left px-3 py-2 hover:bg-gray-100 dark:hover:bg-gray-700">
                  <span>Manage subscription</span>
                </button>
              </div>
            )}

          </div>

          <button className="hover:bg-black/10 px-2 py-1 rounded">Print</button>
          <button className="hover:bg-black/10 px-2 py-1 rounded flex items-center">
            Event log
            <ChevronDownIcon className="h-4 w-4 ml-1" />
          </button>
          <button className="hover:bg-black/10 px-2 py-1 rounded flex items-center">
            View
            <ChevronDownIcon className="h-4 w-4 ml-1" />
          </button>
          <button className="hover:bg-black/10 px-2 py-1 rounded">Help</button>
        </div>
        
        {/* Mobile menu */}
        {isMobile && (
          <HeaderMenu isMobile={true} />
        )}
      </div>

      {/* Right side - Search, Actions, and User Profile */}
      <div className="flex items-center space-x-2 md:space-x-5">
        {/* Mobile Search Button */}
        {isMobile && !isSearchOpen && (
          <button 
            onClick={() => setIsSearchOpen(true)}
            className="p-2 rounded-full text-white hover:bg-black/10 focus:outline-none"
            aria-label="Open search"
          >
            <MagnifyingGlassIcon className="h-5 w-5" />
          </button>
        )}
        
        {/* Expanded Mobile Search */}
        {isMobile && isSearchOpen ? (
          <div className="absolute inset-x-0 top-0 bg-[#2A6E78] h-16 z-50 px-2 flex items-center">
            <form onSubmit={handleSearch} className="flex-1 flex items-center">
              <Input
                type="text"
                placeholder="Search..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="flex-1 bg-white/90 border-transparent focus:border-white"
                autoFocus
              />
              <Button type="submit" variant="ghost" className="ml-2 text-white">
                <MagnifyingGlassIcon className="h-5 w-5" />
              </Button>
              <Button 
                type="button" 
                variant="ghost" 
                className="ml-1 text-white" 
                onClick={() => setIsSearchOpen(false)}
              >
                <XMarkIcon className="h-5 w-5" />
              </Button>
            </form>
          </div>
        ) : (
          // Desktop Search Bar
          <div className="relative hidden md:block">
            <form onSubmit={handleSearch}>
              <MagnifyingGlassIcon className="absolute left-3 top-1/2 -translate-y-1/2 h-5 w-5 text-gray-500" />
              <input 
                type="text" 
                placeholder="Quick Search" 
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-64 pl-10 pr-4 py-2 text-sm bg-white border border-transparent rounded-md focus:outline-none focus:ring-2 focus:ring-cyan-500"
              />
            </form>
          </div>
        )}

        {/* Action Icons */}
        <div className="flex items-center space-x-1 md:space-x-3 text-white">
          {/* Notifications */}
          <div className="relative">
            <button 
              className="relative p-2 rounded-full hover:bg-black/10"
              onClick={() => setIsNotificationOpen(!isNotificationOpen)}
            >
              <BellIcon className="h-5 w-5" />
              {unreadCount > 0 && (
                <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center text-xs font-medium text-white">
                  {unreadCount > 99 ? '99+' : unreadCount}
                </span>
              )}
            </button>
            <NotificationDropdown 
              isOpen={isNotificationOpen} 
              onClose={() => setIsNotificationOpen(false)} 
            />
          </div>
          {/* Messages */}
          <div className="relative">
            <button 
              className="p-2 rounded-full hover:bg-black/10"
              onClick={() => setIsMessageOpen(!isMessageOpen)}
            >
              <ChatBubbleBottomCenterTextIcon className="h-5 w-5" />
              {folderCounts?.inbox > 0 && (
                <span className="absolute -top-1 -right-1 h-5 w-5 bg-red-500 rounded-full flex items-center justify-center text-xs font-medium text-white">
                  {folderCounts.inbox > 99 ? '99+' : folderCounts.inbox}
                </span>
              )}
            </button>
            <MessageDropdown 
              isOpen={isMessageOpen} 
              onClose={() => setIsMessageOpen(false)} 
            />
          </div>
          {/* Document/Create */}
          <button className="p-2 rounded-full hover:bg-black/10">
            <CheckCircleIcon className="h-5 w-5" />
          </button>
          {/* Selves */}
          <button className="p-2 rounded-full hover:bg-black/10">
            <ArchiveBoxIcon className="h-5 w-5" />
          </button>
          {/* Settings */}
          <button className="p-2 rounded-full hover:bg-black/10">
            <Cog6ToothIcon className="h-5 w-5" />
          </button>
        </div>

        {/* User Profile */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="relative h-8 w-8 rounded-full hover:bg-black/10 focus-visible:ring-white">
              <Avatar className="h-8 w-8">
                <AvatarImage src={undefined} alt={fullName} />
                <AvatarFallback className="bg-orange-500 text-white font-semibold text-sm">{avatarFallback}</AvatarFallback>
              </Avatar>
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-56" align="end" forceMount>
            <DropdownMenuLabel className="font-normal">
              <div className="flex flex-col space-y-1">
                <p className="text-sm font-medium leading-none">{fullName}</p>
                <p className="text-xs leading-none text-muted-foreground">
                  {user?.email}
                </p>
                <p className="text-sm font-medium text-gray-500 truncate dark:text-gray-400">
                  {displayRole}
                </p>
              </div>
            </DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuGroup>
              <DropdownMenuItem>
                Profile
              </DropdownMenuItem>
              <DropdownMenuItem>
                Billing
              </DropdownMenuItem>
              <DropdownMenuItem>
                Settings
              </DropdownMenuItem>
            </DropdownMenuGroup>
            <DropdownMenuSeparator />
            <DropdownMenuItem onSelect={(e) => e.preventDefault()}>
              <div className="flex items-center justify-between w-full">
                <Label htmlFor="theme-switcher" className="flex items-center gap-2 font-normal cursor-pointer">
                  {theme === 'light' ? <SunIcon className="h-4 w-4" /> : <MoonIcon className="h-4 w-4" />}
                  <span>{theme === 'light' ? 'Light Mode' : 'Dark Mode'}</span>
                </Label>
                <Switch
                  id="theme-switcher"
                  checked={theme === 'dark'}
                  onCheckedChange={toggleTheme}
                />
              </div>
            </DropdownMenuItem>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={logout}>
              <ArrowRightOnRectangleIcon className="mr-2 h-4 w-4" />
              <span>Log out</span>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>
    </header>
  );
};

export default AppHeader;
