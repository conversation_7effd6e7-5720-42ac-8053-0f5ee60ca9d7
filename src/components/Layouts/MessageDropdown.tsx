'use client';

import React, { useEffect, useState } from 'react';
import { format } from 'date-fns';
import { EnvelopeIcon, EnvelopeOpenIcon, StarIcon, TrashIcon, FolderIcon, PaperClipIcon } from '@heroicons/react/24/outline';
import { Badge } from '@/components/ui/badge';
import { Message } from '@/services/message.service';
import messageService from '@/services/message.service';
import Link from 'next/link';
import { Skeleton } from '@/components/ui/skeleton';

interface MessageDropdownProps {
  isOpen: boolean;
  onClose: () => void;
}

const MessageDropdown: React.FC<MessageDropdownProps> = ({ isOpen, onClose }) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [folderCounts, setFolderCounts] = useState<{ [key: string]: number }>({});
  const [currentFolder, setCurrentFolder] = useState<string>('inbox');

  const fetchMessages = async () => {
    setLoading(true);
    try {
      const result = await messageService.getMessagesByFolder(currentFolder);
      setMessages(result.messages);
    } catch (error) {
      console.error('Error fetching messages:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchFolderCounts = async () => {
    try {
      const counts = await messageService.getFolderCounts();
      // Properly convert types using unknown as intermediate
      setFolderCounts(counts as unknown as { [key: string]: number });
    } catch (error) {
      console.error('Error fetching folder counts:', error);
    }
  };

  useEffect(() => {
    if (isOpen) {
      fetchMessages();
      fetchFolderCounts();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [isOpen, currentFolder]);

  const markAsRead = async (messageId: string) => {
    try {
      // Check if service has markAsRead method
      if ('markAsRead' in messageService) {
        await (messageService as any).markAsRead(messageId);
      }
      // Update state regardless
      setMessages(prevMessages =>
        prevMessages.map(message =>
          message.id === messageId ? { ...message, isRead: true } : message
        )
      );
      fetchFolderCounts(); // Update counts after marking as read
    } catch (error) {
      console.error('Error marking message as read:', error);
    }
  };

  const toggleStar = async (messageId: string, isCurrentlyStarred: boolean) => {
    try {
      // Check if service has star/unstar methods
      if (isCurrentlyStarred && 'unstarMessage' in messageService) {
        await (messageService as any).unstarMessage(messageId);
      } else if (!isCurrentlyStarred && 'starMessage' in messageService) {
        await (messageService as any).starMessage(messageId);
      }
      // Update state regardless
      setMessages(prevMessages =>
        prevMessages.map(message =>
          message.id === messageId ? { ...message, isStarred: !isCurrentlyStarred } : message
        )
      );
      fetchFolderCounts(); // Update counts after star change
    } catch (error) {
      console.error('Error toggling star:', error);
    }
  };

  const handleTabChange = (folder: string) => {
    setCurrentFolder(folder);
  };

  if (!isOpen) return null;

  return (
    <div className="absolute right-0 mt-2 w-96 bg-white dark:bg-gray-800 rounded-md shadow-lg max-h-[500px] flex flex-col z-50">
      <div className="p-3 border-b dark:border-gray-700 flex justify-between items-center">
        <h3 className="text-lg font-medium dark:text-white">Messages</h3>
        <Link href="/messages" onClick={onClose} className="text-sm text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
          View All
        </Link>
      </div>
      
      {/* Folder Tabs */}
      <div className="flex border-b dark:border-gray-700">
        <button 
          onClick={() => handleTabChange('inbox')} 
          className={`flex-1 py-2 px-3 text-sm flex items-center justify-center relative ${currentFolder === 'inbox' ? 'bg-gray-100 dark:bg-gray-700' : ''}`}
        >
          Inbox
          {folderCounts['inbox'] > 0 && (
            <Badge variant="secondary" className="ml-2 bg-gray-200 dark:bg-gray-600">
              {folderCounts['inbox']}
            </Badge>
          )}
        </button>
        <button 
          onClick={() => handleTabChange('starred')} 
          className={`flex-1 py-2 px-3 text-sm flex items-center justify-center ${currentFolder === 'starred' ? 'bg-gray-100 dark:bg-gray-700' : ''}`}
        >
          Starred
          {folderCounts['starred'] > 0 && (
            <Badge variant="secondary" className="ml-2 bg-gray-200 dark:bg-gray-600">
              {folderCounts['starred']}
            </Badge>
          )}
        </button>
        <button 
          onClick={() => handleTabChange('sent')} 
          className={`flex-1 py-2 px-3 text-sm flex items-center justify-center ${currentFolder === 'sent' ? 'bg-gray-100 dark:bg-gray-700' : ''}`}
        >
          Sent
          {folderCounts['sent'] > 0 && (
            <Badge variant="secondary" className="ml-2 bg-gray-200 dark:bg-gray-600">
              {folderCounts['sent']}
            </Badge>
          )}
        </button>
      </div>

      {/* Messages List */}
      <div className="overflow-y-auto flex-grow">
        {loading ? (
          // Loading skeletons
          Array.from({ length: 3 }).map((_, index) => (
            <div key={index} className="p-3 border-b dark:border-gray-700 flex">
              <div className="mr-3">
                <Skeleton className="h-10 w-10 rounded-full" />
              </div>
              <div className="flex-1">
                <Skeleton className="h-4 w-3/4 mb-2" />
                <Skeleton className="h-3 w-1/2 mb-2" />
                <Skeleton className="h-3 w-5/6" />
              </div>
            </div>
          ))
        ) : messages.length > 0 ? (
          messages.map((message) => (
            <div 
              key={message.id} 
              className={`p-3 border-b dark:border-gray-700 hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer ${!message.isRead ? 'bg-blue-50 dark:bg-gray-700/60' : ''}`}
              onClick={() => {
                if (!message.isRead) {
                  markAsRead(message.id);
                }
              }}
            >
              <div className="flex justify-between items-start mb-1">
                <div className="flex items-center">
                  {message.isRead ? (
                    <EnvelopeOpenIcon className="h-5 w-5 text-gray-400 dark:text-gray-500 mr-2" />
                  ) : (
                    <EnvelopeIcon className="h-5 w-5 text-blue-500 mr-2" />
                  )}
                  <span className={`text-sm font-medium ${!message.isRead ? 'font-semibold' : ''}`}>
                    {message.from}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className="text-xs text-gray-500 dark:text-gray-400">
                    {format(new Date(message.sentAt || message.receivedAt), 'MMM d')}
                  </span>
                  <button 
                    onClick={(e) => {
                      e.stopPropagation();
                      toggleStar(message.id, message.isStarred);
                    }}
                    className="text-gray-400 hover:text-yellow-500 dark:hover:text-yellow-400"
                  >
                    <StarIcon className={`h-5 w-5 ${message.isStarred ? 'text-yellow-500 fill-current' : ''}`} />
                  </button>
                </div>
              </div>
              <div className="mb-1">
                <h4 className={`text-sm ${!message.isRead ? 'font-semibold' : ''}`}>{message.subject}</h4>
              </div>
              <div className="flex justify-between items-center">
                <p className="text-xs text-gray-500 dark:text-gray-400 line-clamp-1">{message.preview}</p>
                {message.hasAttachment && (
                  <PaperClipIcon className="h-4 w-4 text-gray-400 dark:text-gray-500" />
                )}
              </div>
            </div>
          ))
        ) : (
          <div className="p-6 text-center text-gray-500 dark:text-gray-400">
            No messages in {currentFolder}.
          </div>
        )}
      </div>
      
      {/* Actions */}
      <div className="p-3 border-t dark:border-gray-700 flex justify-between items-center">
        <Link href="/messages/compose" onClick={onClose} className="text-sm text-blue-500 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300">
          Compose New Message
        </Link>
        <div className="flex space-x-2">
          <button className="p-1 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-300">
            <FolderIcon className="h-5 w-5" />
          </button>
          <button className="p-1 text-gray-500 hover:text-red-500 dark:text-gray-400 dark:hover:text-red-400">
            <TrashIcon className="h-5 w-5" />
          </button>
        </div>
      </div>
    </div>
  );
};

export default MessageDropdown;
