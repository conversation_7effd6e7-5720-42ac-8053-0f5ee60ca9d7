'use client';

import React, { useState } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import InvitationTemplate from './InvitationTemplate';

interface TemplateEditorProps {
  initialTemplate?: {
    name: string;
    subject: string;
    recipientName: string;
    companyName: string;
    invitationLink: string;
    expiryDays: string;
    senderName: string;
    senderTitle: string;
  };
  onSave?: (template: any) => void;
}

const TemplateEditor: React.FC<TemplateEditorProps> = ({ 
  initialTemplate = {
    name: 'Supplier Invitation',
    subject: 'Invitation to join Ascension Supplier Network',
    recipientName: '[Recipient Name]',
    companyName: '[Company Name]',
    invitationLink: 'https://ascension.com/invite/sample-token',
    expiryDays: '30 days',
    senderName: 'Ascension Team',
    senderTitle: 'Procurement Manager'
  },
  onSave 
}) => {
  const [template, setTemplate] = useState(initialTemplate);
  const [activeTab, setActiveTab] = useState('edit');

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setTemplate(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSave = () => {
    if (onSave) {
      onSave(template);
    }
  };

  return (
    <div className="w-full">
      <Tabs defaultValue="edit" value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-2 mb-6">
          <TabsTrigger value="edit">Edit Template</TabsTrigger>
          <TabsTrigger value="preview">Preview</TabsTrigger>
        </TabsList>
        
        <TabsContent value="edit" className="space-y-4">
          <Card>
            <CardContent className="pt-6">
              <div className="grid gap-4">
                <div className="grid gap-2">
                  <label htmlFor="name" className="text-sm font-medium">Template Name</label>
                  <Input
                    id="name"
                    name="name"
                    value={template.name}
                    onChange={handleChange}
                    placeholder="Template Name"
                  />
                </div>
                
                <div className="grid gap-2">
                  <label htmlFor="subject" className="text-sm font-medium">Email Subject</label>
                  <Input
                    id="subject"
                    name="subject"
                    value={template.subject}
                    onChange={handleChange}
                    placeholder="Email Subject"
                  />
                </div>
                
                <div className="grid gap-2">
                  <label htmlFor="recipientName" className="text-sm font-medium">Default Recipient Name</label>
                  <Input
                    id="recipientName"
                    name="recipientName"
                    value={template.recipientName}
                    onChange={handleChange}
                    placeholder="[Recipient Name]"
                  />
                </div>
                
                <div className="grid gap-2">
                  <label htmlFor="companyName" className="text-sm font-medium">Default Company Name</label>
                  <Input
                    id="companyName"
                    name="companyName"
                    value={template.companyName}
                    onChange={handleChange}
                    placeholder="[Company Name]"
                  />
                </div>
                
                <div className="grid gap-2">
                  <label htmlFor="invitationLink" className="text-sm font-medium">Sample Invitation Link</label>
                  <Input
                    id="invitationLink"
                    name="invitationLink"
                    value={template.invitationLink}
                    onChange={handleChange}
                    placeholder="https://example.com/invite/token"
                  />
                </div>
                
                <div className="grid gap-2">
                  <label htmlFor="expiryDays" className="text-sm font-medium">Expiry Period</label>
                  <Input
                    id="expiryDays"
                    name="expiryDays"
                    value={template.expiryDays}
                    onChange={handleChange}
                    placeholder="30 days"
                  />
                </div>
                
                <div className="grid gap-2">
                  <label htmlFor="senderName" className="text-sm font-medium">Sender Name</label>
                  <Input
                    id="senderName"
                    name="senderName"
                    value={template.senderName}
                    onChange={handleChange}
                    placeholder="Sender Name"
                  />
                </div>
                
                <div className="grid gap-2">
                  <label htmlFor="senderTitle" className="text-sm font-medium">Sender Title</label>
                  <Input
                    id="senderTitle"
                    name="senderTitle"
                    value={template.senderTitle}
                    onChange={handleChange}
                    placeholder="Sender Title"
                  />
                </div>
              </div>
            </CardContent>
          </Card>
          
          <div className="flex justify-end gap-2">
            <Button variant="outline" onClick={() => setTemplate(initialTemplate)}>
              Reset
            </Button>
            <Button onClick={handleSave} className="bg-[#18546c] hover:bg-[#1a6985]">
              Save Template
            </Button>
          </div>
        </TabsContent>
        
        <TabsContent value="preview" className="space-y-4">
          <Card className="bg-white dark:bg-slate-800 shadow-sm">
            <CardContent className="p-6">
              <div className="mb-4">
                <h3 className="text-lg font-medium">Email Preview</h3>
                <p className="text-sm text-gray-500">Subject: {template.subject}</p>
              </div>
              
              <div className="border rounded-md p-4 bg-white">
                <InvitationTemplate
                  recipientName={template.recipientName}
                  companyName={template.companyName}
                  invitationLink={template.invitationLink}
                  expiryDate={template.expiryDays}
                  senderName={template.senderName}
                  senderTitle={template.senderTitle}
                />
              </div>
              
              <div className="flex justify-end mt-4">
                <Button onClick={() => setActiveTab('edit')} variant="outline">
                  Back to Edit
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
};

export default TemplateEditor;
