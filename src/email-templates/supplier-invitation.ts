interface EmailTemplate {
  subject: string;
  html: string;
  text: string;
}

export const supplierInvitationTemplate = (data: {
  invitationId: string;
  organizationName: string;
  role: string;
  expiryDate: string;
  invitationUrl: string;
  setPasswordUrl?: string;
  invitedByName?: string;
}): EmailTemplate => ({
  subject: `You've been invited to join ${data.organizationName} on Ascension`,

  html: `
    <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px; background-color: #ffffff;">
      <!-- Header -->
      <div style="text-align: center; margin-bottom: 30px; padding: 20px; background-color: #f8f9fa; border-bottom: 3px solid #18546c;">
        <h1 style="color: #18546c; margin: 0; font-size: 28px;">Invitation to Join Ascension</h1>
        <p style="color: #666; margin: 10px 0 0 0; font-size: 16px;">Supplier Network Platform</p>
      </div>

      <p style="font-size: 16px; line-height: 1.6; color: #333;">Hello,</p>

      <p style="font-size: 16px; line-height: 1.6; color: #333;">
        ${data.invitedByName ? `${data.invitedByName} has invited` : 'You have been invited'} you to join <strong>${data.organizationName}</strong> on Ascension as a <strong>${data.role}</strong>.
      </p>

      <div style="background-color: #f0f7fb; padding: 20px; border-radius: 8px; margin: 25px 0; border-left: 4px solid #18546c;">
        <h3 style="margin-top: 0; color: #18546c; font-size: 18px;">Invitation Details</h3>
        <p style="margin: 8px 0; color: #333;"><strong>Organization:</strong> ${data.organizationName}</p>
        <p style="margin: 8px 0; color: #333;"><strong>Role:</strong> ${data.role}</p>
        <p style="margin: 8px 0; color: #333;"><strong>Invitation ID:</strong> ${data.invitationId}</p>
        <p style="margin: 8px 0; color: #333;"><strong>Expires on:</strong> ${data.expiryDate}</p>
      </div>
      
      <div style="text-align: center; margin: 30px 0;">
        <a href="${data.setPasswordUrl || data.invitationUrl}"
           style="background-color: #18546c; color: white; padding: 14px 30px;
                  text-decoration: none; border-radius: 6px; font-weight: bold;
                  display: inline-block; font-size: 16px; margin-bottom: 15px;">
          Set Password
        </a>
      </div>

      <p style="font-size: 14px; color: #666; text-align: center;">
        Or copy and paste this link into your browser:<br>
        <span style="word-break: break-all; color: #18546c;">${data.setPasswordUrl || data.invitationUrl}</span>
      </p>

      <div style="margin: 30px 0; padding: 20px; background-color: #fff8e1; border-radius: 8px; border-left: 4px solid #ffc107;">
        <p style="margin: 0; color: #5d4037; line-height: 1.6;">
          <strong>Important:</strong> This invitation will expire on ${data.expiryDate}.
          If you didn't expect this invitation, you can safely ignore this email.
        </p>
      </div>

      <div style="margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 8px;">
        <h3 style="margin-top: 0; color: #18546c; font-size: 18px;">What is Ascension?</h3>
        <p style="color: #333; line-height: 1.6;">
          Ascension is a procurement platform that connects suppliers with organizations.
          As a supplier user, you'll be able to respond to inquiries, submit quotations,
          and manage orders all in one place.
        </p>
      </div>

      <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee; text-align: center;">
        <p style="color: #666; font-size: 14px;">
          &copy; ${new Date().getFullYear()} Ascension. All rights reserved.
        </p>
      </div>
    </div>
  `,
  
  text: `
    INVITATION TO JOIN ASCENSION
    ==========================

    Hello,

    ${data.invitedByName ? `${data.invitedByName} has invited` : 'You have been invited'} you to join ${data.organizationName} on Ascension as a ${data.role}.

    INVITATION DETAILS:
    - Organization: ${data.organizationName}
    - Role: ${data.role}
    - Invitation ID: ${data.invitationId}
    - Expires on: ${data.expiryDate}

    To set your password and complete your account setup, please visit:
    ${data.setPasswordUrl || data.invitationUrl}

    WHAT IS ASCENSION?
    Ascension is a procurement platform that connects suppliers with organizations.
    As a supplier user, you'll be able to respond to inquiries, submit quotations,
    and manage orders all in one place.

    IMPORTANT: This invitation will expire on ${data.expiryDate}.
    If you didn't expect this invitation, you can safely ignore this email.

    © ${new Date().getFullYear()} Ascension. All rights reserved.
  `
});
