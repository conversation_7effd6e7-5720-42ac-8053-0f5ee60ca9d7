// Test script for invitation API - ES Module version
import fetch from 'node-fetch';
import { URLSearchParams } from 'url';

// Check command line arguments
console.log('Using credentials from command line if provided, otherwise using default admin/admin');
console.log('Provide custom values: node test_invitation_api.js <your-username> <your-password> [test-email]');


// Configuration
const username = process.argv[2] || '<EMAIL>';
const password = process.argv[3] || 'ascensionadmin';
const testEmail = process.argv[4] || '<EMAIL>';
const apiUrl = 'https://dev.ascensionservices.net/api/v1';
const loginEndpoint = `${apiUrl}/auth/login`;
const invitationEndpoint = `${apiUrl}/invitations/bulk`;

async function testInvitationEmail() {
  console.log(`Testing invitation email to: ${testEmail}`);
  
  try {
    // Step 1: Login to get access token
    console.log('Logging in to get access token...');
    
    const loginResponse = await fetch(loginEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        'Accept': 'application/json'
      },
      body: new URLSearchParams({
        'grant_type': 'password',
        'username': username,
        'password': password,
        'scope': '',
        'client_id': 'string',
        'client_secret': 'string'
      })
    });
    
    if (!loginResponse.ok) {
      const errorText = await loginResponse.text();
      throw new Error(`Login failed: ${loginResponse.status} ${loginResponse.statusText} - ${errorText}`);
    }
    
    const tokenData = await loginResponse.json();
    const accessToken = tokenData.access_token;
    
    if (!accessToken) {
      throw new Error('No access token received');
    }
    
    console.log('Successfully logged in and obtained access token');
    
    // Step 2: Send invitation
    console.log('Sending test invitation...');
    
    const invitationResponse = await fetch(invitationEndpoint, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
        'Authorization': `Bearer ${accessToken}`
      },
      body: JSON.stringify({
        emails: [testEmail],
        role: 'supplier',
        message: 'This is a test invitation from the API test script.'
      })
    });
    
    if (!invitationResponse.ok) {
      const errorText = await invitationResponse.text();
      throw new Error(`Invitation failed: ${invitationResponse.status} ${invitationResponse.statusText} - ${errorText}`);
    }
    
    const invitationResult = await invitationResponse.json();
    console.log('Invitation result:', JSON.stringify(invitationResult, null, 2));
    
    console.log(`✅ Success! Invitation email sent to ${testEmail}`);
    console.log(`Successful: ${invitationResult.successful}, Failed: ${invitationResult.failed}`);
    
    return true;
  } catch (error) {
    console.error(`❌ Error: ${error.message}`);
    return false;
  }
}

// Run the test
testInvitationEmail();
