# User Role Requirements for Ascension Platform

## Current Implementation

### Frontend Role Expectations
The frontend application expects users to have one of the following roles:
- `super_admin`: For super administrators with access to all features
- `s_admin`: For supplier administrators
- `c_admin`: For company administrators

### Backend Role Implementation
Currently, the backend API may not be consistently providing the `role` field in the user data response. The API documentation shows that the `/users/me` endpoint should return a user object with a `role` field, but the actual implementation might be missing this field or using different role values.

## Current Solution
To maintain a functional user experience despite the role mismatch, the following measures have been implemented:

1. **Default Role Assignment**: If the backend API returns user data without a `role` field, the frontend assigns a default role of `c_admin`.

2. **Graceful Error Handling**: Dashboard pages now display a user-friendly error message if the user's role doesn't match the required role for that dashboard, instead of redirecting in an infinite loop.

3. **Generic Dashboard**: A generic dashboard page is shown for users with missing or unrecognized roles, displaying their account information and instructing them to contact an administrator.

## Recommendations for Backend Implementation

To fully resolve the role-based access issues, the backend API should:

1. **Consistent Role Field**: Always include the `role` field in user data responses.

2. **Standardized Role Values**: Use the exact role values expected by the frontend:
   - `super_admin` for super administrators
   - `s_admin` for supplier administrators
   - `c_admin` for company administrators

3. **Role Assignment**: Ensure all user accounts have a valid role assigned during creation or invitation acceptance.

4. **API Documentation**: Update the API documentation to explicitly list the supported role values and their meanings.

## Testing Role-Based Access

To verify that role-based access is working correctly:

1. Create test users with each role type
2. Verify that each user can access only their designated dashboard
3. Check that appropriate error messages are shown when accessing unauthorized dashboards

## Future Improvements

1. **Role-Based API Endpoints**: Consider implementing role-based API endpoint authorization on the backend
2. **Role Management UI**: Add a user interface for administrators to manage and assign roles
3. **More Granular Permissions**: Consider implementing a more granular permission system beyond the three basic roles
